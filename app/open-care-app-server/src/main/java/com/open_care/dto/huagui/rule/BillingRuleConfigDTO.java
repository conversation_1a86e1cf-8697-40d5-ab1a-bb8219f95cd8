package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.common.queryBuilder.OCQueryBuilderRuleExpression;
import com.open_care.dto.IBaseDTO;
import com.open_care.validation.huagui.RuleContentLength;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.groups.Default;

/**
 * 计费规则配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillingRuleConfigDTO implements IBaseDTO {
    
    @RuleContentLength(maxLength = 500, message = "匹配规则内容长度不能超过500个字符", groups = {Default.class})
    @OcColumn(title = "匹配规则")
    private OCQueryBuilderRuleExpression matchRule;
    
    @RuleContentLength(maxLength = 500, message = "执行动作内容长度不能超过500个字符", groups = {Default.class})
    @OcColumn(title = "执行动作")
    private OCQueryBuilderRuleExpression executeAction;
} 