package com.open_care.dto.huagui.rule;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.gson.annotations.JsonAdapter;
import com.open_care.adapter.OCDateTypeAdapter;
import com.open_care.annotation.OcColumn;
import com.open_care.common.queryBuilder.OCQueryBuilderRuleExpression;
import com.open_care.dto.BaseEntityDTO;
import com.open_care.enums.huagui.*;
import com.open_care.validation.huagui.RuleContentLength;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Date;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.Default;

/**
 * 统一规则定义DTO
 * 用于所有规则类型的数据传输
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@SuperBuilder
public class RuleDefinitionDTO extends BaseEntityDTO {
    
    // ========== 通用规则属性 ==========
    @NotBlank(message = "规则名称不能为空", groups = {Default.class, SaveRuleGroup.class})
    @Size(max = 64, message = "规则名称长度不能超过64个字符", groups = {Default.class, SaveRuleGroup.class})
    @OcColumn(title = "规则名称")
    private String ruleName;
    
    @Size(max = 1024, message = "规则描述长度不能超过1024个字符", groups = {Default.class, SaveRuleGroup.class})
    @OcColumn(title = "规则描述")
    private String ruleDescription;
    
    @NotNull(message = "规则类型不能为空", groups = {Default.class, SaveRuleGroup.class})
    @OcColumn(title = "规则类型")
    private RuleTypeEnum ruleType;
    
    @RuleContentLength(maxLength = 500, message = "规则内容长度不能超过500个字符", groups = {Default.class, SaveRuleGroup.class})
    @OcColumn(title = "规则表达式")
    private OCQueryBuilderRuleExpression ruleExpression;
    
    @Builder.Default
    @OcColumn(title = "优先级")
    private Integer priority = 0;
    
    @OcColumn(title = "生效时间")
    @JsonAdapter(OCDateTypeAdapter.class)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date effectiveTime;
    
    @OcColumn(title = "失效时间")
    @JsonAdapter(OCDateTypeAdapter.class)
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date expiryTime;
    
    @NotNull(message = "规则状态不能为空", groups = {Default.class, SaveRuleGroup.class})
    @Builder.Default
    @OcColumn(title = "规则状态")
    private RuleStatusEnum status = RuleStatusEnum.DRAFT;
    
    @NotNull(message = "适用范围不能为空", groups = {Default.class, SaveRuleGroup.class})
    @Builder.Default
    @OcColumn(title = "适用范围")
    private RuleScopeEnum scope = RuleScopeEnum.SYSTEM;

    
    // ========== 特定规则类型属性对象 ==========
    
    @Valid
    @OcColumn(title = "直付品类规则属性")
    private DirectPayCategoryRuleConfigDTO directPayCategoryConfig;
    
    @Valid
    @OcColumn(title = "直付机构规则属性")
    private DirectPayInstitutionRuleConfigDTO directPayInstitutionConfig;
    
    @Valid
    @OcColumn(title = "计费规则属性")
    private BillingRuleConfigDTO billingConfig;
    
    @Valid
    @OcColumn(title = "结算规则属性")
    private SettlementRuleConfigDTO settlementConfig;
    
    // ========== 验证分组定义 ==========
    
    /**
     * 保存规则时的验证分组
     */
    public interface SaveRuleGroup {}
    
    /**
     * 直付机构固定优先级验证分组
     */
    public interface DirectPayInstitutionFixedPriorityGroup {}
    
    /**
     * 直付机构动态用量验证分组
     */
    public interface DirectPayInstitutionDynamicVolumeGroup {}
    
    /**
     * 结算规则验证分组
     */
    public interface SettlementRuleGroup {}
} 