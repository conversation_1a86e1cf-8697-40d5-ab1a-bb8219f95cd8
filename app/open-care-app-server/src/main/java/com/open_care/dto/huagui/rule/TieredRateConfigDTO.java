package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.common.queryBuilder.OCQueryBuilderRuleExpression;
import com.open_care.dto.IBaseDTO;
import com.open_care.validation.huagui.RuleContentLength;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.groups.Default;
import java.math.BigDecimal;

/**
 * 阶梯费率配置DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class TieredRateConfigDTO implements IBaseDTO {
    
    @OcColumn(title = "规则")
    private OCQueryBuilderRuleExpression matchRule;
    
    @OcColumn(title = "基础费率")
    private OCQueryBuilderRuleExpression executeAction;

    @OcColumn(title = "规则描述（仅前端使用）")
    private String ruleDescription;
}