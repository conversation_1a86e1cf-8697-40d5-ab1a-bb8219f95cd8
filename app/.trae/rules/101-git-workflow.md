# 101-git-workflow 规则描述

## 规则来源
见 [../.cursor/rules/101-git-workflow.mdc](../.cursor/rules/101-git-workflow.mdc)

## 规则概述
该文件定义了Git提交与推送工作流的规范要求。

## 主要内容
1. **提交前操作**
   - 合并develop分支到当前分支
   - 执行clean build
   - 运行单元测试
   - 使用Cursor进行代码审核

2. **提交规范**
   - 从工作区根目录运行`git add .`
   - 检查所有变更
   - 创建规范的提交消息
   - 格式：`[提交者姓名] #[任务号|bug号] [类型: feat|fix|refactor|test|docs|chore] 简要描述`

3. **推送规范**
   - 将所有内容推送到当前分支的远程仓库

## 示例
```
# 合并develop分支
git checkout develop
git pull
git checkout feature/login
git merge develop
```