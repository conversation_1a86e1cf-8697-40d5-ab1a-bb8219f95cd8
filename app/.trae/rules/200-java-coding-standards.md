# 200-java-coding-standards 规则描述

## 规则来源
见 [../.cursor/rules/200-java-coding-standards.mdc](../.cursor/rules/200-java-coding-standards.mdc)

## 规则概述
该文件定义了Java编程规范和最佳实践。

## 主要内容
1. **命名规范**
   - 类名使用PascalCase
   - 方法和变量使用camelCase
   - 常量使用ALL_CAPS
   - 函数命名需明确表达意图

2. **编程原则**
   - 遵循SOLID、DRY、KISS和YAGNI原则
   - 遵循OWASP最佳实践
   - 代码需易于理解和维护

3. **相关规范**
   - 代码风格指南
   - 导入路径规范
   - CRUD代码规范
   - 性能指南
   - 单元测试规范

4. **函数命名示例**
   - 查询函数以findBy开头
   - 获取函数以getById开头
   - 创建函数以create开头
   - 验证函数以validate开头