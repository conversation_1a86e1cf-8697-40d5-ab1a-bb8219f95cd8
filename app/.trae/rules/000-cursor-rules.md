# 000-cursor-rules 规则描述

## 规则来源
见 [../.cursor/rules/000-cursor-rules.mdc](../.cursor/rules/000-cursor-rules.mdc)

## 规则概述
该文件定义了Cursor规则的核心结构和文件组织方式。

## 核心结构
```mdc
---
description: ACTION when TRIGGER to OUTCOME
globs: *.mdc
---

# Rule Title

## Context
- When to apply this rule
- Prerequisites or conditions

## Requirements
- Concise, actionable items
- Each requirement must be testable

## Examples
<example>
Good concise example with explanation
</example>

<example type="invalid">
Invalid concise example with explanation
</example>
```

## 文件组织
规则文件应按照编号分类组织。