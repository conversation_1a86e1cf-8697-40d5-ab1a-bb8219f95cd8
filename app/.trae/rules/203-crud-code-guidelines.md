# 203-crud-code-guidelines 规则描述

## 规则来源
见 [../.cursor/rules/203-crud-code-guidelines.mdc](../.cursor/rules/203-crud-code-guidelines.mdc)

## 规则概述
该文件定义了CRUD代码的编写规范。

## 主要内容
1. **实体定义规则**
   - 实体放置位置规范
   - 实体命名和注解要求
   - 继承关系和接口实现

2. **DTO定义规则**
   - DTO存放路径
   - 必须实现的接口
   - 注解要求

3. **Controller规则**
   - 路径规范
   - 注解要求
   - 参数处理规范
   - 业务逻辑禁止写入

4. **Service规则**
   - 服务层路径规范
   - CRUD服务与业务服务分离
   - 继承关系要求