# 项目规则说明

## 规则文件位置

请阅读项目根目录下 `.cursor/rules` 中的 `.mdc` 文件作为编码规范和开发规则。

## 规则文件格式

每个规则文件顶部都有一个 YAML 格式的元数据描述，格式如下：

```yaml
---
description: 规则的详细描述
globs: 文件匹配模式
alwaysApply: 是否总是应用
---
```

### 元数据字段说明

#### description

- **作用**: 规则的详细描述，说明规则的用途和适用场景
- **格式**: 字符串，建议使用简洁明了的描述
- **示例**: "Java 编程规范和最佳实践"

#### globs

- **作用**: 规则应用的文件匹配模式，支持通配符
- **格式**: 字符串或字符串数组，使用 glob 模式
- **常用模式**:
  - `**/*.java` - 所有 Java 文件
  - `**/*.json` - 所有 JSON 文件
  - `ui/*。json` - ui 目录下的所有 JSON 文件
  - `*.md` - 当前目录下的所有 Markdown 文件
  - `.cursor/rules/*.mdc` - .cursor/rules 目录下的所有 .mdc 文件

#### alwaysApply

- **作用**: 控制规则的应用时机
- **取值**:
  - `true`: 规则总是应用，无论文件是否被修改
  - `false`: 规则仅在 Git 工作区有相关文件修改时才应用
- **建议**:
  - 核心编码规范设置为 `true`
  - 特定场景规则设置为 `false`

## 规则文件分类

### 基础规则 (000-099)

- `000-cursor-rules.mdc`: Cursor 规则格式和核心结构

### 工作流规则 (100-199)

- `101-git-workflow.mdc`: Git 工作流程规范

### 编码规范 (200-299)

- `200-java-coding-standards.mdc`: Java 编程规范总纲
- `201-code-style-guidelines.mdc`: 代码风格指南
- `202-import-path-guidelines.mdc`: 导入路径规范
- `203-crud-code-guidelines.mdc`: CRUD 代码编写规则
- `204-process-import-guidelines.mdc`: 流程导入规范
- `205-sql-coding-guidelines.mdc`: SQL 编码规范
- `206-other-guidelines.mdc`: 其他编码指南
- `211-performance-guidelines.mdc`: 性能优化指南
- `212-unit-test-guidelines.mdc`: 单元测试规范
- `214-code-review-guidelines.mdc`: 代码审查规范
- `215-commit-message-guidelines.mdc`: 提交信息规范
- `216-api-specification-guidelines.mdc`: API 规范指南

### UI 开发规则 (300-399)

- `301-opencare-ui-generation-guidelines.mdc`: UI 生成指南
- `302-opencare-ui-requirements-guidelines.mdc`: UI 需求规范
- `304-opencare-ui-interactions-guidelines.mdc`: UI 交互规范
- `305-opencare-ui-styles-guidelines.mdc`: UI 样式规范
- `306-opencare-ui-web-component-formdata-guidelines.mdc`: Web 组件表单数据规范
- `307-opencare-ui-web-component-schemas-guidelines.mdc`: Web 组件模式规范

### 特殊规则

- `code-generation-excel.mdc`: Excel 代码生成规则
- `default.mdc`: 默认规则

## 规则应用优先级

1. 数字编号越小，优先级越高
2. `alwaysApply: true` 的规则优先于 `alwaysApply: false`
3. 更具体的 globs 模式优先于通用模式

## 规则使用建议

1. **新开发者**: 重点关注 200-299 系列的编码规范
2. **UI 开发**: 重点关注 300-399 系列的 UI 规范
3. **代码审查**: 参考 214-code-review-guidelines.mdc
4. **性能优化**: 参考 211-performance-guidelines.mdc

## 规则更新原则

- 规则文件应保持向后兼容
- 新增规则应遵循现有的编号体系
- 重大变更需要团队讨论确认
- 规则描述应清晰、具体、可执行
