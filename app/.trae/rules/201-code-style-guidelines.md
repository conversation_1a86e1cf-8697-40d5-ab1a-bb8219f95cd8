# 201-code-style-guidelines 规则描述

## 规则来源
见 [../.cursor/rules/201-code-style-guidelines.mdc](../.cursor/rules/201-code-style-guidelines.mdc)

## 规则概述
该文件定义了Java代码风格和编码规范。

## 主要内容
1. **代码风格规范**
   - 禁止使用import *
   - 使用Objects.isNull()进行null检查
   - 使用Bean Validation进行验证
   - 禁止使用魔法值
   - 使用Java 8特性(Lambda, Stream等)

2. **函数规范**
   - 函数代码不超过20行
   - 只允许一层if-else和for循环
   - 使用guard clause替代else

3. **工具类使用**
   - 推荐使用Hutool库
   - 字符串比较使用StrUtil.equals
   - 布尔比较使用BooleanUtil
   - 集合操作使用CollUtil

4. **异常处理**
   - 异常消息需清晰明了
   - 附带上下文参数