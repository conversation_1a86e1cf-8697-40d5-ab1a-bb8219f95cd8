{"id": "cd78f609-ae6a-4434-9071-237b80e98046", "className": "com.open_care.dto.third_party.UnifiedPlatformServicePackageDTO", "name": "UnifiedPlatformServicePackageDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "08fc173b-6b9d-4540-a8bb-8c9a2a02c2b7", "name": "<PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "09342e55-67ed-4395-bb1a-1c4ca9b93f7a", "name": "customerNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "0e8f7399-4663-4c3c-b33a-bcee0436b4e5", "name": "flag", "type": "java", "classType": "ServicePackageFlag", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "3686e6ad-8132-44e5-80cf-3d0c84ded4a2", "name": "premiumStandard", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "56b999a2-6196-4731-9e75-a358829485bf", "name": "belongPhone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "63635a51-17fb-4220-8fb0-4be7fa93cdf0", "name": "servicePackageDetailList", "type": "java", "classType": "UnifiedPlatformServicePackageDetailDTO", "refEntityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "774e0a41-b149-41ae-bf5e-4b548c76a61e", "name": "servicePackageBindId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "7776cc75-4599-4e11-ac89-17c660fe15ae", "name": "changeFlag", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "7f48bf48-7de8-4380-a9d4-cec4b3bcacaa", "name": "orgName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "808ea678-15b6-47e7-95d4-b2e311eb0dd0", "name": "channelName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "904af644-afa4-4f98-ba8c-9ceb35daf8cc", "name": "effDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "a65133c9-1b95-473c-ac3f-6fc41a56d809", "name": "residueCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "ad57d44f-b82f-4137-b8bc-ec8135edcff3", "name": "belongName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "aecf278a-1e24-4a5c-8a62-0668a1437e25", "name": "productPlanName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "b3ef58be-58d8-4f11-bb51-6972126d924f", "name": "appointCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "caa184c9-fd8e-433a-8827-7efdad42453f", "name": "sourceName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "d0e4ae53-83b2-4c91-9b05-6e21aa13e686", "name": "policyholderCustomerNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "d0fc82a3-eb26-4699-aa22-d49d01fc220c", "name": "svcPackageCode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "d842b1ff-9eab-447c-a96e-8af4f459564e", "name": "expDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "ddf08e5c-d6e7-49fa-ad17-406eb0618449", "name": "policyNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "e4989f99-7d9e-4d44-a106-fdfc22659b67", "name": "appointFlag", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}, {"id": "f67acc7c-181d-4da2-947d-3725bad7ef98", "name": "svcPackageName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cd78f609-ae6a-4434-9071-237b80e98046", "valid": true}]}