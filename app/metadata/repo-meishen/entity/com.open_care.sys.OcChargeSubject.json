{"id": "5ec1bbb9-84f4-41a8-8287-626c4a95e42d", "className": "com.open_care.sys.OcChargeSubject", "name": "OcChargeSubject", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0ee69a1d-9c74-49e2-8afd-7b159aaedf3b", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ec1bbb9-84f4-41a8-8287-626c4a95e42d", "valid": true}, {"id": "2228a294-7a42-407c-8261-1165bf1260df", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ec1bbb9-84f4-41a8-8287-626c4a95e42d", "valid": true}, {"id": "684b2bcd-0610-4c32-8c84-1c93486e70bf", "name": "ticketTitle", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ec1bbb9-84f4-41a8-8287-626c4a95e42d", "valid": true}, {"id": "6f6b4105-c6cc-4731-88e1-f97e536fc82f", "name": "key", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ec1bbb9-84f4-41a8-8287-626c4a95e42d", "valid": true}, {"id": "85ca140b-e8a4-4cf0-8dab-3fe5c394cce7", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ec1bbb9-84f4-41a8-8287-626c4a95e42d", "valid": true}]}