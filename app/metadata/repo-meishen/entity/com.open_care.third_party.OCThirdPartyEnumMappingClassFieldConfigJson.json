{"id": "88bfb975-a106-4d28-8f57-11b076ba5538", "className": "com.open_care.third_party.OCThirdPartyEnumMappingClassFieldConfigJson", "name": "OCThirdPartyEnumMappingClassFieldConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "002cefee-f6fd-420b-945b-d2d7bd70332a", "name": "isCollection", "title": "是否是集合", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "88bfb975-a106-4d28-8f57-11b076ba5538", "valid": true}, {"id": "12ae8345-0a34-4ee2-83d1-f26ce7aa74d0", "name": "enumCode", "title": "枚举 code", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "88bfb975-a106-4d28-8f57-11b076ba5538", "valid": true}, {"id": "280f35f1-be0d-4565-ac43-fdd77479954c", "name": "fieldName", "title": "属性名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "88bfb975-a106-4d28-8f57-11b076ba5538", "valid": true}, {"id": "7a1bac2d-6df2-4b27-8b54-86f19e9b32d3", "name": "isObject", "title": "是否是对象", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "88bfb975-a106-4d28-8f57-11b076ba5538", "valid": true}]}