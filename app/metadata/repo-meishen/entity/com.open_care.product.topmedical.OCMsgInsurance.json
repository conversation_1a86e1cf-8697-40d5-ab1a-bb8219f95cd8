{"id": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "className": "com.open_care.product.topmedical.OCMsgInsurance", "name": "OCMsgInsurance", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "035102c0-5458-415f-952e-ce12a2a1fecb", "name": "idNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "046d346c-a099-45b7-b274-ed94e06872f8", "name": "insuredName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "0a8e9ce6-ee28-4f7b-be50-2beef5ad5393", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "16681b31-06db-4ce0-a0b5-29728afffad9", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "1f9d43f4-00c4-4bb6-96ae-67fd7ea06bba", "name": "impStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "29d56df4-cc90-46e6-8238-3dbbef59dd93", "name": "grpContNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "397822db-51e5-49f2-9674-54ecfb521da3", "name": "planCode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "3c73a88f-011b-45a5-a186-117511975475", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "417ef7e4-7776-4017-9dab-1b6ef833bab7", "name": "planName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "4515754e-7df8-473d-b906-9b3ca5c6ec88", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "4bf9b6e4-8f41-453d-aca6-5b947fa701cf", "name": "agmValidate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "5b629920-33a9-42ed-aef2-cb317a42f8f1", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "63178e7b-f6ca-4805-b3b3-875711b7fa45", "name": "riskInfoJson", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "709e0ace-ebaf-4929-a7d0-b5ea58eaeb14", "name": "customerNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "7ebf533e-82c6-41c0-b1a4-4852a2765764", "name": "birthday", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "819fa908-4447-4645-a50d-6372198a1a4d", "name": "sex", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "82e8a0fb-5302-43c0-aabc-5caba686f665", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "917a145a-4c2a-48f0-aba3-bbd41528783e", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "a1ccb115-3381-4b03-b73b-3052f8e264bd", "name": "idType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "a5d75ab0-c19b-420a-a33c-c4b6c7edff58", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "a93216c7-f965-4bcf-8dba-d9e1c7597b24", "name": "agmTermdate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "ac040f5a-2213-4380-92e6-b13d687b1345", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "af7139be-2361-4191-a2c5-e3e9276fba5e", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "bdc0f85b-0bf1-471b-83bf-9185e27fee69", "name": "contNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "c5643be4-4d81-4962-918d-93cd4ea7ec96", "name": "agmVersion", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "c80c0444-ee0a-4629-909b-0272b0848e79", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "d98b010d-dce9-476f-a7a6-540ba929b30a", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}, {"id": "e235544c-6045-4617-a26c-36d978200f7b", "name": "agmStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df1f8a3e-2840-4221-9d6a-f74827fdf183", "valid": true}]}