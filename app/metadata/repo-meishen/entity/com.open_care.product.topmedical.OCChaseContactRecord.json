{"id": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "className": "com.open_care.product.topmedical.OCChaseContactRecord", "name": "OCChaseContactRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "027aaa5f-83b7-47af-abc2-02ab5e5cde42", "name": "role", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "53bd218c-00f3-4cc7-87ec-ca217eb729eb", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "57df3044-f032-4335-8100-3c3dd51842db", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "619388b0-9aa0-422b-9550-68f774df2717", "name": "chaseTask", "type": "java", "classType": "object", "refEntityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "6b1850de-93a1-4095-9fd5-fe02668dfce6", "name": "contactTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "75e4cfd0-7b9f-48e1-a7f7-36494d3c9ed2", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "7fa7a244-b21d-42fb-8481-a5dc15177f89", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "80ff30b0-9766-4a8a-8614-4906e4eecb3d", "name": "note", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "992553b2-dd64-4900-9222-b9736e341979", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "a41647c2-9992-4e48-8dfa-f48ef75b4c6e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "ac4dd489-f940-4d28-a46a-fda38d1a67d7", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "af8770d7-3931-41ab-afa9-dd6fbe9c46ff", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "b610f21d-963b-420b-bcd3-f3687f717af3", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "bde1e820-cff4-4b4c-8700-6219b0ba07f5", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "e2ec604c-5737-435a-8d8d-af826fbe4401", "name": "phone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "e32ea4f8-1821-4fee-bc9c-ef2ea7d84bc6", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "e5ff3aa1-3e4b-4d73-97ce-7389b74e79ef", "name": "isRepayment", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "fa2542bb-8698-4dde-93ad-f580753eac4d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}, {"id": "fe340d5f-d506-4eb8-97b7-4ecbcc8f6308", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "valid": true}]}