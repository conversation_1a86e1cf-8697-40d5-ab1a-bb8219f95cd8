{"id": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "className": "com.open_care.password.entity.OCPasswordPolicy", "name": "OCPasswordPolicy", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0b77b36c-f1f5-4dca-87a5-dc95ac48953a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "2db2ffb0-4922-4188-945d-9f5ea93f1c92", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "35b24e3a-58c1-4648-ba10-6deb63e20aa6", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "444eaa05-f971-45f4-b548-4a7ff1fcfa2d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "5c5f19a6-b319-4899-89c4-43cb050b285d", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "6292fd3d-b502-46d9-b7ee-e2519c2ef9c4", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "6e949496-6b9b-45e4-95e2-c9f62d105991", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "72029a34-b3bf-4449-96e7-a4a928243de4", "name": "policyName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "899affd0-9d79-46bb-bea9-d8be96811b18", "name": "min", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "a0b74a98-c72f-4fd0-a080-f7c4bf7b3dab", "name": "includeCharacters", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "a16ee2f8-13de-4ab0-b158-f6ea70864caa", "name": "forcingChangePassword", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "a1abc350-7ab0-4259-a27f-b29562a8f04d", "name": "policyType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "b1276083-57d4-49ed-8ffd-ea65b30eb28c", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "c3a792a8-f47f-43d3-86f9-041fd888edf2", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "d0c511f1-7ed4-407b-8c0a-982efbd994da", "name": "max", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "d6348bd8-3556-4646-b988-7e0fd1b10cb4", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "df06e3c1-7e1e-495b-8f54-4ffd9d3ed6f0", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "e730f02c-8ff6-4f89-b5be-ede8feeceff7", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}, {"id": "edcd4c14-6f2b-4813-932f-23b63ba98dbc", "name": "maxUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d06accb-79d9-4e97-b6fc-8344520cae4d", "valid": true}]}