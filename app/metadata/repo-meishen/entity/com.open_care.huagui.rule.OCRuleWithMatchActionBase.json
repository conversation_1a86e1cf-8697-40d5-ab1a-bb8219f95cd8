{"id": "a0d225b6-1f61-4c58-b278-23eda365ae70", "className": "com.open_care.huagui.rule.OCRuleWithMatchActionBase", "name": "OCRuleWithMatchActionBase", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "138d21b4-f106-4fdb-ad0d-d4e64dd9896a", "name": "effectiveTime", "title": "生效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "3da3f293-c55b-4eb0-b216-306ac7d28294", "name": "executeAction", "title": "执行动作", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "4f8c4ce9-b777-46eb-8641-56f5f56c0108", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "5095eaa8-3fa1-4fd6-bebf-bf910cef6c8e", "name": "expiryTime", "title": "失效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "58325924-c639-46cb-ac16-9f443ebc0fee", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "5decf6f4-f125-4970-a6ae-fa7376ccbe35", "name": "ruleType", "title": "规则类型", "type": "java", "classType": "RuleTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8d22f76ebdf5256c56b8bb2ae1cff8eb2eebb91a", "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "6dbdac9e-5d32-4c30-b7fd-363d3709127d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "7df40c07-9435-41bd-9c7a-1f9b03f0ba6e", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "816df46f-6239-428e-a2b1-b255c663f355", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "9cab029c-fd46-4257-b874-3891d3a2d074", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "9e5d8c50-ec15-4189-b180-34ee53d19dab", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "a475b19f-b23d-41f9-abf3-033f2687b29d", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "a9f933d3-61c0-48a5-b476-be46ff183cd0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "aa41da91-517f-47b2-87cd-8daadfcf8e6c", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "c34793a1-b29a-4ca5-aa3c-528f6ce025ca", "name": "ruleName", "title": "规则名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "c50d4b9a-60d8-4450-ac55-5fcafac909c8", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "e719f140-c057-42cc-a99f-aed69046ca43", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "e7453f57-1f8c-401f-bf29-e723ddbbb4a7", "name": "ruleDescription", "title": "规则描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}, {"id": "f5e859c4-ff89-4034-bb86-e614920fc134", "name": "matchRule", "title": "匹配规则", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0d225b6-1f61-4c58-b278-23eda365ae70", "valid": true}]}