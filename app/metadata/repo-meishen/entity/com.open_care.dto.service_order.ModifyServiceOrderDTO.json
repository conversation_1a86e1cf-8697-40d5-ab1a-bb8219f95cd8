{"id": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "className": "com.open_care.dto.service_order.ModifyServiceOrderDTO", "name": "ModifyServiceOrderDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "title": "服务单信息修改DTO", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0056c165-332f-4429-8bc6-7696d983e674", "name": "birthday", "title": "出生日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "031a3551-4ada-49ab-a0fd-b9d5dd938844", "name": "nonPersonalService", "title": "非本人体检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "0cdc7553-36d9-432e-9b4e-cc1deea7588d", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2d1a4417-cdd2-447d-b199-a7f6c1030211", "style": "Select", "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"reportDeliveryMethod": {"$id": "0cdc7553-36d9-432e-9b4e-cc1deea7588d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "报告递送方式", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "23d8f47b-24e4-4929-9645-e12d089e16cb", "name": "needUploadPdfReport", "title": "需电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "245acecb-46a9-49a2-8d76-52f604c3d928", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "4001fe7a-a2eb-424d-887f-c4809b30d424", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "467605ab-9b73-43d5-9f50-2bce381ae9e9", "name": "allowPostPaid", "title": "可后付费", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "49730ab7-82f0-4d49-8a03-ef9893389d61", "name": "examServiceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "97e7ed54-58b9-4b83-97a6-e842806c3094", "style": "Select", "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"examServiceType": {"$id": "49730ab7-82f0-4d49-8a03-ef9893389d61", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "服务类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4ec7c438-6ee4-4088-ae96-9f7c3a9f86db", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "5021f232-9445-4839-acb4-7a150d3cbba8", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "593732b4-7c04-488a-b1af-30478a319099", "name": "name", "title": "姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "5a476ce1-5da7-40f3-a026-2bb531e70436", "name": "postAddress", "title": "邮寄地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "5db7fa19-e8b8-4096-bebb-58f23871ccb7", "name": "serviceInformationRemarks", "title": "服务信息备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "5e682a45-4fdc-490b-bb53-362c55adf835", "name": "marriage", "title": "婚姻", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1b0456ad-98ad-4905-acb4-93722a180138", "style": "Select", "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"marriage": {"$id": "5e682a45-4fdc-490b-bb53-362c55adf835", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "婚姻", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "662590ad-ae86-4002-8910-1bb3338e48b9", "name": "enterpriseCustomerName", "title": "企业名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "872576f5-84d9-4667-890f-129d345b137d", "name": "hasHealthReport", "title": "需要健康报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "9305ccbb-14d5-436b-8a51-976e9c4fdc57", "name": "automaticallySplitHepatitisReport", "title": "自动拆分乙肝报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "9326d493-882d-44c8-8f1a-7a5d6d688e28", "name": "ageUnit", "title": "年龄单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "a646f8be-2d81-4783-bf83-169f30d34e53", "name": "serviceCode", "title": "ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "cf3125e9-3e22-4a66-9462-e9513e6486c8", "name": "mobile", "title": "手机号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "d29b2e27-dd1e-477d-ace0-43dcbf71a748", "name": "certNo", "title": "证件号码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "dcf5dcac-c7b3-41c7-b2ef-e457bfe3a911", "name": "displayedInGroupExamReport", "title": "显示在团检报告中", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "valid": true}, {"id": "e5f9c283-afb9-49c8-abd2-7af8e9a1c9ea", "name": "certType", "title": "证件类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "17dcd939-cdd9-405b-8deb-1fc7df9bfbeb", "style": "Select", "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"certType": {"$id": "e5f9c283-afb9-49c8-abd2-7af8e9a1c9ea", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "证件类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f8125cae-b716-409f-9830-effbd196c078", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "ac75bb4f-fc6f-4986-b315-a1cdea0db2de", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"sex": {"$id": "f8125cae-b716-409f-9830-effbd196c078", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "性别", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}