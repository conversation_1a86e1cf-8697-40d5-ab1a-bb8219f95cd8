{"id": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "className": "com.open_care.order.medical.OCCustomerOtherCheckList", "name": "OCCustomerOtherCheckList", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户其他检查单", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00059bbc-f519-4b8f-a1cb-d1d1b4cdfedf", "name": "orderItemsJson", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "034efddc-04d5-4895-bd6a-e38c1c4e909e", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "043f34a9-511e-47ee-9d26-23046d846868", "name": "profitLevels", "title": "权益级别", "type": "java", "classType": "object", "refEntityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "052248eb-0a85-4882-ad03-e371eda38451", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "069dd1c7-b668-4c23-9716-15ecb0e934f4", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "0c418729-36c2-4a5b-87ac-4c42d804648c", "name": "branchId", "title": "门店ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "0fbc8167-12b3-4bc0-b77b-164fd258cb7e", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "14c53211-b87a-4e89-98f8-331d53529667", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "1a889733-6b3e-42af-acaa-246202013259", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "1c28436c-3053-482e-98e6-7cdde5f2edcf", "name": "refundOrderId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "1c5b3316-ab6e-4b5c-89a7-ac1709df92e9", "name": "additionServiceOrderId", "title": "加项服务单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "1d22a06e-279e-4b40-bd68-7fb6b15044c3", "name": "orderFlag", "title": "订单标识", "type": "java", "classType": "OCOrderFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "06eb12b855e66cabf22c3772eea900e9746ca8e0", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "21a360f4-64c7-4c88-98d2-7ddf93a52125", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "242901fb-8ab9-4794-a0c6-23b4ca65bc90", "name": "cancelInfo", "title": "取消信息", "type": "java", "classType": "object", "refEntityId": "cd2543df-8799-48e5-8a37-a870977a9a19", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "2d567e7a-c4a2-4f43-9dfc-a5ae93626ecb", "name": "meeting<PERSON>ame", "title": "会议名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "2dbd2d34-a322-4938-b799-5c6fd7e597c7", "name": "orderName", "title": "订单名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "2ebed9c1-b0cc-4cac-81fa-11752fdbe3ac", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "2fb1afaa-f583-466c-b9e4-c116f09cdbe5", "name": "orderAccount", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "3121f2a6-af1e-432e-984e-4e262e08a406", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "34cde113-e623-450f-bfd0-559c3dd64855", "name": "refundFlag", "title": "退费标识", "type": "java", "classType": "OCOrderRefundFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "87fc558a08e65ed030848e89c3e183a36266bf15", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "37b0607a-fb71-4924-a925-725bdcd663d6", "name": "agreementId", "title": "协议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "3c288d6b-ee1b-4b1e-b964-0cf7fcb4771d", "name": "advanceSettleDate", "title": "预结日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "3fcc9001-1982-4a98-afdc-fc3b5cec5ef0", "name": "beneficiaries", "title": "受益人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "3fd99a33-f385-4d84-9f09-d97b3dd79f05", "name": "highAuthorityUserId", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "478e7fe4-3058-4892-8492-c6a51f087db9", "name": "needUploadPdfReport", "title": "需上传电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "49a06dcc-f633-4e2a-be12-71b1f19379c3", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "4cfdeedb-454b-4553-840c-093f5314d21d", "name": "reasonForApplication", "title": "申请原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "4eabbbfa-4ed8-4b9c-a259-d36223186d57", "name": "orderType", "title": "订单类型", "type": "java", "classType": "OrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ce7f095131e8e2446b2a9a9c184e62dfb78ef8d7", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "4eeceecb-d791-4880-b123-e7e166193be8", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "5418d40b-f0f5-4a6a-a8ef-93b1183e6cfa", "name": "orderItemGroups", "type": "java", "classType": "object", "refEntityId": "12386553-11c9-4e42-a4d2-b7c8014a8a85", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "57680c3b-883a-403c-a9ef-9e0a666bea42", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "581bf369-046c-447b-aff7-e88938f5c0d8", "name": "customerSex", "title": "客户性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "604d137e-f32b-4025-bd2e-61892e5dc574", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "60da94d2-dc12-48c4-b501-e512a412dcd3", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "62bcb61e-4e6c-45f1-b64f-edcedae7750a", "name": "replaceOrderId", "title": "替换订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "65068eb7-c05d-462d-89fe-5b1fb433cf5b", "name": "qrcode", "title": "收费二维码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "6a110ed9-a266-499a-b1a5-fde418900501", "name": "children", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "6f109c2c-685d-4668-8703-cd439d19418d", "name": "saveType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "71000d3e-4fcb-4f14-9816-ee08438a32e3", "name": "salesExpert", "title": "市场专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "73555abc-17ce-4d33-bd49-892a433373db", "name": "productPriceInfoSnaps", "title": "审核通过后的产品价格快照", "type": "java", "classType": "object", "refEntityId": "d1b2e111-7825-4378-8137-cb6297029364", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "73ba8545-fce2-488f-83a0-a94ef55b52b3", "name": "settleTime", "title": "结算日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "78c09bfc-0338-4d6a-bcd2-b4814bad3496", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "OCOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c91e35a85e7bca35cb090a6b23a9ad875e31951f", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "7c70f823-499a-4a4b-9ac0-4231fa5e6ca9", "name": "orderNo", "title": "订单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "7ce56d09-bdfb-4de3-a3ad-9b9c7b67ba82", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "7fd9b4be-c1d5-4969-8c5c-b7374ef6feb0", "name": "seller", "title": "销售方", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "86dd387c-cda0-4123-b3d0-c2ada9335d78", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "88042a5a-dcb0-467b-8a00-88680a62dffb", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "8b76aab3-9f2c-489f-954d-4f4fca4469db", "name": "print", "title": "是否已打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "8b7971ab-7511-493b-b53b-f9559a2dfe61", "name": "cancelReason", "title": "订单作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "8e16132a-4557-4fe8-ae4c-2636d1e0f52d", "name": "brandGroupId", "title": "品牌组ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "9be2582a-5334-4cc2-8be6-4b38739fd128", "name": "parent", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "9c136f37-6061-499e-989d-21433d0cf49e", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "9e5d1581-198d-4a8b-8b54-c1213147f27f", "name": "selfUse", "title": "本人使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "9fdf1afa-1abe-4b0c-a4e8-d1c1e324de2b", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "a6dcaf62-ef7e-4e06-812f-9a4dc810326f", "name": "attachmentList", "title": "附件列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "aa19e9ac-fe48-4d5d-96b8-3ef495813dd4", "name": "paymentStatus", "title": "订单支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "aa22859d-ea82-43ba-9dfb-bf146722aeba", "name": "salesStrategyId", "title": "销售策略", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "ac184e1e-8497-4807-92f3-d615ff90c3e0", "name": "customerMedicalRecord", "type": "java", "classType": "object", "refEntityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "ae2635f8-2753-4046-b017-fe5e31ddec5d", "name": "hospitalRegistrationId", "title": "挂号单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b1081882-c5be-42ff-9a0a-4def348b4aa3", "name": "orderLocation", "title": "下单地点", "type": "java", "classType": "OrderLocationEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b4354294f6b84222c01e0d2e7f1b13777edb09c7", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b2366dd7-b8e6-42de-9d84-5b782a7db245", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b2c50d01-6444-4f2d-b6db-c979ca79a2b2", "name": "serviceOrderNo", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b5684296-c02b-4691-8b24-6157d0843999", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b5bd612f-3827-4e08-bd0f-a98959ba28c5", "name": "examServiceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b6dba9c9-119a-49a0-a538-660f85e8dc67", "name": "parentOrder", "title": "父订单", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b7fccd93-3e08-44ab-837f-819b33146056", "name": "enterpriseCustomerId", "title": "企业客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72666", "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "b81509a9-0ec9-433d-85bc-1dba05f3392a", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "c29cffd9-33ca-4d1e-b5c7-fec1c594577b", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "c44cfad9-d631-47a3-96ce-57db621339d2", "name": "partyRoleName", "title": "客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "c795ec1c-8f2b-4b63-b003-6f4a54d2f30a", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "cb2ee536-7b44-436d-8826-1929c7b812e0", "name": "sourceDepartment", "title": "来源科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "cb35382d-fd37-4fa0-8b99-bce7d2f890cf", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "d2720dfc-8c91-494e-8bb3-94eb82c1326c", "name": "owner", "title": "所有人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "d921e703-fbcf-4d0c-86fc-d5e06489ab71", "name": "paySource", "title": "付费来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "dce48098-c258-441c-9738-b2c03b4550c2", "name": "orderTime", "title": "订单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "dfcf2e3d-96cb-44b7-8d78-adf3b325139d", "name": "sign", "title": "标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "e0c1890c-d1b2-4fd1-9cd3-30cc8cb3d427", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "ea3d8646-eb8d-4edc-ad75-490423ba2fdb", "name": "brandGroupName", "title": "品牌组名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "ec2e1e68-5991-4974-a316-b157617cad33", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "edf9d74b-cb14-488c-9b70-34007f57054a", "name": "channel", "title": "渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "f7938768-3955-4b8b-a394-bf7f27b341e7", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "fa539095-5763-4b66-b12f-a322e68b3ba7", "name": "rcptNo", "title": "预交金单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "fa82fa4c-6f8f-4029-9fcb-4393dfd368b7", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "fd84192b-6753-4c06-a82b-19cf28eb5006", "name": "serviceCode", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}, {"id": "fdc99825-854d-4b7a-a74c-57a025c1dcb9", "name": "orderItems", "type": "java", "classType": "object", "refEntityId": "7b028dd2-f0d3-4b62-bc28-09489c72bd32", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2cca4f8f-08af-42cf-b054-ac0c29a69e5b", "valid": true}]}