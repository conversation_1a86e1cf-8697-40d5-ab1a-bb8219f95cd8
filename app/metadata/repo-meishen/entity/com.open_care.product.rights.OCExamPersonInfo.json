{"id": "cef2ce12-5748-4c80-bcfe-df319e67c38c", "className": "com.open_care.product.rights.OCExamPersonInfo", "name": "OCExamPersonInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "3560a56f-f14b-4497-a6ae-b631b19909b6", "name": "age", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cef2ce12-5748-4c80-bcfe-df319e67c38c", "valid": true}, {"id": "4613300a-72e3-401f-960c-b784b049b0b0", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cef2ce12-5748-4c80-bcfe-df319e67c38c", "valid": true}, {"id": "54ca3294-58fb-4d96-a60a-2d387d061ce0", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cef2ce12-5748-4c80-bcfe-df319e67c38c", "valid": true}, {"id": "6670d8ec-f4dc-4b8f-9b73-9f7fcf3d79db", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cef2ce12-5748-4c80-bcfe-df319e67c38c", "valid": true}, {"id": "9f2f98d5-9d3e-455f-aa82-8070a98ca595", "name": "sex", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cef2ce12-5748-4c80-bcfe-df319e67c38c", "valid": true}, {"id": "f79eeb60-506a-4bf7-b374-b055ea5b70fb", "name": "mobile", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cef2ce12-5748-4c80-bcfe-df319e67c38c", "valid": true}]}