{"id": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "className": "com.open_care.medicalMicro.customer.OCCustomerExamItem", "name": "OCCustomerExamItem", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户检查明细记录", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "034855ab-5526-42a9-8b2d-6115e1fcafe6", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "08bc3ab3-f639-4e87-a21b-a382056bf7d6", "name": "customerExamProduct", "title": "对应产品", "type": "java", "classType": "object", "refEntityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "0b4ec95c-2156-4ae1-84bf-ad76c2922d78", "name": "legalMinValue", "title": "合法下限值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "0b52ebc5-c8d1-4627-bc82-56fbc6f900b1", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "0cf30487-4ba5-4aa9-87e9-af3fa79b0ecf", "name": "flag", "title": "标识", "type": "java", "classType": "object", "refEntityId": "23ba0726-f29b-4ef3-a4d0-b9d995a92cd6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "193ae346-2883-4391-9311-bccb67effcdc", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "1a46aa42-233d-49d7-964e-3e1b99501631", "name": "customerExamConclusions", "title": "结论词", "type": "java", "classType": "object", "refEntityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "27a1e581-1a92-4316-9283-c957d27d694b", "name": "highRisk", "title": "高危", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "27abde21-9808-4da3-b4b6-2f94f91ac7e3", "name": "abandoned", "title": "弃检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "2dc89d20-60b7-4213-a8ea-c672498c4b2a", "name": "operationInfos", "title": "单项弃检延期", "type": "java", "classType": "object", "refEntityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "31499581-e885-418c-b234-776fd0300f76", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "3e958a35-a930-40e6-9a69-3be56c273a7e", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "44c2828f-e26f-49cc-ac22-8e4b63ea0df7", "name": "postponed", "title": "延期", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "4599b1d3-433d-454e-91b9-be3206f84dd2", "name": "submitInfo", "title": "送检机构", "type": "java", "classType": "object", "refEntityId": "5652b5a3-24b2-46ee-80bc-16e2112ed377", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "45a268ff-1a35-4c3a-8c79-7e000cc23371", "name": "inspectionOrgId", "title": "外送机构ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "50868082-fc74-4602-8aed-ecd80f17a077", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "508f7ada-f399-4ba6-a672-b1a550468b63", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "57edf8a2-58c1-4d8d-a2a7-9bb570def313", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "60fda4f1-9a80-4d88-ac4f-db6685a4f6c5", "name": "customerExamItemSubmitInfo", "type": "java", "classType": "object", "refEntityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "6e6f2f19-a6d3-4dcf-a369-31428f4f3049", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "6f71c694-0356-4283-b1ba-1b612f098992", "name": "severeDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "style": "Select", "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "7056592c-83ed-4df7-b13b-9d840cc8682d", "name": "postponeDeadline", "title": "延期到期日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "745fced4-fb7e-4e03-8b71-afa495d65c9c", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "7c0e10ba-a821-4e16-b3bc-f48541d8e8d4", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "84390f91-1fbd-4d9d-a7db-22b73148d287", "name": "resultValueType", "title": "数据类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "85468a82-cc95-4b27-9dd5-cb23dc30db1d", "name": "highReferenceValue", "title": "参考值-高值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "8559b379-a574-4e19-90d6-c866410da430", "name": "valueSymbol", "title": "结果值符号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "87f8f6c1-3205-49cc-8c5a-d703337a931e", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "8d483d9a-a639-436f-8ce2-cd48ef8be253", "name": "unit", "title": "单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "8e033057-c186-4543-8f07-05e92863117f", "name": "interfaceItemName", "title": "项目接口名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "9b19d94b-6d53-49a3-ad28-db2efe2b664f", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "a087cf9a-404f-48b6-91cf-8275c18b5dd7", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "a088c83f-921f-4e85-a1cd-fb479a84d100", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "a3d55130-7e22-4e5f-8104-521ca04a8725", "name": "reference<PERSON>ange<PERSON><PERSON>nt", "title": "参考值范围描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "a743080c-37c0-4014-a3cf-0e1706d77455", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "a7680a50-9b85-4ae3-81c1-95edab6f3b15", "name": "examItemValueString", "title": "检查结果字符值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "ab137a93-1c6f-4121-8037-1687e51b194f", "name": "legalMaxValue", "title": "合法上限值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "aea22320-cbd4-483d-8c03-d5bfe3097524", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "b0abd05f-0b2b-427e-a2a0-cca9ff08141e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "b3371613-896a-4c6c-a116-0200c21dfe96", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "b5ceb761-1d22-4b20-9124-7043cc4f91d7", "name": "interfaceItemCode", "title": "项目接口编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "bcad4bf0-1ee5-4bff-b484-faf279de34d9", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "c2d027f1-f332-4b81-bf34-a25357869473", "name": "baseExamItem", "title": "基础检查项目", "type": "java", "classType": "object", "refEntityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "510212ff-59b3-495e-a4e4-3b6eb151860e", "style": "Select", "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseExamItem": {"$id": "c2d027f1-f332-4b81-bf34-a25357869473", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "基础检查项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c92a496a-441a-4931-9feb-2993847fad5a", "name": "referenceRange", "title": "参考值范围", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "d5810105-e626-41ed-9447-080c1c1aa8cd", "name": "lowReferenceValue", "title": "参考值-低值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "d873fe77-efc8-405d-a9e4-fbf31a639c51", "name": "description", "title": "检查描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "df537eca-757c-4d8f-835a-7ac914f5fdeb", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "e31717a7-1036-42bc-a49b-cd5eef348eb6", "name": "examItemValue", "title": "检查结果", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "e98282ae-830d-40b0-aeda-dbab05d5cc8e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "eccbd3b8-edd8-42d6-b7d4-c63ceddd76b1", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "ece3ea7a-d8dd-43bb-9074-66009f52a008", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "ee2644e9-418c-4255-8c46-3d6f81833beb", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "f3c9035e-e502-433e-9b4e-f65ad72f3ea9", "name": "inspectionOrgBarCode", "title": "外送机构条码号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "fc15e388-f8aa-4185-9965-85231c69bf55", "name": "inspectionOrgReportId", "title": "外送机构报告ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "fc27e452-2052-42d7-9ac7-0374603cce6b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}, {"id": "fceb7739-c0e3-49cf-b50d-18df632075c7", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "valid": true}]}