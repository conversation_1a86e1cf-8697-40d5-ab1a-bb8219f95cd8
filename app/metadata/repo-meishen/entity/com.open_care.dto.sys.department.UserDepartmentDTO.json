{"id": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "className": "com.open_care.dto.sys.department.UserDepartmentDTO", "name": "UserDepartmentDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "102ab639-6c1c-4f63-85fb-feb0f3b10a29", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "293e4fb4-2fa3-4eda-911f-988aa87afe37", "name": "parentOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "30516fcf-a7c5-42e3-8146-8745fd356fb8", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "3c84262a-6e03-4733-acca-e04eea837162", "name": "ownOrgs", "title": "所属机构", "type": "java", "classType": "object", "refEntityId": "93f9cfe6-8c49-4d51-a737-dbd7e593533a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "41a4c966-d51b-4147-9851-a67607dec83e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "46668ff5-ed9e-4a65-90b4-ffaa1ceb3456", "name": "parentDepartmentName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "4b895c48-69c4-4bf5-b158-762e23637adc", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "61f59ad0-38ff-4dc4-8cb1-67bf7966adcc", "name": "departmentNo", "title": "部门编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "72c280c5-3e53-41d4-b99d-e8f1a63614eb", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "a0a829a7-4f4b-4865-b382-477a6607794b", "name": "departmentName", "title": "部门名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "a17efb57-ab46-4706-b20a-0c32515c3418", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "b10ab778-035e-4c3d-9894-0a230da31a5d", "name": "children", "type": "java", "classType": "UserDepartmentDTO", "refEntityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "b319caa7-077d-42a3-b16a-d3c6c4065c43", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}, {"id": "e9f43a34-ef7f-40ca-9602-e257672b77c7", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94e9df6f-f54f-4ab1-83c4-89d0e358e526", "valid": true}]}