{"id": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "className": "com.open_care.product.entity.OCProductServiceProcess", "name": "OCProductServiceProcess", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0b76ca4e-46d3-475f-aadd-36627046dbf8", "name": "serviceOrg", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "0bfbfed6-58e0-44d4-8c7a-03f2f8751ea0", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "372125ee-6578-4300-9442-f98f7dcd9135", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "4def795c-7891-4965-a93c-33fb5a76a791", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "4e95efc3-f822-422b-a68c-1a504dcf3f69", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "6d8c21d8-239b-4bf1-9467-d87c99842b72", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "78bc8e85-3fec-4e60-bb0f-ede697d95d44", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "79758bf7-2347-47d9-bd3f-3206426d3537", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "821476a7-68b8-4bad-943a-0f95b4b7280f", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "8a5441eb-e946-4102-a395-14ea0106cfc4", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "aa01fbbe-f263-4a3d-85ac-15259f1fa72a", "name": "product", "title": "产品", "type": "java", "classType": "object", "refEntityId": "83b77f63-f957-46ca-a1b8-e5d556f59fcb", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "c875446c-175f-49e1-844e-7fa452eee337", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "cdcbf49b-1cb0-4ad2-aa1f-2cf0aa17cf68", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}, {"id": "d711e7ab-ea7d-4de3-baba-429f8baa8b11", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "df0f8ab8-b7a5-439d-b241-02df1bc6e891", "valid": true}]}