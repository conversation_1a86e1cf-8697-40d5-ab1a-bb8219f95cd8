{"id": "8877778e-704b-4df1-861c-1e4ed3a895b8", "className": "com.open_care.process.OCProcessInfo", "name": "OCProcessInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0351bf60-b871-4493-b78c-34243bb1c018", "name": "businessKey", "title": "业务扩展字段", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "09007e11-21fa-4844-a8c6-92bbda8aa254", "name": "taskId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "0b9fb3f0-0294-45b5-8517-3c272ff1ac23", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "15ca14f4-39dd-4272-99af-8b460cc90b51", "name": "commitTime", "title": "流程提交时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "193100a2-6ee5-4a02-a8c5-6c98e5092880", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "1941644d-6fd5-4e09-bee2-584858bb6e26", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "219eb521-4926-4001-abab-69c03588db60", "name": "entityInstId", "title": "实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "244c8c41-5581-47f6-806e-00b5e46847e2", "name": "className", "title": "关联类名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "2dc049ca-460a-47c8-a648-b3720bbe7eeb", "name": "taskHandleLogs", "title": "任务处理记录", "type": "java", "classType": "object", "refEntityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "2f02ed5f-dd92-412d-9979-6e2acf90fd5a", "name": "variables", "title": "流程变量", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "31bbb307-a6f3-4128-b912-1b1b6716439f", "name": "taskStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "40c826e7-02f6-4412-bf4c-d5c13f82cb17", "name": "startingVariables", "title": "流程开始时的变量", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "596c23b8-c8c3-4c9b-88cd-2ec58a712d50", "name": "processBusinessType", "title": "流程类型", "type": "java", "classType": "ProcessBusinessTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4de256127195d130bc32c47c46b8e352e1137882", "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "5e3fd712-e73d-4a72-8822-0344c8d11a67", "name": "lastCommitter", "title": "流程最后一次提交人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "6028fab5-a0ca-4b5b-9598-d5125cc9f6b6", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "6341d431-c3eb-453c-abe3-b29d351730cb", "name": "processInstance", "title": "流程信息", "type": "java", "classType": "object", "refEntityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "64bc79dc-edad-4c9a-8c87-cdb1612927a3", "name": "taskName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "6f8ae28b-562d-4856-904e-40f4925973b8", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "78aad473-4ff2-43ef-b511-02e4daddb23d", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "798f444f-6026-4e75-a495-5eb7ed52e83c", "name": "taskInfos", "title": "当前任务信息", "type": "java", "classType": "object", "refEntityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "7e7ef08b-4211-41d1-b276-763752be3ab8", "name": "taskTitle", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "8a719615-0584-484f-8f4c-679431316768", "name": "processInstanceId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "8ce3cc86-9f60-4d4b-a0cd-a6e368c4db22", "name": "taskType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "947f5c45-8fc3-4552-b009-98f1a3becf6a", "name": "taskDefinitionKey", "title": "任务定义key", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "bed54d85a83f765c5e66b2f57708566a", "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "9943880e-980d-480d-882f-fab674836331", "name": "commiter", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "99c730e8-0d90-44ae-9af6-722bc9cf0a3a", "name": "processNo", "title": "流程编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "9cbe697f-9431-4365-b95a-b44953681796", "name": "attachments", "title": "附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "9d13e828-4a4d-4a78-9f20-ce8f9a2e8c12", "name": "taskNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "a3ee2249-5acb-4bd8-a1b4-c0ce4924057f", "name": "lastCommitTime", "title": "流程最后一次提交时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "a7f59073-7ccf-4afd-9b91-10f4f1469d8d", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "abc3820e-c9bf-4d4f-bb85-d1c5b8d93245", "name": "committer", "title": "流程提交人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "b0f6492b-9bef-4813-96bc-f8a50d240b1a", "name": "handleResult", "title": "任务最新处理结果", "type": "java", "classType": "object", "refEntityId": "c685dd30-8acc-4a38-a5b1-8c498589f867", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "bde3b440-4c94-4947-8f37-0c7e041cc262", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "d073f89e-ac42-4240-b5d7-4f3b7ad99de7", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "db6c8720-1ec5-4bd8-8f49-9c427a48fe51", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "e46c61eb-e9c0-41f2-a0ea-3bd65eac6a3a", "name": "source", "title": "流程来源", "type": "java", "classType": "WorkOrderSourceEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1c4dfd922dae2d24e5c7384940f61def2795f507", "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "e60f35e6-7759-46b3-8b6a-545029fb3d6d", "name": "processTitle", "title": "流程标题", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "e7861fdb-f4c6-4822-9f55-3d857da78775", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "e9749029-02ed-4321-963e-f32c3cf0c569", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "f50afb1f-74d1-43ad-b7bf-061fa2389b05", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}, {"id": "fa11a494-2a14-4496-96c5-577c04e6fc25", "name": "taskTime", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "valid": true}]}