{"id": "38ec36fa-000a-4bbd-9515-23062e584ee4", "className": "com.open_care.order.OCProfitLevelJson", "name": "OCProfitLevelJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05170d20-3d14-41d1-8b2f-d6f89440dad7", "name": "name", "title": "权益级别名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "valid": true}, {"id": "23688026-5e7b-4e1d-a048-245f7d3b0f1b", "name": "matchRuleAction", "title": "满足规则后的动作（满足条件输出）", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "valid": true}, {"id": "662b21cf-0e35-4b84-bf2e-4fb792e604ff", "name": "ruleJsonLogicValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "valid": true}, {"id": "75e812de-111a-4638-8627-7bc07dc5803b", "name": "description", "title": "权益级别描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "valid": true}, {"id": "93e1ba12-98fd-463a-9d94-455b842729a7", "name": "rule", "title": "规则单元", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "valid": true}, {"id": "aef0e20d-cb82-4c6d-895a-57405fa612c5", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "valid": true}, {"id": "dcf6dd60-2d5e-49cb-b653-8e0d79730853", "name": "matchRuleActionJsonLogicValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "valid": true}]}