{"id": "0690417f-cb65-412a-9aad-cd7743c3bb60", "className": "com.open_care.dto.scheduleManagement.RemindTimeDTO", "name": "RemindTimeDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "9df426bf-7f95-4759-8f90-ceac240654a5", "name": "unit", "title": "时间单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "0690417f-cb65-412a-9aad-cd7743c3bb60", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"unit": {"$id": "9df426bf-7f95-4759-8f90-ceac240654a5", "type": "standard", "title": "时间单位", "entityName": "com.open_care.jsonschema.JsonSchemaObject"}}}}, "valid": true}, {"id": "ce858a4b-c942-4135-b229-86323d6f9787", "name": "beforeTime", "title": "提前提醒时间", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0690417f-cb65-412a-9aad-cd7743c3bb60", "valid": true}]}