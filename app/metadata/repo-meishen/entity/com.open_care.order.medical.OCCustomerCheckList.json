{"id": "67714d79-8b28-42ca-a136-7871fb48af81", "className": "com.open_care.order.medical.OCCustomerCheckList", "name": "OCCustomerCheckList", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户检查单", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05e284cd-7367-4c5d-a50c-76fa0ec27de1", "name": "settleTime", "title": "结算日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "08c821ef-0e05-4a2b-a869-f1d30a9b8e51", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "09f137a2-f7f0-4f02-8d49-56e5521340f6", "name": "print", "title": "是否已打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "11b436df-f684-4885-846f-13d52d23e096", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "13700e9d-68fa-432e-9711-108aca1b4ef1", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "13fdba10-197f-4ebc-a521-f8b249f96bae", "name": "orderName", "title": "订单名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "148d4c7a-e1dd-4de1-977f-088d9d47a2e5", "name": "orderItemGroups", "type": "java", "classType": "object", "refEntityId": "12386553-11c9-4e42-a4d2-b7c8014a8a85", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "1dd936c5-3a2b-4a23-855a-4a9e57f6b833", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "1e4488da-285d-46dc-bb9b-055fae8f11fa", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "21c0dfdb-38b1-4f10-b23a-a5f0c3366657", "name": "sourceDepartment", "title": "来源科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "23a08c65-8917-4a02-9d89-7883aa094f8f", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "29ca5a40-322b-4b24-9a41-848be7c27875", "name": "highAuthorityUserId", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "2e9d35ab-e213-477f-9852-dc4f9b63fc8a", "name": "selfUse", "title": "本人使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "32d5b9ac-2037-4509-be18-23a1ee7a192b", "name": "orderLocation", "title": "下单地点", "type": "java", "classType": "OrderLocationEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b4354294f6b84222c01e0d2e7f1b13777edb09c7", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "37e0143b-c74a-4c1d-88ce-8886fb6821cf", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "38fddc5a-6d51-409f-a680-668635125c99", "name": "beneficiaries", "title": "受益人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "3a658904-4c63-44a6-9cb4-9296a371c2f5", "name": "symptom", "title": "症状", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "3efc4edd-027d-42b7-96ad-7ec708163d57", "name": "orderTime", "title": "订单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "4e6d70d9-69aa-4acd-960a-af6686760abb", "name": "serviceCode", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "4f825c3f-0797-4888-93d1-ca6bcc7ac50f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "5326598d-b93d-4f23-89ef-e38a6a448c65", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "56980631-15c6-4789-85d7-d92764ab5c1e", "name": "needUploadPdfReport", "title": "需上传电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "586f40ab-aeaa-4535-bf1a-ae916f8c81a3", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "5a4de50b-b784-48ac-9d1d-fce09c8c1391", "name": "advanceSettleDate", "title": "预结日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "5c2fd38d-eeb0-47a2-bfad-3ad4dfd38725", "name": "seller", "title": "销售方", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "6317c868-c8cb-4d2c-be73-f099cda84b00", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "6461de90-aa68-4d17-8a0b-52f140a99e0c", "name": "orderFlag", "title": "订单标识", "type": "java", "classType": "OCOrderFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "06eb12b855e66cabf22c3772eea900e9746ca8e0", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "67ac7ca6-a39e-4930-9989-33b547cbd3ad", "name": "department", "title": "科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"department": {"$id": "67ac7ca6-a39e-4930-9989-33b547cbd3ad", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6cd82949-af32-4d10-9e7c-b63d6ecfa5b8", "name": "checkpoints", "title": "检查部位", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "6db6f8aa-3119-4448-8ff6-91ffc9deb4ce", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "6e42ca3e-7794-4f9c-a4ba-e7280f46d30c", "name": "salesExpert", "title": "市场专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "6ee08df0-effb-4190-848b-dbffc76b5fae", "name": "parentOrder", "title": "父订单", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "6f4a993b-9b48-4b0b-9b83-f26b4eb57b6f", "name": "hospitalRegistrationId", "title": "挂号单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "719546c9-c79b-4e4e-ac0e-b8ea4e739f17", "name": "cancelReason", "title": "订单作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "7498ca28-9a87-4dea-a4c3-47b820e62d2a", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "7848d838-0086-4362-b40a-d371375c002f", "name": "profitLevels", "title": "权益级别", "type": "java", "classType": "object", "refEntityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "79fe67d6-0c53-43d2-9486-58f8b31812eb", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "7c620573-53e5-4d3a-af64-e303d961aefc", "name": "owner", "title": "所有人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "7de5fd9e-bec1-4ea1-8fac-8ac825b876a0", "name": "customerSex", "title": "客户性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "8164d111-c452-4f31-8b90-f62771afc033", "name": "branchId", "title": "门店ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "883f50cc-97c6-4650-8b86-e3ce838d2dcb", "name": "orderItems", "type": "java", "classType": "object", "refEntityId": "7b028dd2-f0d3-4b62-bc28-09489c72bd32", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "88ee1601-0a6a-4b87-a6fa-a56a64f430db", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "89791545-1a4a-4fb4-92dc-f4c4325a1f2c", "name": "brandGroupName", "title": "品牌组名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "8cffc5cf-539f-496b-abf0-17a36366b51b", "name": "agreementId", "title": "协议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "8fd31d7b-fa7d-4522-8cfc-7a9d998028df", "name": "enterpriseCustomerId", "title": "企业客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72666", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "9517278d-0ac8-49d1-9e33-dcf64929271e", "name": "parent", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "96f7a03b-4300-4dca-a6e3-ede00d2bafbd", "name": "brandGroupId", "title": "品牌组ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "9cb55646-9444-4839-bd1d-c8115ceca34a", "name": "qrcode", "title": "收费二维码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "9cd46978-8514-4ff1-9c04-f86519f1559d", "name": "meeting<PERSON>ame", "title": "会议名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "a077783a-e703-428d-8790-1dd3ff73dae7", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "a2bc7d2c-f16d-4f83-8d62-0826d8d7f1ec", "name": "saveType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "a42e0626-4eb8-4b5f-9277-e093bdf9d6ba", "name": "orderAccount", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "a4a3a8c4-f2b3-4737-a184-dd3a84d35fbb", "name": "partyRoleName", "title": "客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "a522909c-816e-44a8-9d5b-122d7cc2a00a", "name": "refundFlag", "title": "退费标识", "type": "java", "classType": "OCOrderRefundFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "87fc558a08e65ed030848e89c3e183a36266bf15", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "a6cb6602-5c37-4921-bc9e-91a116728eb6", "name": "orderNo", "title": "订单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "a8934956-95c5-42c3-985c-dcff67966fba", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "ac14f603-099a-427c-8c83-524ee5e9cc88", "name": "productPriceInfoSnaps", "title": "审核通过后的产品价格快照", "type": "java", "classType": "object", "refEntityId": "d1b2e111-7825-4378-8137-cb6297029364", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "ad994445-4e6e-441f-94b9-df5ac59c759a", "name": "salesStrategyId", "title": "销售策略", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "b22e6693-f077-46d9-80f9-625cd77c34b1", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "b33965ba-5b6b-4076-a9de-a203b6721df2", "name": "reasonForApplication", "title": "申请原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "b339b2d8-d8c4-48c4-b254-c0a1003ad264", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "b7354116-cdf2-41ba-b952-d6b740a2719a", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "b7fd0789-32ec-4d3f-a10c-eea7ba14c216", "name": "paymentStatus", "title": "订单支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "b82d4160-ac00-4049-998e-7a44e5193402", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "bd710251-377e-45a7-b92e-aaa62479c0e8", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "be232e3c-0255-4c48-bda2-e0b8b2095d6e", "name": "sign", "title": "标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "c182f932-d9d3-445e-baff-544565aa83ed", "name": "customerMedicalRecord", "type": "java", "classType": "object", "refEntityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "c3735ef2-b69e-483b-9ab7-125a88fda10e", "name": "refundOrderId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "cbef3cc0-60af-47fd-aec7-3b13b056257b", "name": "channel", "title": "渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "cbf374bf-00c7-4d93-8394-5e237c187c23", "name": "attachmentList", "title": "附件列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "d4402297-7342-4155-bbb1-63bdd217d16f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "d80f179a-895f-43bc-8346-243becd62fa3", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "d8149934-f71d-4844-b2ac-ac2824029a38", "name": "examServiceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "da120ecc-1612-4c5e-bf40-bb5b636930ed", "name": "orderItemsJson", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "e4c7cef3-5554-425a-b316-4e125b026ace", "name": "auxiliaryInspectionResults", "title": "辅助检查结果", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "e6de2374-fff6-40a3-8f54-1dcd9ffe0c11", "name": "serviceOrderNo", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "e99a6468-744b-49fe-9502-fbfec3e98143", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "eb215b87-78f1-44f6-b77b-784fec675eb6", "name": "additionServiceOrderId", "title": "加项服务单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "eb6b20f9-0657-4488-a96a-1fcd8b363dea", "name": "cancelInfo", "title": "取消信息", "type": "java", "classType": "object", "refEntityId": "cd2543df-8799-48e5-8a37-a870977a9a19", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "ed069abe-626a-41a8-b4b4-401fcb9e71fb", "name": "rcptNo", "title": "预交金单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "ed5119ea-b868-431d-a5f8-9b79247619db", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f09a0322-9c06-4624-a6c0-aa610ead8285", "name": "replaceOrderId", "title": "替换订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f0e3084c-e14c-4a47-97f5-452c703ddd1a", "name": "orderType", "title": "订单类型", "type": "java", "classType": "OrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ce7f095131e8e2446b2a9a9c184e62dfb78ef8d7", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f2288c3c-0e65-4c25-8e7a-533167c0b51c", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "OCOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c91e35a85e7bca35cb090a6b23a9ad875e31951f", "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f3aade1e-227d-4d5b-b9f8-ffe025b41182", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f3c0457b-8c80-4b37-aed5-1b1a7362339a", "name": "children", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f60a2d68-846b-4762-9e52-cb4cf437b357", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f7a6e615-d507-4459-9495-476fed278780", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}, {"id": "f8619fae-2061-4f67-b37e-913f66e3a49a", "name": "paySource", "title": "付费来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67714d79-8b28-42ca-a136-7871fb48af81", "valid": true}]}