{"id": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "className": "com.open_care.dto.third_party.UnifiedPlatformCustomerDTO", "name": "UnifiedPlatformCustomerDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04fa2802-f894-44e6-8659-3cf9299dea29", "name": "voiceBoxFlag", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "0af71af8-075a-4591-b229-b0f3f6e89d0a", "name": "address", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "14a0557d-60c8-47a9-b7da-ce56474b26be", "name": "customerLabel", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "287dcbd5-3455-49fd-99f6-60e899281a3d", "name": "certType", "title": "certType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3c0c713f4a40c01d2c9a28a771ee45aafb43e943", "style": "Select", "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "properties": {"certType": {"$id": "287dcbd5-3455-49fd-99f6-60e899281a3d", "type": "standard", "title": "certType"}}}}, "valid": true}, {"id": "2f82b503-612c-4389-ab3a-f8cef675cd21", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "31253e07-926b-4689-925b-a1aa4dc1b0c0", "name": "customerNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "32fa3701-520a-49c1-8124-4a1e63fc6428", "name": "certNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "4de9a712-149e-4026-987e-51427754c82d", "name": "identity", "title": "identity", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "91706b564484367b04c6a20431ef88b086204d8c", "style": "Select", "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "properties": {"identity": {"$id": "4de9a712-149e-4026-987e-51427754c82d", "type": "standard", "title": "identity"}}}}, "valid": true}, {"id": "5e843db5-a2fb-47ac-b352-beac39616290", "name": "contactPhone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "5fefa2f6-37dd-4b62-931d-fa58ec0bad3e", "name": "customerIdentifier", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "6c03a299-77f5-4e31-b017-2caeb02329aa", "name": "birthday", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "714c5839-f05c-44ee-98d5-82101dc55b78", "name": "customerLevel", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "72de4ed3-12ef-4c58-9f49-cca847983738", "name": "<PERSON><PERSON><PERSON>", "title": "偏好管家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "7b5eb9c2-1c42-4b15-8c69-cfec8c45d12a", "name": "activateFlag", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "833c731a-efc4-45d4-a370-58a9fd5928e0", "name": "contactName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "8c45cccb-848d-4d0e-b8a1-f5e6e42ee1cd", "name": "relationUserInfoList", "type": "java", "classType": "UnifiedPlatformCustomerDTO", "refEntityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "955eb404-8c25-463a-be3a-648b5295aaf5", "name": "personOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "99a6168f-8250-403d-b028-5ea34a5882cb", "name": "sex", "title": "sex", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "style": "Select", "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "properties": {"sex": {"$id": "99a6168f-8250-403d-b028-5ea34a5882cb", "type": "standard", "title": "sex"}}}}, "valid": true}, {"id": "c5c842bd-392d-41ed-9726-527fa20b155c", "name": "phone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "d3bdfc72-70f0-4ebf-a91f-9b75d22d49f2", "name": "personalCustomerOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "e544748b-13ee-4fb1-b772-fa13c2a54cd6", "name": "addressCode", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}, {"id": "f0846cdd-4387-4842-84f9-c74d115c0f30", "name": "unifiedPlatformServicePackageDTOList", "type": "java", "classType": "UnifiedPlatformServicePackageDTO", "refEntityId": "cd78f609-ae6a-4434-9071-237b80e98046", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a6ab8fa8-c572-4f76-b071-6d6d71d8f16b", "valid": true}]}