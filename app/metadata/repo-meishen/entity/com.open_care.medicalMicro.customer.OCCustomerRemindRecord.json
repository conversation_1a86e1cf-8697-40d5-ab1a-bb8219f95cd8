{"id": "61357ed2-e69f-4715-b1d2-a736150651b0", "className": "com.open_care.medicalMicro.customer.OCCustomerRemindRecord", "name": "OCCustomerRemindRecord", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户提醒记录", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "1d850f4b-4cc7-4d08-96c9-0e32f4e05f67", "name": "beginTime", "title": "提醒开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "2a8ff3aa-4da6-471b-9c19-414d12869ec4", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "2cae1bed-cba5-4fdc-ad46-550708f37b01", "name": "remindType", "title": "消息来源", "type": "java", "classType": "object", "refEntityId": "f014c76b-755c-466e-9595-4b53cfbe678e", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "dd0c6446-9e5d-4a32-9560-9c4fadb75f80", "style": "Select", "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"remindType": {"$id": "2cae1bed-cba5-4fdc-ad46-550708f37b01", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "消息来源", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3189becd-1cb0-4305-af6f-bda96627506d", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "38f820b8-939f-4156-b0c3-45dc62a10209", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "4055a9a4-dcbd-483c-b885-649c2a6ee69c", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "45a0976f-bd40-4c99-83e6-0aae535b9acd", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "480813e1-b9c6-46f8-ab5f-67b8233e56da", "name": "remindNote", "title": "提醒话术", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "4a1fdc39-1d86-481b-b15f-b6da9420a452", "name": "is<PERSON><PERSON>", "title": "是否加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "50723663-6ff3-4fb6-8734-869d8431fd15", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "507a4a95-3d0e-4c17-a4e3-6d69921a0b6f", "name": "messageRead", "title": "已读", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "52bca81c-b5fc-4c7a-a599-c2ba4ba85a86", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "5b9bda60-9576-486d-9713-9421e5c5559c", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "6fd086aa-13ee-47ca-811d-cac629f93a48", "name": "endTime", "title": "提醒截止时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "781c63b7-57aa-4bd2-885c-9e1d978b8100", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "7cd26d23-7c73-444e-afe8-1f09cd262f60", "name": "baseResultRemindSetting", "type": "java", "classType": "object", "refEntityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "855623f1-340e-46b5-a028-ec8f17d17356", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "935bc525-a72a-43f3-b636-e218eab2e6d9", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "9a854f0e-7c4f-4230-9214-a23e0b951257", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "9d25b3a2-4adb-4767-af38-f2b99a7eed01", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "a1235063-2cd3-48f1-a348-a384be636c30", "name": "addRemindDoctor", "title": "添加提醒医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "84eee9c7-2c11-4751-94e3-8fa86258664c", "style": "Select", "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"addRemindDoctor": {"$id": "a1235063-2cd3-48f1-a348-a384be636c30", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "添加提醒医生", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a5a03f98-69ce-41cc-982a-64cd83a46ace", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "b30a9f0c-5934-43ce-9e8a-2437cbb33c17", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "b5a12930-1a8f-4ab4-8254-2c2886277ca2", "name": "customerRemindRecordDataSources", "title": "来源数据", "type": "java", "classType": "object", "refEntityId": "bdd276b2-edc0-4694-8def-c4a9d1836eb1", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "b795983f-6a7a-4052-998c-3a2e33f8bdb2", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "bd20af0d-f984-41ac-b159-f0c233195578", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "c353ce91-4a84-4e36-b53c-0bf17243d733", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "c3f245ef-beb2-4530-8d93-e1d81b95fae9", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "cea13f9a-0c56-47b4-8a15-30d4f30dd907", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "cfab8a08-1d73-44d8-b58e-b7d1b8d4b747", "name": "remindDepartment", "title": "提醒科室", "type": "java", "classType": "object", "refEntityId": "6c6fcaa1-55f5-410d-b5fb-ad2ece9240a1", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "da973ffe-91ee-4e34-bf16-d78cf63aa13v", "style": "Select", "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"remindDepartment": {"$id": "cfab8a08-1d73-44d8-b58e-b7d1b8d4b747", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "提醒科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d25eea93-db1d-46e3-96da-1366137cd065", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "da7d5ff4-0576-4288-b357-ee455f5f65f0", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "e4612f88-fa23-48ac-9317-0af660c79607", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "f0505cc5-9740-402b-9058-f94e07eafb8c", "name": "addRemindDepartment", "title": "添加提醒科室", "type": "java", "classType": "object", "refEntityId": "6c6fcaa1-55f5-410d-b5fb-ad2ece9240a1", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "da973ffe-91ee-4e34-bf16-d78cf63aa13v", "style": "Select", "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"addRemindDepartment": {"$id": "f0505cc5-9740-402b-9058-f94e07eafb8c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "添加提醒科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f0c78769-886d-4f27-bc84-d1f691431707", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}, {"id": "fb7400db-46e4-42be-b42b-8ac36f95645c", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61357ed2-e69f-4715-b1d2-a736150651b0", "valid": true}]}