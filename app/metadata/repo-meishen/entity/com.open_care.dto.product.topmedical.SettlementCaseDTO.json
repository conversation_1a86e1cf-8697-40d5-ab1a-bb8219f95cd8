{"id": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "className": "com.open_care.dto.product.topmedical.SettlementCaseDTO", "name": "SettlementCaseDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "002fedb0-97ca-4624-9dd8-73f4c4ca04cf", "name": "payDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "03d39739-977b-4c77-995c-e885a6748faf", "name": "billAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "05f3ca65-c011-44d7-871d-40eb87dcf1df", "name": "caseNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "0f7fa0f8-9134-4963-8640-449a88f01420", "name": "casePayMoney", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "203ff8a3-5b8c-405b-8bad-aae6b7a02f5c", "name": "insuredCertNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "36653c5b-6aac-4987-a3b7-3e0db5494119", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "3d88146e-aa14-4b9d-93d4-5ee5add2d999", "name": "payCurrency", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "50eae44e-73d4-426d-a0a5-005c2dabacab", "name": "payAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "611684db-6eee-4d0e-9986-cd59d1290286", "name": "failedReason", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "67d183ce-1a8a-481a-a07d-f72a73354349", "name": "insuredCertType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "7b5b1a16-0af8-4687-a9da-9f73572b4156", "name": "insuredName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "a06d70a5-73bb-4843-8b1f-305bacf82e57", "name": "servDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "a80d4684-6fd7-4c4e-bdd5-973868c4a2e1", "name": "taskNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "af05c1e6-5c3b-477d-9062-35d838ee435a", "name": "settleAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "cfc7c7fe-c829-4ec9-9247-a055caa34dbd", "name": "serialNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "ec0aab90-cfd8-4298-b279-af04a5adc9bd", "name": "ifPayByBillSourceAmount", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}, {"id": "fdae57c4-8ca5-4bc3-ad22-dad627cf8c12", "name": "settleBatchNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc56a6b-bf79-4c40-b3cd-1799f3c86fdc", "valid": true}]}