{"id": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "className": "com.open_care.dto.registration.AvailableRegistrationUnitDTO", "name": "AvailableRegistrationUnitDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04619ad2-d8a3-4358-904b-703b2dc77a2f", "name": "shiftName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "05fa6c5d-90dc-4162-82dc-406054262430", "name": "shiftId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "d8c5511c-6f51-442c-8bf2-2e41359db394", "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "0fe5ecdf-bc5b-414e-a293-434385cd8e7b", "name": "shiftStartTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "3096ffbf-adba-43b4-b40c-39a1961c6bda", "name": "capacity", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "541071f4-9b22-48e1-afee-fbb55b0fb75e", "name": "resourceId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "58802dd3-4648-42e5-ba0a-631dde82748b", "name": "departmentId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "7794b373-9054-4901-af90-feada05ab845", "name": "registrationProducts", "type": "java", "classType": "object", "refEntityId": "5d069f26-dc1f-433d-90da-bfab2945e9f0", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "aa948f89-4322-4353-9615-65141e7a3443", "name": "shiftEndTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "c2d3ed83-fd27-48d8-8863-a5fa68e3e89d", "name": "resourceName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "dd84cd11-d7a7-4de8-9e07-ce32f8c602ce", "name": "departmentName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}, {"id": "ed8b159a-29bd-4de6-8208-f8562375dae6", "name": "scheduleDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb2f60dd-0de6-43f5-97bf-8f50e26c80b4", "valid": true}]}