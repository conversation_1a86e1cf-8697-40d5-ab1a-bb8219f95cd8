{"id": "6a6d8e6f-7a16-4579-9bad-9f4844dc66fd", "className": "com.open_care.process.business.TaskBusinessInfoJsonObject", "name": "TaskBusinessInfoJsonObject", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "16814429-d0a6-4ba6-83c5-ec573eac8ecf", "name": "callCount", "title": "沟通次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a6d8e6f-7a16-4579-9bad-9f4844dc66fd", "valid": true}, {"id": "3f18935e-bf29-4b17-b14e-e23224a87625", "name": "appointmentTime", "title": "改期时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a6d8e6f-7a16-4579-9bad-9f4844dc66fd", "valid": true}, {"id": "adedd63e-66da-47d0-a3c2-71f4db547edc", "name": "callStatus", "title": "沟通状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a6d8e6f-7a16-4579-9bad-9f4844dc66fd", "valid": true}]}