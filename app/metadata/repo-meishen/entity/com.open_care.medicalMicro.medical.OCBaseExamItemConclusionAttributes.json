{"id": "21673039-e520-440e-9fd2-3fa4fff39e95", "className": "com.open_care.medicalMicro.medical.OCBaseExamItemConclusionAttributes", "name": "OCBaseExamItemConclusionAttributes", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "检查项目结论词属性", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "006d5d36-d117-4864-b9fe-f12ae9ca639e", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "0b8d7f14-c06f-4a28-bdc1-bb3c20f0d318", "name": "defaultConclusion", "title": "默认", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "24bc440c-36c1-4fb3-8e33-a6b3f009538c", "name": "useDescriptionAsTheConclusionKeyword", "title": "使用描述当结论关键字", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "29b6745a-4c88-4413-adef-2a9e333e2f4e", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "3245ddad-cf2f-483d-9f9e-dc8d4c19b7ec", "name": "baseConclusion", "title": "结论词", "type": "java", "classType": "object", "refEntityId": "33adf15e-809a-42c8-bfb7-667c1c086445", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0d92d857-e3b8-4180-9067-6d4a5999490e", "style": "Select", "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"baseConclusion": {"$id": "3245ddad-cf2f-483d-9f9e-dc8d4c19b7ec", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结论词", "required": [], "entityName": "OCBaseConclusion", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4b856806-29c3-4c0b-99db-5cd5661d82de", "name": "ageComparator", "title": "年龄比较符", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2f91e26c-879b-40f4-b462-fd8490822a43", "style": "Select", "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"ageComparator": {"$id": "4b856806-29c3-4c0b-99db-5cd5661d82de", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "年龄比较符", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4cdd4aa6-0f5c-440e-941a-738d8ee48946", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "5703bbcb-d64e-42b6-9920-71347646874e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "58f8ea95-6e0b-4dab-9287-c1bf927377c4", "name": "operation", "title": "操作符", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2f91e26c-879b-40f4-b462-fd8490822a43", "style": "Select", "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"operation": {"$id": "58f8ea95-6e0b-4dab-9287-c1bf927377c4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "操作符", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "63825239-82a3-4b22-832b-f083982e1634", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "64529616-f426-4ead-bfa5-bf1acf053dde", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "6fbfb652-dad2-4daa-bad0-1e125e19559c", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "7664721c-a51b-472f-aa48-edfc4c9d8888", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "8d8d8276-5ae7-4f8d-a61b-68671e8edf0b", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "8e964433-fc10-4b19-a1b3-1c845d65f44b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "987dea4f-9823-4292-b184-06685723550e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "9e01cb26-6264-49cc-b0be-9efad6d7d3aa", "name": "maleV<PERSON>ue", "title": "男性值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "9f37d0c6-b7ce-420d-83fd-744e216fa77e", "name": "examServiceTypes", "title": "适用服务类别", "type": "java", "classType": "object", "refEntityId": "e2793e20-5280-403c-8003-28cb1bc29135", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "5558d496-380c-4cde-bdd7-78a73d9c8fea", "style": "Select", "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"examServiceTypes": {"$id": "9f37d0c6-b7ce-420d-83fd-744e216fa77e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "适用服务类别", "required": [], "entityName": "OCBaseExamServiceType", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ad1004cf-c3e1-42ce-a873-108c6afb3da6", "name": "baseExamItem", "type": "java", "classType": "object", "refEntityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "b88b06eb-8689-4b7e-a399-253b87660bfd", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "bd7c498d-dc64-49a1-a9df-373c574c6d61", "name": "femaleAgeV<PERSON>ue", "title": "女性年龄值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "c9179e9f-8f9a-4780-b202-ebb220e12513", "name": "signContent", "title": "体征描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "cd53c67c-1efa-40a3-8a53-b723674ebed7", "name": "maleAgeValue", "title": "男性年龄值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "d42564ea-cb70-49ca-b1a8-af104ab63377", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "d54ceea4-f136-43ca-a1ff-122d8ed4e665", "name": "femaleValue", "title": "女性值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "d5fa2d89-d1ce-456e-b47b-0d4317248b84", "name": "mutexGrouping", "title": "互斥分组", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "eafa5fda-8671-40b5-9e61-53fd98ea9598", "name": "mutuallyExclusiveGroup", "title": "组内互斥分组", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}, {"id": "ece9f3ee-2d55-40c3-a3db-6d21aae17f83", "name": "severeDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "style": "Select", "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"severeDegreeLevel": {"$id": "ece9f3ee-2d55-40c3-a3db-6d21aae17f83", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "重症级别", "required": [], "entityName": "OCBaseSevereDegreeLevel", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f66c1cf5-bdee-42cb-85cc-b1ceb267ffb2", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "valid": true}]}