{"id": "e5a6d1d0-25ca-4de0-b3e4-f63186ab20e0", "className": "com.open_care.jsonObject.OCAppointmentInHospitalSamplingInfo", "name": "OCAppointmentInHospitalSamplingInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "583680ce-c37f-464d-a0b2-060a01a505ac", "name": "plannedSamplingDate", "title": "计划到院采样日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5a6d1d0-25ca-4de0-b3e4-f63186ab20e0", "valid": true}, {"id": "6f652cf5-f3ba-45e5-a540-b2c671f6b4ff", "name": "remarks", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5a6d1d0-25ca-4de0-b3e4-f63186ab20e0", "valid": true}, {"id": "86efb6b1-be78-4e5c-b855-69ebcfddefc2", "name": "isLocalCustomer", "title": "是否是本地客户", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5a6d1d0-25ca-4de0-b3e4-f63186ab20e0", "valid": true}]}