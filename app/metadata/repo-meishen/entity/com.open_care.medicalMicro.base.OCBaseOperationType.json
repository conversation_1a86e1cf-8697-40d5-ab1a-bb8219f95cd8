{"id": "63597756-e3a4-402c-9523-4759b8eb9cf9", "className": "com.open_care.medicalMicro.base.OCBaseOperationType", "name": "OCBaseOperationType", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "操作类型", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "076615d6-0c5e-4038-9890-e40e6b9d6ade", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "17eee3a2-34d3-431a-a0a5-85621ec3936d", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "227874f0-0bcc-4f44-9fc7-c79e53a53661", "name": "reverseOperationType", "title": "反向操作类型", "type": "java", "classType": "object", "refEntityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3b303773-74bc-4ee8-af99-040dbcce6798", "style": "Select", "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"reverseOperationType": {"$id": "227874f0-0bcc-4f44-9fc7-c79e53a53661", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "反向操作类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2360a193-579f-4a84-add9-7e84fe7d4568", "name": "coverPreviousOperator", "title": "覆盖前操作者", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "2d133053-33e5-4b49-8859-82714cf9a24e", "name": "multipleOperations", "title": "多次操作", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "3e9c8b69-07bf-4ecc-86eb-dec6115d2c7b", "name": "operationTypeWeight", "title": "操作权重", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "0", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["operationTypeWeight"], "properties": {"operationTypeWeight": {"$id": "3e9c8b69-07bf-4ecc-86eb-dec6115d2c7b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "操作权重", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "43e29f5d-bf7d-40e2-b381-c82abe0065f9", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "4c3d68ed-115a-414c-bca0-aba483c25a61", "name": "cancelOperationType", "title": "取消操作", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "5720e651-5db0-4bc6-9e53-da33cc159ded", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "5cb141a1-1a32-45ff-adf4-d1e9f577ae75", "name": "preOperationTypes", "title": "前置操作", "type": "java", "classType": "object", "refEntityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "c69a5bbf-fac7-4a47-940a-1d841add43bd", "style": "Select", "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"preOperationTypeList": {"$id": "5cb141a1-1a32-45ff-adf4-d1e9f577ae75", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "前置操作", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "634cebc9-6798-492e-9ca2-bf05402688bf", "name": "mutuallyExclusiveOperationTypes", "title": "互斥操作类型", "type": "java", "classType": "object", "refEntityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "62608746-2fdd-4b1c-8785-5ad9fcbc0735", "style": "Select", "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"mutuallyExclusiveOperationTypeList": {"$id": "634cebc9-6798-492e-9ca2-bf05402688bf", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "互斥操作类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6824a7e1-bd5d-4977-ba9e-156794a0511c", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "6a4b3463-2b8f-4674-a5a0-e7d4021c61e5", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "6cda5e56-0ff0-4ad9-b0bc-725fbfb642ca", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "6cda5e56-0ff0-4ad9-b0bc-725fbfb642ca", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "74543790-21b2-4924-9078-138a00aa76f5", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "7707d9dc-59fd-41a3-b9d2-0e83a2bf2e07", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "891320ca-8e9c-4596-83c0-1d82fce8e2d8", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "90578bce-2ba3-468b-b580-5c833d92b197", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "afd4a12c-597f-4b6d-afd2-173e415b33e1", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "b6a66b9f-bf00-4e42-9348-e2ca5dc779f7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "bb1adfce-abd2-428d-914e-3a0114efdf88", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "bb1adfce-abd2-428d-914e-3a0114efdf88", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "da1aeecf-db14-4a75-b29d-caf7ce32c608", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "e4f24719-25c9-45f6-98aa-1d511e2ee746", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "e5292684-c85a-4eab-95c1-e1e333615f82", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "e92128d7-e875-4be0-8c9c-43189cefabea", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}, {"id": "f77ea7eb-d693-4531-b5e2-e67ed91748f0", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "valid": true}]}