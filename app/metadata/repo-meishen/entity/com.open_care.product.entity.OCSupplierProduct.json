{"id": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "className": "com.open_care.product.entity.OCSupplierProduct", "name": "OCSupplierProduct", "standard": true, "authority": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02f7d374-aac4-4630-8d2d-55f8b2390985", "name": "explanation", "title": "服务项目释义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "044be918-3a86-4d0b-a678-923ac0238037", "name": "content", "title": "供应商服务项目内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "0508df87-f178-40c0-81bc-0cf95b6e5f39", "name": "comments", "title": "提交备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "2db521f7-f9a1-4ddd-b3ce-d83c69f38789", "name": "name", "title": "供应商服务项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "3393f1ac-07e4-4517-946f-0d7a8dcb19e3", "name": "h5Url", "title": "供应商服务项目H5页面Url", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "35e78a39-623d-499b-9d34-180cd59e5cf7", "name": "deliveryMethod", "title": "服务提供方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "3bf10a5b-8b8f-4ba1-90ef-9a1c111ecbbc", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "49be8b91-daaf-4272-bd1c-fcab3d724026", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "49e89770-e81b-431d-9910-53ad606ffa9b", "name": "invalidReason", "title": "无效原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "4ce7132f-4bf2-4228-879b-e607c669b8da", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "4ee6b4b1-ee6b-4df9-ae09-d1f178a0ab92", "name": "materialInfo", "title": "物料信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "5767d226-3420-49ea-8e90-21dbb4d368d9", "name": "material", "title": "是否有物料", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "59dea4e1-d0fd-494d-a26b-c9ef1d0002b3", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "5ae0ff0b-05d1-44b4-8983-ce0526e9027f", "name": "drafts", "title": "草稿箱", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "642324bb-7297-48a3-9db8-602b064b7bab", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "6cf2e420-908b-424b-af84-03927f3b2f6b", "name": "lastSaveDate", "title": "最后一次保存时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "6dd9f16a-2090-46a6-9fda-bfdf10399b07", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "7051946e-f83b-45c1-bff5-210962ca7741", "name": "fromDate", "title": "开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "7e5d4ba8-68be-4750-bb9e-9f28c519a4a0", "name": "internalRemark", "title": "对内备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "829c0f7e-6e22-4dd8-a2b1-84a854163c95", "name": "product", "title": "产品", "type": "java", "classType": "object", "refEntityId": "83b77f63-f957-46ca-a1b8-e5d556f59fcb", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "85f5d05c-5b69-4ca7-bc56-77894e56d3b7", "name": "currentAuditStatus", "title": "当前审批状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "984997b2-c6ae-48a5-a75c-a093c9d2ab85", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "99d20399-0d93-40d7-bb16-e8631e23c923", "name": "draftsOperator", "title": "草稿箱操作人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "a2218b15-4d40-4bff-8e74-020f385c7af7", "name": "landingForm", "title": "落地形式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "a25d63f0-6046-42e0-b184-217983be697b", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "a8e866dd-f28b-47da-87b8-564a632c4e17", "name": "externalRemark", "title": "对外备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "aab64d16-9277-4bc9-ac28-ec28bd9d371d", "name": "preStageId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "bbfc9626-7419-40e8-86dd-f86c236161fa", "name": "currentAuditBusiness", "title": "当前审核业务", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "bd6b1f60-77e0-454f-bfc5-698cd548bf70", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "c00c8046-ac6c-44ba-b806-7618042302d3", "name": "productNo", "title": "供应商服务项目编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "c29c00e5-9ded-4f78-9d04-6f4dabaa8da8", "name": "thruDate", "title": "结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "c627b834-db53-4978-b333-042e0b806b93", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "c7fa063d-c91f-4c36-9f62-9d10c8deb424", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "cf872639-88f0-46c3-9688-fb95de1d8957", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "d0417805-544d-42cf-877c-91954d3fd106", "name": "enable", "title": "有效", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "e3ac48d2-9b68-4a89-9127-88c3313bf456", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "f117290c-d9b4-4d1c-bf19-c6854723aaff", "name": "supplier", "title": "供应商", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}, {"id": "f374d5f7-c307-49fe-9cef-9464bdcd9648", "name": "contract", "title": "合同ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b567b2a0-a78c-4f62-8ca5-89ca3987bbf2", "valid": true}]}