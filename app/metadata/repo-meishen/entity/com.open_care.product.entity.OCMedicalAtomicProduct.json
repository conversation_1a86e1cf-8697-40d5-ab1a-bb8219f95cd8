{"id": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "className": "com.open_care.product.entity.OCMedicalAtomicProduct", "name": "OCMedicalAtomicProduct", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "医疗明细项目", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "03638eec-6fff-44d3-9de9-32b9aa3ee7f8", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "0991c191-2ea6-433e-93b4-b2b1ea0e5758", "name": "productTypeEnum", "title": "产品类别", "type": "java", "classType": "ProductTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a412e1fa5e9b5c0522b9b505403365f9953fdd16", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "111df278-6d1c-4773-975b-27707a4125f8", "name": "otherSysCode", "title": "其他系统编码", "type": "java", "classType": "object", "refEntityId": "84b0cdeb-49ef-4965-afa0-3825b0a947b6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "11b147e1-2b4d-48c3-b678-ddbda1bb211b", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCProductAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b46b46e30ec540dffb8a1d2910673c202c96e0a2", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "135c3be3-ab95-4fae-aeec-1315a459740c", "name": "productNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "15701ffa-766f-4628-86f6-d389b0978e13", "name": "mealType", "title": "餐前餐后项目", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "e93ba801-7caf-4a95-b02e-06e9d4fc82dd", "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["mealType"], "properties": {"mealType": {"$id": "15701ffa-766f-4628-86f6-d389b0978e13", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "餐前餐后项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "15e4935d-45a8-4291-bb83-521442e5e4b0", "name": "examPart", "title": "检查部位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "2070868b-abbd-4676-96cd-b3cdf3d66237", "name": "processTemplateKey", "title": "流程模板", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1e55cb9d-560c-41c1-81a5-4fc879478bbb", "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["processTemplateKey"], "properties": {"processTemplateKey": {"$id": "2070868b-abbd-4676-96cd-b3cdf3d66237", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "流程模板", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "20b850cd-a781-40d0-83f9-419e20e53e06", "name": "invoicePrintCategory", "title": "打印发票时产品类别", "type": "java", "classType": "object", "refEntityId": "dcc64fe7-6a12-44a3-b254-7add8cbc9e55", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "7f83053d-54a9-4eed-8e54-7c395325fb9e", "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"invoicePrintCategory": {"$id": "20b850cd-a781-40d0-83f9-419e20e53e06", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "打印发票时产品类别", "required": [], "entityName": "OCInvoicePrintCategory", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2879fba8-50ed-42ff-9e40-13ac1b26d410", "name": "interfaceType", "title": "接口类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "6e1c3a98-4cd4-4ae0-90a3-c0724a70beeb", "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"interfaceType": {"$id": "2879fba8-50ed-42ff-9e40-13ac1b26d410", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "接口类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2bb293a0-bd63-4771-af23-63d1b095f5a6", "name": "phonetic", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "2ec27ad0-160e-4106-b699-ae1731a951fe", "name": "separateReport", "title": "单独出具报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "31098a01-1251-4434-9de8-0d6729b3a9ce", "name": "serviceInfo", "title": "服务信息", "type": "java", "classType": "object", "refEntityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "31b72476-59fc-4f3e-b545-587b12845722", "name": "tags", "title": "标签", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "3497e7a4-1195-4c10-a8f6-a4b4f3835b85", "name": "medicalProductType", "title": "项目类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "34bb96d2-24cf-40b5-98f9-c592152e58f3", "name": "examPartDirection", "title": "检查方位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "38b38a9f-10c2-41b1-a8b7-e19558070523", "name": "appointmentInfo", "title": "预约信息", "type": "java", "classType": "object", "refEntityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "3d8bba31-4468-4cb6-99ae-f30f79bf0627", "name": "brand", "type": "java", "classType": "object", "refEntityId": "f9317ca8-02a7-4f69-8069-f4f0e61e6ef2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "428f08d9-3218-4789-8609-f0fe1cba08db", "name": "inspection", "title": "是否外送", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "460a4e59-bf3e-4db6-a669-08feb4e89d5d", "name": "medicalProductComponents", "type": "java", "classType": "object", "refEntityId": "f227f42a-798e-404d-8072-ea04719c638b", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "487f7692-4c6f-4ee9-8e54-9b8fb1ab8932", "name": "serviceResources", "title": "服务资源", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "48c45853-e25c-4329-9640-7bef0d08d1e3", "name": "productSop", "title": "产品SOP", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "4d329ec6-e4b9-4845-a7b1-60da9a889d87", "name": "productIndicatorCategory", "title": "所属健康指示灯类别", "type": "java", "classType": "object", "refEntityId": "d5e1113b-eb2e-479a-af67-1f111379b258", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "56a61dcb-06ff-4ace-b2d4-907c2607e4ce", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "4fa0064c-a2bc-48be-86bd-5a3d2bd24478", "name": "appointmentPageInfo", "title": "预约页面信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "51163eee-e68d-4922-899b-78f7b2e91b2a", "name": "productReportUnit", "title": "所属报告单元", "type": "java", "classType": "object", "refEntityId": "1d0847f3-9bf4-43eb-a8b9-17f2ae85c6cd", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c112906c-e6ab-40ad-8a67-1084093522c4", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "51f024a8-0528-4ad5-b733-dfa44cebb758", "name": "timeoutInfo", "title": "超时提醒", "type": "java", "classType": "object", "refEntityId": "bcdf5a27-ff91-4b72-8618-506ab447bfb8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "5237b594-ee2e-4c48-8ebf-0fdb2101838c", "name": "seqno", "title": "产品行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "52f70e4c-c8ae-40ee-a53c-ea14a7fc5cc2", "name": "sampleType", "title": "标本类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "54fb6db0-dcc6-4e69-a49c-012addf60010", "name": "productCategory", "title": "产品分类", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "db0f67be-41e7-4763-b834-849a309dcd4f", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["productCategory"], "properties": {"productCategory": {"$id": "54fb6db0-dcc6-4e69-a49c-012addf60010", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品分类", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "5655342d-30ab-4f8e-ac3b-533df0b9111a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "567cf29d-477b-4a07-b64d-ca890b301554", "name": "processDefinitionKey", "title": "流程定义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "*************-4e60-9f16-3f4670b3bd5b", "name": "showOnGuide", "title": "在导检单上显示", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "5a0ec603-ac93-4bf7-b096-40c390752bed", "name": "disabled", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "5b91c3ef-4e39-4811-99b7-2a56322b66a5", "name": "organizationProducts", "title": "不同机构的编码", "type": "java", "classType": "object", "refEntityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "5bf379d5-fa26-47e9-9524-726533e971b1", "name": "thruDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "5c485a18-affb-4c74-8120-046f40ac5b7d", "name": "examOrgs", "title": "送检机构", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "5def58e9-cafa-46da-aa8b-70cdc417ac18", "name": "productComposition", "title": "产品组合", "type": "java", "classType": "OCProductCompositionEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "fb77728c82b4976215813d01658d30647905b917", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "6723f8f9-0c07-4b76-b2d2-1e8e17e67b41", "name": "productUpOrDownEnum", "title": "上下架状态", "type": "java", "classType": "OCProductUpOrDownEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "5783903a3025d203419af1adb17c5b58e2779555", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "67f048c2-a0cd-48ae-ac2e-17aa17cb28d7", "name": "status", "type": "java", "classType": "OCProductStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "7f4e41d9bf4d856872e61acab81b030d08583b05", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "6a499002-1d5b-4be8-bd56-1f5f427ba38c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "6e68f211-3dbc-4e93-a857-b3dc65dba591", "name": "englishName", "title": "英文名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "75e33a81-8569-4cff-a064-d35162cc6897", "name": "productAttributes", "type": "java", "classType": "object", "refEntityId": "05ad013f-9028-48e7-b064-e72934c69b35", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "7778874f-a3d6-4f45-bf39-d96770dcbb62", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "78c99af3-70b8-4e50-b768-6df4c77b3706", "name": "attributeSets", "type": "java", "classType": "object", "refEntityId": "41ca4d69-933e-416d-b1ec-5f500a801a7a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "7cf2f59f-1dad-47db-92dc-b4d73da101be", "name": "pacs", "title": "是否对接PACS", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "7edd1b0c-d5c8-4669-9a04-77315d72359e", "name": "productInspections", "title": "送检机构", "type": "java", "classType": "object", "refEntityId": "478c0a7a-06d3-4395-ab69-e8783515802a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "823de8d7-2654-4764-b71f-2f59ebbccbb9", "name": "productName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "89fcfd3f-0073-41bd-b0ce-899fa0c51ea5", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "90e680cd-c98f-4cb7-8573-0937d9b07b9b", "name": "departments", "title": "检查科室", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "da973ffe-91ee-4e34-bf16-d78cf63aa12b", "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["departments"], "properties": {"departments": {"$id": "90e680cd-c98f-4cb7-8573-0937d9b07b9b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "检查科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9927aa0d-12b8-47f9-ab5b-01f9bc6fc4a5", "name": "preStageId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "9ea8f491-1c25-4d6c-b3bf-da092f330677", "name": "showWorkbench", "title": "管家工作台显示", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "9fd1744e-7eec-4bc0-bb01-4d295fbcd2ee", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "a54e9b0f-e5dd-4e63-a359-d531eb6a77e3", "name": "productMedicalName", "title": "产品医疗名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "a6577297-145b-4bfc-9077-3d3fe87752f4", "name": "serviceDuration", "title": "服务时长", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "a6763347-078c-40a2-ac9f-04be13fe0579", "name": "dispatchAndAcceptanceRule", "title": "供应商接派单规则", "type": "java", "classType": "object", "refEntityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "a975b644-a589-42d8-b692-9cc6f5c6f3b7", "name": "meal", "title": "就餐项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "ada3e53a-9dce-4eff-922c-c20397fc2f30", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "aee4ed02-47db-4cc3-875b-7cb5095e0fe8", "name": "agreeTemplate", "title": "同意书模板", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "afa930d4-e3a8-4ee1-b324-222366485111", "name": "purchaseTips", "title": "购买提示", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "b036479b-2b3e-4d4e-8e17-607f7a3bcf2a", "name": "description", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "b40ff3fc-b9fb-4230-be83-49cedd1ee035", "name": "fromDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "b4f3688c-87f4-412c-82ba-bc6382331614", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "b53413b7-8b2e-40fa-971c-4a2cd2fe67c7", "name": "hepatitisProductType", "title": "乙肝项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "b81d8d09-c70f-4622-a01d-64171f5ced20", "name": "abbreviation", "title": "英文简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "ba3559ef-04a4-4716-b62a-31fce8447427", "name": "bloodProject", "title": "血液项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "be68f630-3ba6-4858-a3ec-87ec5170d418", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "c7e3d4b1-669a-4e2a-bfb8-291f6b767c2c", "name": "barcode", "title": "是否配置条码", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "cfcea5b1-a5d7-413e-9eb7-670d9d062090", "name": "manager", "title": "产品负责人id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "d85e5a65-0e8a-4dd7-b24f-1328227eac72", "name": "canSell", "title": "是否可销售", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "dcfad5e3-92f1-4024-a9cd-3eaf7d15204a", "name": "productType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f415ae72-8dd8-4e80-8daf-34e63ecc2912", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "de38fe3f-e48b-4546-8bb1-1f5e98aaaf9c", "name": "<PERSON><PERSON><PERSON>", "title": "产品负责人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "e1302fb6-b658-425e-b00e-46489cad487b", "name": "comments", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "e2ec456c-c601-4a04-9b07-916713a8c8fb", "name": "saleInfo", "title": "销售信息", "type": "java", "classType": "object", "refEntityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "e3aca621-5b62-4efd-8912-6ceec4860287", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "e44f3469-1eff-4c3f-8109-1fc2a672bd11", "name": "mallInfo", "title": "商城信息", "type": "java", "classType": "object", "refEntityId": "ac78171b-b49e-4a2a-9739-a7deb58152d8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "e6175ede-79c1-ef1a-7fba-149d0facff7e", "name": "productGuideGroup", "title": "产品导检单分组", "type": "java", "classType": "object", "refEntityId": "cdcc0a88-0f8c-dda6-dd86-081ebaa2cece", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3fd0c9c0-009d-4333-8f94-30a2c4968907", "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"productGuideGroup": {"$id": "e6175ede-79c1-ef1a-7fba-149d0facff7e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品导检单分组", "required": [], "entityName": "导检单分组", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ece53dd2-30a5-4493-a827-f9cb557f7359", "name": "checkType", "title": "检查类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f0b8c546-5ee5-428e-8221-7830d4d04944", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "f03a3fad-e3b9-4d54-83fb-729ffaf61f5c", "name": "printCategoryFlag", "title": "打印时类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f9fc0b3b-aaec-4602-808a-03c6dced4688", "style": "Select", "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"printCategoryFlag": {"$id": "f03a3fad-e3b9-4d54-83fb-729ffaf61f5c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "打印时类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f0d34f33-e235-4bbc-a247-35ffa011d202", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "f185a26c-2836-4b60-aea9-64765ad7dbf2", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}, {"id": "fa45d7c6-0f9c-4dd7-86e1-bc704681b052", "name": "urine", "title": "需憋尿", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0ffb1b21-9b7e-46bf-826f-102fd8cd1119", "valid": true}]}