{"id": "19ff55d0-0986-4bd5-aa51-565cc1222869", "className": "com.open_care.product.supplier.OCDimensionQuotationDetail", "name": "OCDimensionQuotationDetail", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "028a8697-31e9-4fb6-83d0-26fd801f8593", "name": "maxQuantity", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "92bb230e-37d9-4a4c-938a-4c5bb0c3b94d", "name": "minQuantity", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "b1213492-a74d-4be7-a8d0-76d95ba5a93f", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "c31d3f8e-37a0-4cb7-98a3-91b96ddfc7ad", "name": "quantityType", "title": "数量类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8dc12692-f5c3-43e7-88b3-c65bef7b7c0b", "style": "Input", "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"quantityType": {"$id": "c31d3f8e-37a0-4cb7-98a3-91b96ddfc7ad", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "数量类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c65f2026-d5d3-4965-ab71-d5547963f578", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "c6872d22-b7fd-4572-b809-2c5a9c78f434", "name": "amountType", "title": "金额类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8dc12692-f5c3-43e7-88b3-c65bef7b7c0b", "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"amountType": {"$id": "c6872d22-b7fd-4572-b809-2c5a9c78f434", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "金额类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "cda2c547-eba0-4707-92ab-55c2f1b433f4", "name": "_entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "dba7bf11-51a0-4a8b-996c-0cfc196dcf86", "name": "maxAmount", "type": "java", "classType": "Double", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "dfb10d6e-4a8f-4fc6-9716-e1abb3a31d72", "name": "amount", "type": "java", "classType": "Double", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "f001060e-179d-4b59-b7b1-9a7cc9bdef35", "name": "minAmount", "type": "java", "classType": "Double", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}, {"id": "fe5da578-c1f3-45a5-b1c6-64d807fc45d1", "name": "quantity", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19ff55d0-0986-4bd5-aa51-565cc1222869", "valid": true}]}