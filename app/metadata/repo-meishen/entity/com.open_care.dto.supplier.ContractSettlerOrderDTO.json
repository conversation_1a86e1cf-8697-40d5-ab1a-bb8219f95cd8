{"id": "9f0e039a-75c6-4041-827f-aad385f04768", "className": "com.open_care.dto.supplier.ContractSettlerOrderDTO", "name": "ContractSettlerOrderDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05e50dfd-1920-4886-9a51-dd3cef4ade52", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "0a60146b-43c6-42d6-90e6-393077136fa3", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "161ef655-6df5-407c-a403-dfa73c1b4f29", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "19f89780-89d9-4982-b3c4-7829e59cfd99", "name": "quotation<PERSON><PERSON><PERSON>", "title": "报价方式", "type": "java", "classType": "QuotationMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c89487c290c2503e8d1e6490168fdb2e60b513e2", "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "1f8b520a-e628-4f0b-baac-20ff1a5f5d42", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "2ea66b60-0583-4f90-9e34-58861640c2a4", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "7292f7df-dcc5-4781-81a6-8471e2648d92", "name": "contractSettleInfos", "title": "合同结算信息", "type": "java", "classType": "object", "refEntityId": "d22cbbb7-b042-4d71-94ed-11af5346c2b3", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "8b034ffc-b37f-49f1-a9c3-3402c51d4809", "name": "settlementDiscountRemarks", "title": "结算折扣备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "8e67914d-2c4c-4748-85bd-434abf3054fe", "name": "settleMethod", "title": "结算方式", "type": "java", "classType": "SettleMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b98f54cbe534f48d7f3f996caf6543a622823faa", "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "943ed50e-14a0-482a-8b95-cb8ef729ce76", "name": "settleType", "title": "计费周期单位", "type": "java", "classType": "BillingUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "14c1896abf052f7fd8fe2bf54d0bd2e1b7523ff5", "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "98b435db-cc5e-4f0d-84e8-0382e3020a47", "name": "settlePeriod", "title": "计费周期", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "a2f0541d-9fbe-402e-930e-ff7d943a171d", "name": "billingMethod", "title": "计费方式", "type": "java", "classType": "BillingMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "baa55b6d34ca3c4014a24602e07065b52fd0bb5f", "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "bfdc1fe3-a17f-491a-96e7-4d368b684e5d", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "c0b28f30-9c6e-4fcc-b28d-ba3bfbd8922d", "name": "settlementAll", "title": "使用后是否全额结算", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "c9d91866-ce26-4be6-b841-f36f0ec3928e", "name": "discountRate", "title": "折扣率", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "d9accfb0-07df-494b-86eb-4d31c63ee5fb", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "f2b6187c-45bd-4dfc-a12f-f13e1bf56f7e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}, {"id": "f488c616-3f12-49e5-b36e-6c92d4d6c8d6", "name": "taxRate", "title": "税率", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-abcd-a64e-3af484a72777", "entityId": "9f0e039a-75c6-4041-827f-aad385f04768", "valid": true}]}