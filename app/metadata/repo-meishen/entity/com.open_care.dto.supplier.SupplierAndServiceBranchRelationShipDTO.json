{"id": "d1b60427-3be9-48c1-8856-a21d137a48d5", "className": "com.open_care.dto.supplier.SupplierAndServiceBranchRelationShipDTO", "name": "SupplierAndServiceBranchRelationShipDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "21355ee1-4756-4bea-9e02-467468c614ab", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "378306f0-42ba-4f7f-8c9f-3498e2bf7592", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "804f5086-4942-4b85-9b50-8dfa20238601", "name": "serviceBranch", "title": "服务门店", "type": "java", "classType": "ServiceBranchDTO", "refEntityId": "3ac1959d-2119-4482-9216-099c538aa87a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "8986c563-8a0d-4b29-a75e-7fddffefc712", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "9f5519ab-f763-4110-ae21-0ae807cad3f4", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "b876a336-5af5-4738-8cb4-3c18eca2dfc0", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "bc020dd4-6de4-4bb3-bd7d-da353d14a871", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "c1a674ff-1ea2-4512-a9d7-c024091ce462", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "d15fce90-fc82-409e-932a-f9c66eec0a00", "name": "supplier", "title": "供应商", "type": "java", "classType": "SupplierDTO", "refEntityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}, {"id": "e9f31bc9-a05d-4947-bc52-71572bec8ad3", "name": "status", "title": "true/上架,false/下架", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1b60427-3be9-48c1-8856-a21d137a48d5", "valid": true}]}