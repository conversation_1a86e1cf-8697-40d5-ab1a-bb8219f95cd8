{"id": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "className": "com.open_care.dto.report.ExportDataDTO", "name": "ExportDataDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1179fbfa-76c9-428d-9059-48aab443b3bd", "name": "footer", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "18848537-9e91-4548-8f4a-6431b798cf00", "name": "printer", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "247af92b-8dc9-4537-90ea-c75b5210aab4", "name": "reportKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "46387dc8-bbfd-4324-9078-53e0f16b0222", "name": "fromDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "4cd09920-79f4-4832-80c8-7ce58043821a", "name": "reportKeys", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "580bd0e2-22c5-4860-8529-ab8c6cc68a75", "name": "dataList", "type": "java", "classType": "Map", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "634dccf2-51bb-4459-9810-18b3fa1abe93", "name": "cashier", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "6a380071-34dd-4d22-829c-5c9cf0a6a980", "name": "reportDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "729c8db4-3815-4309-b0c0-abf8aab8f226", "name": "endDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "8c6354f5-b521-4b75-ada3-bb48486fb173", "name": "title", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "bed66064-8719-4be7-80a1-bb8907ab20f4", "name": "filters", "type": "java", "classType": "FieldData", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "d22b3cfc-feaa-429c-8c8c-e0994e3aec70", "name": "userId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "d66d2b7f-165a-4b31-94ab-fa998801804f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "e2f2e514-8e09-44ca-974c-27f298fef62c", "name": "sorter", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "ef0b551e-067e-40e7-91de-d584fe19c5a7", "name": "fileName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}, {"id": "f6af2b21-afb8-449e-8f94-0614f33b4c47", "name": "userName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "158c5ae7-7572-4ca9-90e2-b0ee6217c497", "valid": true}]}