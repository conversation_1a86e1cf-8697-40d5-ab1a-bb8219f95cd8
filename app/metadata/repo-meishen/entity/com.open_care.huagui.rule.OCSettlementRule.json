{"id": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "className": "com.open_care.huagui.rule.OCSettlementRule", "name": "OCSettlementRule", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0f14b46a-a3ce-47b6-b86e-b45530d64c7f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "1cb68c99-933a-4be5-bfa6-97cc25035b7a", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "24da2d57-28f7-436e-b302-0f4f004e5763", "name": "tieredRateConfigs", "title": "阶梯费率配置", "type": "java", "classType": "object", "refEntityId": "fbb60776-6bb8-4262-b36a-310716ef83d1", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "440680f8-f3ff-4858-b9c5-163f4cb3b383", "name": "platformTechnicalServiceFee", "title": "平台技术服务费（元/月）", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "486b625d-0a12-4330-acd5-14a9493f547e", "name": "settlementCycleType", "title": "结算周期类型", "type": "java", "classType": "SettlementCycleEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a768c7cd851609871c9c0256ef784189bb2e37c4", "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "4ad8c72c-8976-42b8-b232-783bff95123b", "name": "ruleDescription", "title": "规则描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "552caecb-ddac-4b65-b0fc-ec2907fbc2d6", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "5b981fc2-ab5e-4dc9-be06-d666687d25ea", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "5e9ff8ef-9048-46c6-9126-6328b09c3124", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "73082b8b-c082-462d-9c4b-d3a7a43323a6", "name": "expiryTime", "title": "失效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "7fb3e6e4-96c2-47ed-b338-b8f5dbd60acd", "name": "transactionProcessingFee", "title": "交易处理费（元/笔）", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "81d002d9-9738-44fa-ae1e-b128fd99932f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "954b390d-42b3-45c1-a8a7-d24477243d1e", "name": "settlementCycle", "title": "结算周期类型", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a768c7cd851609871c9c0256ef784189bb2e37c4", "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "a52a96e8-c853-4bd1-b217-b82da400ff7f", "name": "baseServiceFeeRate", "title": "基础服务费率（%交易总金额）", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "a8784947-e85c-45f7-bfdb-867627ce2f0a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "bd99461b-6ead-45be-94d1-ebc1b230ed00", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "bfb8f612-fc70-484f-a7bb-17aad466eddb", "name": "ruleType", "title": "规则类型", "type": "java", "classType": "RuleTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8d22f76ebdf5256c56b8bb2ae1cff8eb2eebb91a", "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "c4f9ff65-77a9-4ec6-bb7b-61ad0fbea9ad", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "c810e066-bcd9-4b46-a673-dfe8432bc793", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "d14a59de-51ca-484c-ab4d-79b29fbf8b94", "name": "effectiveTime", "title": "生效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "d6cbc176-ff43-49f7-8c21-8cdb1bc885cd", "name": "ruleName", "title": "规则名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "e9f36b15-4181-4f69-a05b-a906621c2b29", "name": "taxRate", "title": "税率（%）", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "f4975dda-74e0-43be-9174-9bf6ea9d7d78", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}, {"id": "ffa7772b-cf21-4281-9a38-553f3b9fd1d6", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bfd506ea-e17f-4f5c-9e78-98f2a03314c1", "valid": true}]}