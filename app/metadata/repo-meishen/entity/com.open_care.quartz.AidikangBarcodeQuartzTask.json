{"id": "7af20b45-3599-42e4-9b73-b786895f7fdf", "className": "com.open_care.quartz.AidikangBarcodeQuartzTask", "name": "AidikangBarcodeQuartzTask", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "005c010b-cd3b-4560-8446-df7676761331", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "019adbee-42bd-496a-ab0e-9e3666cc25c7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "13488b86-8e6f-44c9-93a6-51da0b49d952", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "26911846-d53c-46ad-bb87-6aaedbc2ac4f", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"status": {"$id": "26911846-d53c-46ad-bb87-6aaedbc2ac4f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "状态", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2bff3863-67f4-4c5a-a797-58d61499ce88", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "2df34e83-1738-49a4-9cf6-3cc9b5f06969", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "3c5de02a-8b74-41f5-90e4-8f98fbbb397c", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "3ee93355-c4d0-40e2-a25d-2b918bc139ba", "name": "patientName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "6df5ac48-6a03-4df0-94c2-8844f5096fc9", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "6e7c2f62-bdca-473a-970c-4e7c81fbecfb", "name": "retryCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "77846af2-6f4a-4b94-b3e2-6a19fe0c1207", "name": "aidikangDateRangeQuartzTask", "type": "java", "classType": "object", "refEntityId": "d7474e10-34aa-4742-b7e4-dac12f7e9a1b", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "85942ab8-a661-4855-9a24-d6df8c2f603c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "8a6c4a99-2dfb-4029-b1df-03cd04772756", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "8f4dd87c-e326-4892-851d-df27bd8627d0", "name": "customerBarcode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "9dcc29a8-8560-4b0d-867c-5d5ea3b84e9a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"created": {"$id": "9dcc29a8-8560-4b0d-867c-5d5ea3b84e9a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "创建时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b7675830-a087-41bf-a6ad-873c862bca49", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "c318d76c-d00f-4d02-a650-7f437c604fec", "name": "<PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "cb21be92-b4cf-4a2a-994e-5d99b1abce1d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "ccce0ffa-378a-4d33-96a5-46557eab811b", "name": "quartzTaskLogs", "type": "java", "classType": "object", "refEntityId": "d068172a-ec56-4a60-98d0-4e97219dbe9e", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "valid": true}, {"id": "f53f0ed6-32c7-4e97-82a3-ca1ca93b2f70", "name": "taskName", "title": "任务名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "7af20b45-3599-42e4-9b73-b786895f7fdf", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["taskName"], "properties": {"taskName": {"$id": "f53f0ed6-32c7-4e97-82a3-ca1ca93b2f70", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "任务名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}