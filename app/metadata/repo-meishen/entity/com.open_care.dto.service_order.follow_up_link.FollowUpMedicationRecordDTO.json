{"id": "5bb7ef6e-2cc7-451f-904a-8690086c5691", "className": "com.open_care.dto.service_order.follow_up_link.FollowUpMedicationRecordDTO", "name": "FollowUpMedicationRecordDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "22250759-a511-4aaa-9f12-7b6233f020e5", "name": "medicationRecordItems", "title": "用药服务记录明细", "type": "java", "classType": "object", "refEntityId": "1cebf94c-b5c9-4025-9afa-eb8b21d015aa", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "5bb7ef6e-2cc7-451f-904a-8690086c5691", "valid": true}, {"id": "7edbe977-70e4-4a05-a6e0-6ce3eae3e625", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bb7ef6e-2cc7-451f-904a-8690086c5691", "valid": true}, {"id": "8d669c2a-4051-4791-8dc2-386ee1cdb458", "name": "whetherUseMedicationService", "title": "是否使用用药服务", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bb7ef6e-2cc7-451f-904a-8690086c5691", "valid": true}]}