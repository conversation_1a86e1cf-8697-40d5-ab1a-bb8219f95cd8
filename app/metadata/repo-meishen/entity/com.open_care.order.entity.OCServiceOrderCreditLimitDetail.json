{"id": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "className": "com.open_care.order.entity.OCServiceOrderCreditLimitDetail", "name": "OCServiceOrderCreditLimitDetail", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "017665f0-e84e-427d-af66-2de3ed7d8e3e", "name": "hisS<PERSON><PERSON>tatus", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "0c222d6f-d9b7-43d0-ac6d-621683027791", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "2e2fc118-4eba-47e9-8795-a71a9d1aaf12", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "4423f4d8-ab10-424b-a522-783c4024e1b6", "name": "advancePaymentNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "44e2f706-e078-4cf7-a785-a38b38400438", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "568cbdac-af01-4b16-8c5c-0d8b16e83770", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "60716b33-c575-492b-947e-69fb77bdeffc", "name": "errorMsg", "title": "HIS系统同步消息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "69f26ac5-f95c-44c1-a21a-fad29301dc1c", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "6c8ac17e-9335-4c99-ab2f-66668cf548c8", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "7fe85f31-e8dc-4474-be9c-1e27a347ff98", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "84df6929-b12a-4cfd-ace2-76100945cdad", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "9b19accf-cefc-40af-9468-db224781eabe", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "bae6ba58-97db-47e9-aa7d-d8b047d2f04f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "c80fedfe-b69f-4bc1-ae05-78c9ad139f4a", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "c9dc6994-cc56-4b07-9cd7-1b2ef6b6d39f", "name": "creditLimitDetailTradeLogs", "type": "java", "classType": "object", "refEntityId": "4533d8c0-57bd-4350-a6a3-1d4777087d98", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "cd5515a0-5f1d-4385-b074-df6c11bb3b52", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "d2fec221-dba6-4b9b-b780-a7d8f93545d4", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "dd866bcd-298a-4212-922e-430fd507ffbf", "name": "creditLimit", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}, {"id": "f1eb3288-cc3c-47ca-9ebf-5b37e346e6ca", "name": "serviceOrder", "type": "java", "classType": "object", "refEntityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75acb129-18f8-4aec-be9d-41aeae4c6a30", "valid": true}]}