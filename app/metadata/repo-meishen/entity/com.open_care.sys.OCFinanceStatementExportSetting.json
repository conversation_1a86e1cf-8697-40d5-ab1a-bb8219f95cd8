{"id": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "className": "com.open_care.sys.OCFinanceStatementExportSetting", "name": "OCFinanceStatementExportSetting", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "002dae5c-2219-4f68-bc14-7ad021ea3b9a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "0a3a7bba-bd3b-475c-a8f1-4cc520826b15", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "14ea7f23-9889-4047-be9d-f6aeb5336f3a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "1c8d44c8-4937-4719-b848-cc9319a75a57", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "504cbeb5-2414-4436-9660-9e0a234b657f", "name": "code", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "504cbeb5-2414-4436-9660-9e0a234b657f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "编码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "52522fea-670c-42d5-9210-36fd1d0de4fa", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "676a5a80-00ed-499c-8dca-e5040fee0655", "name": "excelSuffix", "title": "后缀, XLS/XLSX", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "153e3e01-f52d-4df3-b5b3-adb24afb0e6d", "style": "Select", "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "xlsx", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["excelSuffix"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"excelSuffix": {"$id": "676a5a80-00ed-499c-8dca-e5040fee0655", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "后缀, XLS/XLSX", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6ad225ad-1cf8-4348-b9e3-306431dda17d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "70db0422-0bd4-4b45-a0d9-8853f3e28cb9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "71701bf0-ed90-4012-bdd6-265ef83f71ca", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "87b6e8dc-4055-484b-b820-e6c0b8799442", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "87b6e8dc-4055-484b-b820-e6c0b8799442", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "96099f73-30aa-4118-8fea-e1b2696c3cf4", "name": "automaticallyAddSerialNumbers", "title": "自动添加序号", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "true", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["automaticallyAddSerialNumbers"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"automaticallyAddSerialNumbers": {"$id": "96099f73-30aa-4118-8fea-e1b2696c3cf4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "自动添加序号", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a0ffdf3a-dfe4-45f3-9fb5-04e5a3ea1024", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "acc3d119-90c3-4da6-b9ce-bdca00ff3a6c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "bbe7924b-837a-499e-997a-bad60d4902ca", "name": "financeStatementExportColumnList", "type": "java", "classType": "object", "refEntityId": "20ce7a45-f451-4283-a0d4-09ac3495a45a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "ceb08f76-f154-4d17-bb3a-b7cc50631d0f", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "fd1c0b51-10ce-4f0b-81a7-5c42ce47493e", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}, {"id": "fd4cd269-e4e7-423b-856e-175610a300ec", "name": "seqno", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d13f2cda-6d4e-4b5f-9d0c-e21c092c67da", "valid": true}]}