{"id": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "className": "com.open_care.medicalMicro.customer.OCCustomerExamProduct", "name": "OCCustomerExamProduct", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户产品记录", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "012c22a7-bd77-4a5c-bda2-859596f1e7e1", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "037ee226-2f7f-46c7-8fed-e17c70d0f4c9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "04a91c56-30bb-41aa-bec0-04f16a87f7a7", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "07cc6970-c3c0-46af-b8a8-1e2dbc071b0d", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "08b533fb-f19d-4fb8-ab9b-6840d0cf7b6e", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "17b00b83-caa5-4f54-a72b-e24b8b55a801", "name": "entryName", "title": "录入者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "17b00b83-caa5-4f54-a72b-e24b8b55a802", "name": "auditorName", "title": "审核者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "17b00b83-caa5-4f54-a72b-e24b8b55a852", "name": "auditor", "title": "审核者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "1a88b72e-adf8-41a7-8cd1-a15e38b753be", "name": "postponeDeadline", "title": "延期到期日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "22761e35-7df1-4480-871e-6de8e87e6af8", "name": "examProductCode", "title": "产品编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "2a0e250c-7e79-4977-bae0-674ba7c7235a", "name": "severeDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "style": "Select", "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "2a648151-2823-4c32-8fcc-20833d7b1138", "name": "customerExamDepartment", "title": "对应科室", "type": "java", "classType": "object", "refEntityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "381ef1d7-b27d-4fb1-92a0-8cb55f1de1aa", "name": "submitted", "title": "已送检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "3a0656e0-dee6-4541-b5aa-923f6fc40835", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "3ad891f9-5d60-45b9-bfd2-33aff3acab95", "name": "checkItemQuantity", "title": "判断是否检查明细产品数量", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "497c64be-fcd2-4d78-ad03-037bc62a65ae", "name": "sampleType", "title": "标本类型", "type": "java", "classType": "object", "refEntityId": "4cd80aa0-64b7-4916-b092-8c39bb040245", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "4c036ae2-4666-4796-acce-5b46c3d57ba6", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "502c77b5-1d44-47a4-acc3-c5ebda71885f", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "5256b446-0085-4235-84dc-21248ddb3cbd", "name": "needSample", "title": "需要采样", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "577007fb-dd61-4625-b7a3-351bd447b03f", "name": "sampleTime", "title": "采样时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "5e0a510c-1ad7-4427-a670-6e4df0bc2381", "name": "productPositiveSummary", "title": "产品阳性汇总", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "5e55c03c-91cf-42c5-9c97-4f4e277649d1", "name": "submitTime", "title": "送检时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "5f4a9593-d5d9-472f-9835-f2ee63dcbfa4", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "5fb48e09-9a7a-4403-a5b2-a7a991488893", "name": "serviceOrderItemOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "602837b0-e405-4e45-ad7d-a4833a41cf21", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "623e2a25-1bee-416e-b2fe-f63fda636c60", "name": "uploadedToInspectionOrg", "title": "是否上传至第三方检测机构", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "643529a7-bbce-4a3d-ad14-f6f9b6031ec7", "name": "auditTime", "title": "审核时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "66b44fb6-91cf-4d4a-8420-1641d0adfda9", "name": "entryTime", "title": "录入时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "7565a0c3-22d1-43ad-8735-c0e2100a645b", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "76b28c91-d9f8-43b2-af0f-0d1099367fba", "name": "productMedicalName", "title": "产品医疗名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "771efdcb-0b93-4466-91e5-c217ec960c31", "name": "entry", "title": "录入者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "7ae0fad0-28dc-448b-8241-ad010b36d56c", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "7ae112c8-15cd-49b7-bb3f-8168f2e5d313", "name": "needSubmit", "title": "需要送检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "7d238e77-12a9-48d8-8d69-f342389831dc", "name": "entered", "title": "已录入", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "7edbfeac-fe87-40b8-9b6d-497e35f0fe15", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "82fdf3d1-4b86-4b0c-a240-138e52767371", "name": "processInstanceId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "845cd497-550c-4542-b0d4-712fe42ab0fc", "name": "abandoned", "title": "弃检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "883359a9-8c17-4e85-8818-23faec10f89b", "name": "samplerName", "title": "采样者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "8d562cf5-9143-46b2-a23e-826a3309236e", "name": "postponedWhenFinalExam", "title": "总检时延期状态", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "8f5329ea-e86a-4761-84c7-112ec7ed523e", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "9cdc67c7-0ef3-46e7-94ba-744aacc45c72", "name": "sampler", "title": "采样者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "9e3ad7aa-be87-4c89-b108-3977693274a5", "name": "submitter", "title": "送检者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "9fe82fe1-06af-4cde-8256-075a8b47bb96", "name": "packageName", "title": "对应套餐名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "a319f3dc-e92b-4ebe-8ba3-8c3b9aaec284", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "a3483a4d-981c-4353-a5b2-a180a6fa8096", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "ab4a16e8-951a-445d-bee6-4ec2c4dddc65", "name": "oldFlag", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "abd620ae-e5e7-4f79-a5ff-1b370e14c31f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "ac06edc3-10af-4007-a35f-223a4e1e5b1d", "name": "postponed", "title": "延期", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "aeac2584-d8e8-46cb-85ce-b272e1049f33", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "b3e38f50-2962-49a8-82be-2fa5624075a1", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "b9890091-2c44-43ca-9074-7362bb797b35", "name": "separateReport", "title": "单独出具报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "c8511085-d4fa-4de3-848c-b5e034dd75ff", "name": "customerExamProductBarcode", "title": "产品条码号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "ca78c342-d14f-4727-a2fd-7c564303da31", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "d52357d1-0179-4086-8c4e-d36888a4ef86", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "d611b589-37b9-49f8-bade-dc5c83eff8de", "name": "productSummary", "title": "产品汇总", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "d637487b-7cec-4d14-aa75-b6fbdea54fb8", "name": "examProductName", "title": "产品名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "d874d4bf-6207-46be-83c8-076fa697aa40", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "d8e09709-ab9f-4e85-974a-bdf8a9ff7cc8", "name": "forReview", "title": "复查", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "dd3fd45f-d6af-47e1-8f7b-72cf19e0f737", "name": "productId", "title": "产品ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "de9cda0f-486a-4da1-b88a-90a20e99b5da", "name": "pacsed", "title": "已发送至PACS", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "df25ce07-f7df-483c-97fe-57cf341e1f6b", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "e0058552-9cfd-44f7-8d72-77773fff1152", "name": "customerExamItems", "title": "基础项目明细", "type": "java", "classType": "object", "refEntityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "e258b620-83a3-4b74-9e15-ea0d220c15be", "name": "submitterName", "title": "送检者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "e2cb9c53-598e-46aa-bf70-ce1fd0544010", "name": "sampled", "title": "已采样", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "e68b717e-8279-43e9-9511-d2cc89f3a609", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "e7ab1a4b-eb4c-49ef-b315-188915649e11", "name": "productSummaryDesc", "title": "产品汇总详情", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "ebadbac5-725e-4df6-b646-ceb192931f2e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "ebce94af-9e1f-42cb-bc4f-6098cf0feb2b", "name": "audited", "title": "已审核", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "ef7924c5-6a93-4ab9-969b-40312dd0ed36", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "f588d3b8-9eca-4bd3-9283-897ce8293417", "name": "mealType", "title": "就餐类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "f776c79a-e172-4bc8-8967-d958b64b7d6f", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "f8ba1017-d615-44ce-8083-12513a7129be", "name": "serviceTime", "title": "服务时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}, {"id": "f93f4ee1-dc5e-44f3-9c2a-fbe2f205f874", "name": "examOperationInfos", "title": "项目操作信息", "type": "java", "classType": "object", "refEntityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "valid": true}]}