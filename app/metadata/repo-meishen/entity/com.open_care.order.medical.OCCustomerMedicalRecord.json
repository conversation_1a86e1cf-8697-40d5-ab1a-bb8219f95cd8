{"id": "f6727162-3cf3-4411-9eda-99e6c5c47157", "className": "com.open_care.order.medical.OCCustomerMedicalRecord", "name": "OCCustomerMedicalRecord", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户病案", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "07e00e39-dda8-4567-b0b3-7947ebcf0717", "name": "paymentStatus", "title": "订单支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "0a813353-3cfd-4e0b-8eea-32c3722d6a0b", "name": "print", "title": "是否已打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "0a81e495-4049-471f-9ee2-420574e43383", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "OCOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c91e35a85e7bca35cb090a6b23a9ad875e31951f", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "0e9accd5-e9d5-428b-b6fa-9456c203e61c", "name": "sourceDepartment", "title": "来源科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "13f89b9a-7188-415e-9fe4-3b25137efa1c", "name": "selfUse", "title": "本人使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "14f05b9f-496a-47e2-b83c-3a97f30fad4a", "name": "qrcode", "title": "收费二维码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "15499f59-c36d-4c10-8989-88a8aefe68e0", "name": "resolution", "title": "处理意见", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "16514760-17d8-40ad-89e1-6cd995f8fd61", "name": "rcptNo", "title": "预交金单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "16ebbb98-c062-45f0-930f-817988878e10", "name": "beneficiaries", "title": "受益人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "1872f54f-7101-4c85-9df2-e18a1a023916", "name": "partyRoleName", "title": "客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "19dc80f8-acad-428f-bcef-e7e38ae078d0", "name": "selfReportedSymptoms", "title": "主诉", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["selfReportedSymptoms"], "properties": {"selfReportedSymptoms": {"$id": "19dc80f8-acad-428f-bcef-e7e38ae078d0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "主诉", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "20e05903-b2fd-4662-a715-f93fc5ce4054", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "26f4de9a-18eb-4e74-a74a-d1404c463c47", "name": "drugAllergy", "title": "药物过敏史", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "36da498f-388e-493a-bfe6-80014147245c", "style": "Select", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"drugAllergy": {"$id": "26f4de9a-18eb-4e74-a74a-d1404c463c47", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "药物过敏史", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "27bb639a-4a8d-475d-b7b8-ccf41340e83b", "name": "agreementId", "title": "协议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "2bb2e9c5-b968-4896-870f-fc7cf129cc7f", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "32bde9ca-9c69-4512-8021-584a1daa07a9", "name": "orderItemGroups", "type": "java", "classType": "object", "refEntityId": "12386553-11c9-4e42-a4d2-b7c8014a8a85", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "355313cb-6b07-4aa9-9f9b-dd96712ff789", "name": "hospitalRegistrationId", "title": "挂号单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "36b98343-86da-4b29-8614-84503ceea861", "name": "refundOrderId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "37c09f85-3d7b-4e34-9e53-09095825b402", "name": "highAuthorityUserId", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "39ceb2ce-89c3-4fba-b418-a3d80b3eab6a", "name": "physicalExam", "title": "体格检查", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "3ed7169c-c555-46f0-82bf-aee24428f616", "name": "replaceOrderId", "title": "替换订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "3fbd1f4f-ec80-4613-9b8b-cdf7e105da8e", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "40d4b7d4-921f-46dd-9396-e177b27d452c", "name": "settleTime", "title": "结算日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "416e387c-cc23-4f4d-a3be-76cc5ec30bca", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "44d346dd-6456-4e16-af87-a128086a389f", "name": "channel", "title": "渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "4599b627-ba9e-488f-a938-f1d35ee185b5", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "48544b97-df7f-451f-bf60-55f9e6fa31aa", "name": "orderFlag", "title": "订单标识", "type": "java", "classType": "OCOrderFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "06eb12b855e66cabf22c3772eea900e9746ca8e0", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "4d7d3be4-8ca4-4212-bd52-20cdcfc777eb", "name": "advanceSettleDate", "title": "预结日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "4da8433d-959d-4240-a88b-45fe423d260e", "name": "otherDrugAllergy", "title": "其他药物过敏史", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "4f3b6794-715b-4068-89fc-f4f337811de2", "name": "productPriceInfoSnaps", "title": "审核通过后的产品价格快照", "type": "java", "classType": "object", "refEntityId": "d1b2e111-7825-4378-8137-cb6297029364", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "4f9608a4-e3b9-4830-9557-195a7bf9c28e", "name": "owner", "title": "所有人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "5087977f-b2c2-4289-b912-c21c3bd3fce4", "name": "familyHistory", "title": "家族史", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "5667fe51-6864-42ba-8852-2247492330f7", "name": "meeting<PERSON>ame", "title": "会议名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "56c8e4a9-5cb8-4b66-a8ec-0b055e82cbda", "name": "needUploadPdfReport", "title": "需上传电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "58d44881-39cd-4514-9508-978108ba4a91", "name": "orderItemsJson", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "5bd103e8-c45b-4338-ab24-a654b4fd809d", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "5f850c5b-1089-4c85-b32a-5b76577ebc86", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "6125c5a1-61cb-472c-a954-0ba545a1618c", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "615e79e1-ffee-4aac-a7ed-7b0a26d6fced", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "635aa3f9-f169-43d9-ae72-ab80fa8f02c9", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "63c62626-9f78-4243-b523-ffdb36785be6", "name": "presentIllnessHistory", "title": "现病史", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["presentIllnessHistory"], "properties": {"presentIllnessHistory": {"$id": "63c62626-9f78-4243-b523-ffdb36785be6", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "现病史", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6a54b9c3-182d-49ac-bd72-c861cc3916be", "name": "orderNo", "title": "订单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "6b0b6f3c-fba8-4536-ad42-801578db7bbb", "name": "followUp", "title": "复诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "6bc60a08-004e-4783-bed6-9883e4a7e550", "name": "customerSex", "title": "客户性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "6ff3f0cf-e940-4685-b607-24815792e234", "name": "cancelReason", "title": "订单作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "76922aa7-6c18-43e6-8df8-5ff702f22717", "name": "seller", "title": "销售方", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "84ff8208-4c4a-4ab6-a356-5473948bc039", "name": "saveType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "86cd077d-6136-494a-83da-0ee6e729d569", "name": "orderItems", "type": "java", "classType": "object", "refEntityId": "7b028dd2-f0d3-4b62-bc28-09489c72bd32", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "8929c8a7-b2dc-46f6-8e44-8b2550f83896", "name": "brandGroupId", "title": "品牌组ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "8b43eb4c-5100-4d51-ade0-70445a120345", "name": "children", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "8cd14d48-ed49-4602-8142-5c78a6051ccf", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "8fcbfebf-bb57-4af1-a989-3d74137c37b4", "name": "personalMedicalHistory", "title": "个人史", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "8ffc002c-2cd4-44e9-b59e-87b542cbf3c8", "name": "sign", "title": "标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "94dd3ca5-57ba-458b-b34d-716dd68f4c80", "name": "reasonForApplication", "title": "申请原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "9598b6d3-1270-4755-a1cf-11d3582acf63", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "9c01ccb9-6137-4094-96f4-ff90fd8827c1", "name": "finalDiagnosis", "title": "最终诊断", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "9e4571c4-07c9-4f98-9e12-0bda72695478", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "9ec1df1d-14a7-41c6-b13e-62de1382451e", "name": "attachmentList", "title": "附件列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "a10f06b8-b5f7-479f-8514-c814df4badd8", "name": "enterpriseCustomerId", "title": "企业客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72666", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "a30da23d-36c1-4dbb-abd8-d6339a189ac6", "name": "diagnosis", "title": "诊断", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["diagnosis"], "properties": {"diagnosis": {"$id": "a30da23d-36c1-4dbb-abd8-d6339a189ac6", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "诊断", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a606a6fe-dfd3-4015-9705-287fe4f3ba77", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "a9115fe8-732a-4e18-a607-0a36a19660ef", "name": "orderAccount", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "ac5d4bd0-7811-4c7f-bb4f-671589b63b18", "name": "branchId", "title": "门店ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "b014a557-eaa7-4c52-ab39-88333f3acdab", "name": "parent", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "b547604f-80bc-41d5-9d39-06b735563280", "name": "serviceCode", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "b6348cdd-a5d5-43c5-ba3e-d8a7f97977e5", "name": "refundFlag", "title": "退费标识", "type": "java", "classType": "OCOrderRefundFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "87fc558a08e65ed030848e89c3e183a36266bf15", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "b661992b-2f03-4b42-a3ab-079aac29603b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "b66c135c-9cd5-4d51-8eb1-0864180e7fa4", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "b77ca84e-d148-433e-b88f-a0b2943245a0", "name": "salesStrategyId", "title": "销售策略", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "bddce857-eb1c-42fe-8650-10c03374225f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "bed419a9-6137-4de4-b5e3-d16c6a23560b", "name": "serviceOrderNo", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "c610dc60-1dcd-47fb-b7ba-b1e541b14ef6", "name": "examServiceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "c75ef2eb-fa40-433e-961f-747a9be5ed1b", "name": "orderTime", "title": "订单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "ca6783e1-463d-44fa-841e-b7d81fc1fe47", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "cb10ba1f-5645-4c90-a31f-9d667e6224d5", "name": "salesExpert", "title": "市场专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "ccb3b2af-cdaf-43c0-9a9f-bd77b8909045", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "cf86957f-ced9-433a-a0b6-d2318b785532", "name": "orderName", "title": "订单名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "d0385474-7063-475e-91f7-790fa122fd8a", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "d98ed930-9b39-4dae-a160-28af79b99a52", "name": "updatedBy", "title": "更新医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "683d38a2-fee3-4d3f-8af5-2d7912aa1585", "style": "Select", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"updatedBy": {"$id": "d98ed930-9b39-4dae-a160-28af79b99a52", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "更新医生", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "db36da5e-65c1-4fc6-b3f6-4e47c60fe38c", "name": "brandGroupName", "title": "品牌组名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "e12cc3e5-02ca-4d44-9e4d-26275357117c", "name": "orderLocation", "title": "下单地点", "type": "java", "classType": "OrderLocationEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b4354294f6b84222c01e0d2e7f1b13777edb09c7", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "e38497fe-b93e-4774-84bb-223518f0055a", "name": "profitLevels", "title": "权益级别", "type": "java", "classType": "object", "refEntityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "e5a5dcfb-d014-4372-bb60-fb240538805a", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "e5bd6094-4feb-4bdc-b18c-5986027c9e80", "name": "additionServiceOrderId", "title": "加项服务单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "e71a8f88-5d53-46ec-a013-0d033269f6ed", "name": "parentOrder", "title": "父订单", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "e75dac90-132f-4ffa-826a-136f63425c2b", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "ecccadf3-bfa7-45d1-9879-ff1dd2335069", "name": "paySource", "title": "付费来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "ecf2bdd0-8d2e-4cba-afce-80fe65379049", "name": "created<PERSON>y", "title": "开单医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "683d38a2-fee3-4d3f-8af5-2d7912aa1585", "style": "Select", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"createdBy": {"$id": "ecf2bdd0-8d2e-4cba-afce-80fe65379049", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "开单医生", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f0df2764-398e-4698-8fc5-b78d8c52f72f", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "f4e8c60b-c873-4bf3-85f2-f9fa5f4de3e2", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "f717c369-a4e8-4507-848e-07ccb181de55", "name": "pastMedicalHistory", "title": "既往史", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "f72074fd-4c67-485f-92c8-e71533f405d2", "name": "cancelInfo", "title": "取消信息", "type": "java", "classType": "object", "refEntityId": "cd2543df-8799-48e5-8a37-a870977a9a19", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "f90e0445-d5d9-4f41-aa99-899580c96254", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "f959b23f-9e39-4627-9ed9-7908e483cc94", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "fac68fe1-b827-420b-afa4-5552584938c9", "name": "orderType", "title": "订单类型", "type": "java", "classType": "OrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ce7f095131e8e2446b2a9a9c184e62dfb78ef8d7", "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}, {"id": "ff94d955-5c9b-4bf8-af3f-30653f666cf5", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "valid": true}]}