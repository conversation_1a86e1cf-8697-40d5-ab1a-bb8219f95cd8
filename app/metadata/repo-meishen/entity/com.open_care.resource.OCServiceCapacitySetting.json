{"id": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "className": "com.open_care.resource.OCServiceCapacitySetting", "name": "OCServiceCapacitySetting", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0271ba7e-6730-46fc-b12f-59c1525cc9cf", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "1088850e-e991-4cf1-a1b6-e31a17e5a429", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "1faf151b-bd53-4981-a920-c5335a495b70", "name": "customerTypes", "title": "客人类型", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "419127b7-8d3f-4884-8f9d-2cda9f3f25de", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "5bf85ecf-2c5a-4640-bc0a-1c5cdaed8a1a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "5d407b05-27f1-4832-a810-691e9731d739", "name": "shiftSetting", "type": "java", "classType": "object", "refEntityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "71dc0e01-1974-4397-9f9d-87b07b5a13dc", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "74b37f93-34aa-4b0f-b23a-5684800b6c90", "name": "departments", "title": "科室", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "81812dab-d60d-4400-944f-123a023d92da", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "90a6d514-76c8-4a72-a7e7-4d613e0f7945", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "939e6eaa-63f3-4fb1-8ce9-1c6a306deb84", "name": "warningValue", "title": "警告值", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "96313062-257f-4f3d-866e-3a56aac0b3ce", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "a3381960-bf8f-4152-a85d-c08045b8170f", "name": "products", "title": "产品", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "c0a5647b-21e6-4b8e-8de4-63448ca46f62", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "cabe5622-0b94-4173-92b8-ccedaaf8432c", "name": "enterprises", "title": "团体", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "d28161ba-4ad6-4e78-85b0-ae4381e6a6d9", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "d31c3763-acac-49db-af7b-07c125c49974", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "e29d4d4f-2032-41a0-a306-b16d119e8bcf", "name": "bookingChannels", "title": "预约渠道", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "ed790acb-1b21-4658-bfdc-f2575abf3583", "name": "alarmValue", "title": "预警值", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}, {"id": "eedddc77-b42a-4e32-a719-770db5eea215", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "valid": true}]}