{"id": "c7532374-a0d0-4adb-8fd6-367f79590287", "className": "com.open_care.medicalMicro.customer.OCCustomerExamItemPictures", "name": "OCCustomerExamItemPictures", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户检查明细图片", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0c5d2e06-666a-4f1f-8e50-a2136e3a9f3b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "180df008-99ee-466a-8852-f5d27093a0a0", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "1a3e3a37-b926-4e21-8204-169acd08a580", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "1c2459ec-f05a-4923-a0eb-ec2dfc7f127d", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "1ee2a9c4-dd00-4593-854f-4ab1598223c0", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "2494a025-9e2a-43f7-b503-7c0825fd7725", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "55bef23d-aaf3-462c-bda6-67eed158794f", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "5d564386-dfb6-478d-bfd5-ba8f5ae2d711", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "69c36421-0734-4304-a7bc-7ef637a17c8f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "6ef99ee3-9859-4a2d-92ed-8de3e183cab0", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "82b86782-eda2-4eaa-9655-add7490dd98d", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "96d0d35f-99fd-4bad-aaf0-7bad3613fa46", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "b1efa0d0-6f03-4c55-8d32-106ac9c71db4", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "b46e288c-0eff-486a-bf6c-3894993a16e7", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "c16a7f02-b9d3-412e-b580-d09c31b48b57", "name": "examItemOcId", "title": "明细项目", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "cc1fbb43-8bda-44ed-b58a-469e08b38c17", "name": "productOcIds", "title": "产品", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "d0eb6fc9-638b-4d6b-b7dc-8c153b4e3beb", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "d3e469e7-c212-4c96-bed7-ca40028e139b", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "d555385e-2400-47d2-babb-3dc14e85826d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "dbf11bf2-fc0e-4fe8-8d29-6f7d45bce8d6", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "dc097b20-c9de-4cd1-93b5-2fd7ab28b768", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "dc869a59-bf33-46de-8c7e-600f462c91e2", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "e477eb0a-d55c-48c1-90b2-db25df0e5daf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "f195a2fa-c692-4f76-b0a4-51fb295e5835", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "f3da6c36-9730-45b6-958c-7ed352b5f31e", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "f4e208db-3053-4287-b4f5-84a527eb1c30", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "f8d98afa-8110-4e8f-8e57-cf2109b7f820", "name": "customerExamItemPictureDetails", "type": "java", "classType": "object", "refEntityId": "85ce0b84-2232-4701-80f7-808619651a85", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}, {"id": "f9653e4c-a5da-4710-ba2d-04c3a24a9e13", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7532374-a0d0-4adb-8fd6-367f79590287", "valid": true}]}