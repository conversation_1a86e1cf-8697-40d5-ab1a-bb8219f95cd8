{"id": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "className": "com.open_care.payment.OCPay", "name": "OCPay", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "003de0a9-793e-4959-ba79-746cb175f063", "name": "payerId", "title": "付款方ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "05950db8-837e-423f-93d6-5221e677771e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "06185143-d5cf-43bc-95de-1dc389a081f4", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "1040e9b4-a5f1-4b60-8224-8c658692fcf7", "name": "partyRole", "title": "客户ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "116ebcb6-de32-4140-84e5-ddb9cb4bf143", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "12d7b8d9-772f-4d7a-a0df-435b73690817", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "288f3a92-340e-45d3-acdc-a483b7d85c07", "name": "paymentNo", "title": "收费编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "366fd5b5-d3c6-4d8a-8db7-8b13ea03d756", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "3a24b9f5-017c-4d92-979d-699f1568a110", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "4134f046-20ae-4310-9fa1-f5e4c27f23c0", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "442b396d-7612-4ad5-95c5-ca079d93a0ab", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "47ee0d6c-a4b4-40b8-9ee9-cf308055736d", "name": "agreementId", "title": "协议ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "4f4f068e-033c-4eaa-a32b-f68433c27c95", "name": "refundFlag", "title": "退费标识", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "4ffa8752-df34-43dd-a769-26ecde85aa57", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "527cf735-499d-4439-9f59-59cbf8f9fd9e", "name": "payments", "type": "java", "classType": "object", "refEntityId": "1e4adb73-21cb-4e49-bdd9-abf12b2844ce", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "52fbbb7f-75ca-417c-96f0-678731892655", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "551e555c-1357-47ff-982a-7289fc0f1641", "name": "paymentTime", "title": "收费时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "551e555c-1357-47ff-982a-7289fc0f1642", "name": "chargeSubject", "title": "收费主体", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "9b5a5081-0a64-4589-9e0a-7b0877f1bda6", "style": "Select", "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["chargeSubject"], "properties": {"chargeSubject": {"$id": "551e555c-1357-47ff-982a-7289fc0f1642", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "收费主体", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "5a543bf6-e2ee-4971-abe8-29621e50940e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "74b1fdd9-2d42-47d5-b18a-033c433663d8", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "8bf551af-01bd-44a1-9e2d-a17d4e317f54", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "8dad3c87-acd6-41aa-89a8-95f44085cb1a", "name": "paymentAmountFact", "title": "收费金额实值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "9dc4464e-862f-4630-9eca-d27e5d67c884", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "a6f76845-9d81-48b0-9a43-bee6eb329349", "name": "comments", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "c16086a0-9784-4e41-aac5-8f9628042d4b", "name": "paymentType", "title": "支付类型", "type": "java", "classType": "PaymentTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "6d169d15c1dbaa1819106c4575363d51c7bb8759", "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "c6f3c819-42ed-4e5a-856b-66f8e48d46c7", "name": "paymentAmount", "title": "收费金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "d0746b94-8d41-4043-99d4-6037ca9cd8a4", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "d773b085-a762-4f4b-a879-f00ab7ddd4e1", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}, {"id": "d944dd8c-2ca5-44d2-a2ef-c1b8f5d5bc98", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f0d3b412-8d3c-472f-b17a-170b59b391d4", "valid": true}]}