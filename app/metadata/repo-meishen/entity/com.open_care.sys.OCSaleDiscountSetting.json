{"id": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "className": "com.open_care.sys.OCSaleDiscountSetting", "name": "OCSaleDiscountSetting", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00cc8184-639c-4025-8324-fff9d52e9326", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"name": {"$id": "00cc8184-639c-4025-8324-fff9d52e9326", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "1fe868c2-da42-40ee-88a6-dc86b529043b", "name": "minRatio", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "valid": true}, {"id": "2b897677-e785-43be-831f-1229770cd6c2", "name": "salesRatio", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "valid": true}, {"id": "5af7a1ba-9167-41b7-9958-f2f9c89d10ce", "name": "maxRatioComparisonOperator", "title": "xx", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["maxRatioComparisonOperator"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"maxRatioComparisonOperator": {"$id": "5af7a1ba-9167-41b7-9958-f2f9c89d10ce", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "xx", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6249da7c-952c-448f-b5de-bc7964ccf24b", "name": "maxRatio", "title": "折扣上限", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["maxRatio"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"maxRatio": {"$id": "6249da7c-952c-448f-b5de-bc7964ccf24b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "折扣上限", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7ae0eca2-740d-47ef-892d-0c0137743bca", "name": "warnUpperSalesVolume", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "valid": true}, {"id": "7d78453c-1a00-4171-9118-8728965e4082", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "valid": true}, {"id": "8d00d9fe-0105-439b-bac2-39dd896408ca", "name": "salesVolume", "title": "金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["salesVolume"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"salesVolume": {"$id": "8d00d9fe-0105-439b-bac2-39dd896408ca", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "金额", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a1b60ee8-8950-401c-b19c-123234d98c07", "name": "minRatioComparisonOperator", "title": "折扣下限", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["minRatioComparisonOperator"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"minRatioComparisonOperator": {"$id": "a1b60ee8-8950-401c-b19c-123234d98c07", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "折扣下限", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "cfeae5b2-b988-4f8d-baaf-3423b1ecb014", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "valid": true}, {"id": "f8466508-e070-49c7-9c46-d776768c7d55", "name": "expression", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00153a8c-9e51-4b58-b73e-0f5d2a956dd1", "valid": true}]}