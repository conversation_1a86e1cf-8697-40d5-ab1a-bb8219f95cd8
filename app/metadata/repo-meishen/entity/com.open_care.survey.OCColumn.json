{"id": "632a8618-e448-4b4a-827b-08d84833fd7b", "className": "com.open_care.survey.OCColumn", "name": "OCColumn", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "26078177-6d5c-4678-8a83-9eb5ce19c824", "name": "editable", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "632a8618-e448-4b4a-827b-08d84833fd7b", "valid": true}, {"id": "76d9c14b-cc0a-4d0c-8b5a-dfd9a01cce93", "name": "title", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "632a8618-e448-4b4a-827b-08d84833fd7b", "valid": true}, {"id": "8481cc46-9fba-44f5-b82c-ea0a82e6df55", "name": "seqno", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "632a8618-e448-4b4a-827b-08d84833fd7b", "valid": true}, {"id": "9d060b54-8ca2-41e6-a7ea-eb23b1f016b7", "name": "width", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "632a8618-e448-4b4a-827b-08d84833fd7b", "valid": true}, {"id": "ba668b60-b897-406a-b537-20a75d5e1359", "name": "dataIndex", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "632a8618-e448-4b4a-827b-08d84833fd7b", "valid": true}, {"id": "c51ac101-7a00-4de9-ad1e-018b3ef12dd8", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "632a8618-e448-4b4a-827b-08d84833fd7b", "valid": true}, {"id": "e2e069d0-e53e-4cc2-a2be-dcccf64f295a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "632a8618-e448-4b4a-827b-08d84833fd7b", "valid": true}]}