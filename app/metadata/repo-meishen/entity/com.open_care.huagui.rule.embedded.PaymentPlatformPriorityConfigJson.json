{"id": "7dad3f55-6b98-4d34-85b3-2e666b117476", "className": "com.open_care.huagui.rule.embedded.PaymentPlatformPriorityConfigJson", "name": "PaymentPlatformPriorityConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "29d99f7a-9d90-4085-96bc-6877435b0788", "name": "enabled", "title": "是否启用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7dad3f55-6b98-4d34-85b3-2e666b117476", "valid": true}, {"id": "9e6a1ffc-a437-4593-b43c-befde65f6d1a", "name": "priority", "title": "优先级", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7dad3f55-6b98-4d34-85b3-2e666b117476", "valid": true}, {"id": "a17fd90a-5087-48c8-83a3-50d2663c1649", "name": "platformName", "title": "平台名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7dad3f55-6b98-4d34-85b3-2e666b117476", "valid": true}]}