{"id": "4ee6730d-1641-4026-8756-c646ce280e49", "className": "com.open_care.dto.wordOrder.AssignedTaskDTO", "name": "AssignedTaskDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "9759fa24-0395-49bb-9a56-1a4e90a13631", "name": "userName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ee6730d-1641-4026-8756-c646ce280e49", "valid": true}, {"id": "c25072e7-6992-4524-a6c6-a732f7473c62", "name": "userId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ee6730d-1641-4026-8756-c646ce280e49", "valid": true}, {"id": "d3d3e153-b277-4992-8082-4d207dc33394", "name": "totalCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ee6730d-1641-4026-8756-c646ce280e49", "valid": true}, {"id": "dc71377d-46ed-4ca5-8e75-f3e0189050de", "name": "oldTaskCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ee6730d-1641-4026-8756-c646ce280e49", "valid": true}, {"id": "f80b0437-1712-4dad-b483-14646483640b", "name": "newTaskCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ee6730d-1641-4026-8756-c646ce280e49", "valid": true}]}