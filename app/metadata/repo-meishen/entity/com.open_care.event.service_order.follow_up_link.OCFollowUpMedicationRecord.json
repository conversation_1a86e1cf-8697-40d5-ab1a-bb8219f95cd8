{"id": "f75e33c8-f061-4491-b202-cbb5987b046a", "className": "com.open_care.event.service_order.follow_up_link.OCFollowUpMedicationRecord", "name": "OCFollowUpMedicationRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "32d43489-930d-4b00-8ec1-11b9fcfaa3b6", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f75e33c8-f061-4491-b202-cbb5987b046a", "valid": true}, {"id": "4f7acac5-c4ec-4840-b4e8-4a96bdc4d9b8", "name": "whetherUseMedicationService", "title": "是否使用用药服务", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f75e33c8-f061-4491-b202-cbb5987b046a", "valid": true}, {"id": "6cc00dd8-934b-4062-8063-1b8b483d0b17", "name": "medicationRecordItems", "title": "用药服务记录明细", "type": "java", "classType": "object", "refEntityId": "a276f8e7-a793-44de-a1a3-27e917089f40", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f75e33c8-f061-4491-b202-cbb5987b046a", "valid": true}]}