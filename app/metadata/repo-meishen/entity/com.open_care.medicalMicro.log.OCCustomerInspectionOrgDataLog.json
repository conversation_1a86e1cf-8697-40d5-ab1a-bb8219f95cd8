{"id": "83e8c03b-631a-447a-a45a-9472778df1bd", "className": "com.open_care.medicalMicro.log.OCCustomerInspectionOrgDataLog", "name": "OCCustomerInspectionOrgDataLog", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0b78b81b-84f2-431b-9020-864b2685bb73", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "0ca531ce-3535-43c0-a1ec-1044ae915ead", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "0cccb9cc-de30-48cb-a4da-99fa12792862", "name": "inspectionOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "133a79b2-d8de-44ad-ae12-72930a0685b9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "1cd74eb7-7422-48ab-8c23-4a30544ba117", "name": "operationType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "1ed09b8b-161e-4b5d-a92a-2daa925c2ae9", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "20bbfd74-d0c4-48b5-93ac-6e133beb8515", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "26e8d453-ad0e-4ec6-9d68-7da5a2ac3976", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "2f3440e5-b1dd-4e18-99b1-28ff65f6fad2", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "3ed86ca5-6f2e-4109-896f-2512450108b8", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "4721d357-d86c-46c6-9fc7-60f3ac250043", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "48cceb6c-06f0-4e11-be45-442174ddec6b", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "57f34ebd-d85a-46ab-abb1-158786b822bf", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "5a4f9518-5b19-4c0a-a0d9-9852a6e5d068", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "5b552ffd-528e-4ea8-b3ec-30b23ef454e8", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "73c6f677-79d1-427a-95ea-9dfb5f64f33e", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "7d151432-7b5d-44d1-abc5-2529bd7d5094", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "85a9d991-7774-4a78-abea-941fa12ace3a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "8dfd648a-ef55-46de-bf95-b9c21da1d769", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "94112525-e381-4424-b529-36bb27880c1f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "a011b25f-3139-471c-8f62-5fdd1a8eff71", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "a8ebe6fc-1753-43fa-bdb9-e61e5c4a8c4c", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "b7658fa1-d862-4507-b990-5ebf0e0e9895", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "b8255058-530e-453f-9dd7-6744b108bc68", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "b9ccb60c-9561-4487-b936-41e667e5c811", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "e56f6abf-b4d0-4e14-b28a-8bdd79724e22", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "f47dfa02-b1e0-4d96-8efe-4411ab80d124", "name": "results", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}, {"id": "f5eadb0e-b0eb-4094-a60d-b401138c9682", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "83e8c03b-631a-447a-a45a-9472778df1bd", "valid": true}]}