{"id": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "className": "com.open_care.medicalMicro.medical.OCBaseNumberDataTypeExamItemValueConfig", "name": "OCBaseNumberDataTypeExamItemValueConfig", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "数字型明细项目值维护", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "062dfc89-7491-4b86-95e0-d52b2c6849b7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "20462313-7f9c-444f-9a96-bf3e1020360b", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"sex": {"$id": "20462313-7f9c-444f-9a96-bf3e1020360b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "278914c7-a18f-4125-9053-461318ee931e", "name": "legalMaxValue", "title": "合法上限值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "2f369875-2bb5-40a4-bb18-f0231e4a5c1b", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "336deb3a-deb5-448c-99b5-6ee093d2094d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "34957ef4-d001-45c0-b262-2678d5fb6e84", "name": "minValue", "title": "下限值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "44706b74-85de-4d87-9921-5142d487d398", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "4a375cfc-592d-4298-9368-31b5e268f090", "name": "numberDataTypeExamItemSubmitInfo", "type": "java", "classType": "object", "refEntityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "4bdf1508-72a6-450d-a82e-249a88174fa5", "name": "maxValue", "title": "上限值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "4ebb36fc-7f92-4982-9235-1455d5c088ef", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "73968c9c-a874-4f69-ba2a-cd5a0c31f296", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "8319d1f0-7924-4568-a452-523b4bdc7798", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "9001a4c4-6703-4b5d-90e9-a3f9d0b23256", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "a051cf15-d9c7-4d67-a674-0614b4ea3f97", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "a93ddf03-6f9b-443d-9d9b-b679b476c5dd", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "b0723bba-c5aa-4678-b737-c0e99bf77c25", "name": "legalMinValue", "title": "合法下限值", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "bf2eda49-5f99-4c44-8bb0-894e1e512931", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}, {"id": "cbc1c8df-ea29-4c33-967d-134acd4e4a1c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "valid": true}]}