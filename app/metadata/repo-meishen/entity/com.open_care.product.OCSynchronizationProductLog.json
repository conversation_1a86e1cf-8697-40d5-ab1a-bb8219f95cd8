{"id": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "className": "com.open_care.product.OCSynchronizationProductLog", "name": "OCSynchronizationProductLog", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04f204b7-4a31-4a25-939c-2bdba413c32d", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "0cd8d89a-f587-4f49-b5cc-1f6ef6eb1181", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "0fc77e57-df45-475f-8a48-19396f061198", "name": "synchronizationProductDepartments", "title": "同步产品科室", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "177d2e26-cbce-4f05-8b1a-b600a8e77e31", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "20377639-5be1-4875-bd58-a4d58b9b1091", "name": "synchronizationProductNo", "title": "同步产品编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "23719600-71ba-4768-9ddf-240b3ae302e8", "name": "synchronizationTime", "title": "同步时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "3396582a-4b2d-48b6-925e-124ac2d2590b", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "340fc3df-e1b2-4ddf-9203-b5e8ae7d6ca8", "name": "synchronizationProductSampleType", "title": "同步产品标本类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "437b028e-6f42-46e3-83cf-9500b243635a", "name": "theirProductId", "title": "对方产品的主键", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "43eae622-87aa-431b-8a8b-b86a9cfca0ea", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "4c8a9d60-4862-4969-b6a5-e9609ffd7673", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "5c820797-6f4a-4a7c-b731-f559633d0de1", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "6f3ea8e5-cd39-49cf-9ed0-4dc5fe9588b4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "74436620-a92a-4213-939a-19b1a273054b", "name": "synchronizationProductTubeColor", "title": "同步产品试管颜色", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "82eed7a5-5466-4789-bddf-ca40492934b4", "name": "synchronizationProductOcId", "title": "同步产品OcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "85d050ee-9d2e-4a0d-8e0e-7940bb0ecb5f", "name": "synchronizationProductType", "title": "同步类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "8627f43a-0848-4e4e-951d-ea34c963f80e", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "89bb6d36-2c4c-45c7-b4db-9bfb069ba25f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "92218fff-6db0-41f9-affe-e7302b5c6240", "name": "synchronizationProductCategory", "title": "同步产品类别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "a34c385d-7733-431c-a69d-1cb90af9ff92", "name": "synchronizationProductChannel", "title": "同步产品渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "a3b22819-f25d-4ad3-9eed-42e95a8173b3", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "c0a3f085-cb8f-4a98-b604-7942901f15b9", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "db3195e9-a949-4abf-a05e-9144f6ede1a1", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "db87b4e7-8781-48fd-b1ce-9e12b676178a", "name": "synchronizationProductPrice", "title": "同步产品价格", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "e4bf1831-e536-4caa-aca9-4fe0054fc559", "name": "synchronizationAction", "title": "同步动作", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}, {"id": "fba50b2b-90e9-4ac9-a16b-985db168484a", "name": "synchronizationProductName", "title": "同步产品名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c8e98c0c-4195-4a9b-9f97-6491d0525d48", "valid": true}]}