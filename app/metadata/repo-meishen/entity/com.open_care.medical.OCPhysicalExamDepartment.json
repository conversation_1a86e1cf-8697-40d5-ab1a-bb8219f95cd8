{"id": "8bb9e56f-9267-4769-9ac7-369d40f6baf3", "className": "com.open_care.medical.OCPhysicalExamDepartment", "name": "OCPhysicalExamDepartment", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "28c419e1-95b6-47d7-af19-7c294b7138c0", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8bb9e56f-9267-4769-9ac7-369d40f6baf3", "valid": true}, {"id": "70debb81-d3fa-4a2c-b51b-b657a8855104", "name": "note", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8bb9e56f-9267-4769-9ac7-369d40f6baf3", "valid": true}, {"id": "8b78f48b-5b9b-435b-a892-92e66ff4ea23", "name": "sign", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8bb9e56f-9267-4769-9ac7-369d40f6baf3", "valid": true}, {"id": "a6897acc-d716-4548-b493-3703e3ba52a2", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8bb9e56f-9267-4769-9ac7-369d40f6baf3", "valid": true}]}