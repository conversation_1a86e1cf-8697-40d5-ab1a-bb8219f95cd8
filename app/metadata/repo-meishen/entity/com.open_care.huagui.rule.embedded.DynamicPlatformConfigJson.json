{"id": "b0ad962d-632d-4fd1-968b-7336114ea0e0", "className": "com.open_care.huagui.rule.embedded.DynamicPlatformConfigJson", "name": "DynamicPlatformConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "2b5bf1b3-8a60-4625-84b1-e17a7474171e", "name": "targetRatio", "title": "目标分配比例（%）", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ad962d-632d-4fd1-968b-7336114ea0e0", "valid": true}, {"id": "900a605c-10e8-470b-b0aa-562038d61177", "name": "minimumRatio", "title": "最小保障比例（%）", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ad962d-632d-4fd1-968b-7336114ea0e0", "valid": true}, {"id": "bfcee763-b9db-4206-b2cb-3ff1059c48ae", "name": "platformName", "title": "平台名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ad962d-632d-4fd1-968b-7336114ea0e0", "valid": true}]}