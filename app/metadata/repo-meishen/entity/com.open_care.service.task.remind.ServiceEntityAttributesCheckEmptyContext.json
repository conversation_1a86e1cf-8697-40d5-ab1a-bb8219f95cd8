{"id": "19b06930-ac79-41f7-a4fe-b43168b6b913", "className": "com.open_care.service.task.remind.ServiceEntityAttributesCheckEmptyContext", "name": "ServiceEntityAttributesCheckEmptyContext", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02df1311-7dd9-4fa4-bd83-34a4a01d6072", "name": "serviceProductAppointment", "title": "预约单", "type": "java", "classType": "object", "refEntityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19b06930-ac79-41f7-a4fe-b43168b6b913", "valid": true}, {"id": "c53da60f-c3dc-4859-b9cd-e7c1c42424d3", "name": "serviceOrder", "title": "服务单", "type": "java", "classType": "object", "refEntityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "19b06930-ac79-41f7-a4fe-b43168b6b913", "valid": true}]}