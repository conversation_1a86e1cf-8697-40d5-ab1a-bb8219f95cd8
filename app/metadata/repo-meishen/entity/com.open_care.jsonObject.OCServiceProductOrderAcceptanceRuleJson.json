{"id": "71e19e1e-1d78-4456-835b-607274352e2d", "className": "com.open_care.jsonObject.OCServiceProductOrderAcceptanceRuleJson", "name": "OCServiceProductOrderAcceptanceRuleJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "99ccbcff-4084-44cc-96bb-994f35488f0e", "name": "acceptanceRule", "title": "接单规则", "type": "java", "classType": "OrderAcceptanceRuleEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "df0f323da9f380e8eed725a79a2fcce638d38a90", "entityId": "71e19e1e-1d78-4456-835b-607274352e2d", "valid": true}, {"id": "a2273f7b-62f0-49d3-94be-bfe50ee962cc", "name": "dispatchMethod", "title": "派单方式", "type": "java", "classType": "DispatchMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "d4053be6ec38199afbb5487a70f5220c76d2e7aa", "entityId": "71e19e1e-1d78-4456-835b-607274352e2d", "valid": true}, {"id": "d1f705a0-b21b-407e-a723-804b4edbda08", "name": "maxQuantityCount", "title": "可指派供应商数量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "71e19e1e-1d78-4456-835b-607274352e2d", "valid": true}, {"id": "fb8b3e03-8be2-4953-883a-965e7663bed7", "name": "dispatchRule", "title": "派单规则", "type": "java", "classType": "DispatchRuleEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f5b0d4f99aedd6976acb47a39284fa156853d0a", "entityId": "71e19e1e-1d78-4456-835b-607274352e2d", "valid": true}]}