{"id": "8142d390-3f94-4cfa-a4a7-7a9787687123", "className": "com.open_care.product.topmedical.OCMethodContent", "name": "OCMethod<PERSON>ontent", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0cfaf632-8291-4e4b-99b3-17d776a39186", "name": "contentManualEdit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8142d390-3f94-4cfa-a4a7-7a9787687123", "valid": true}, {"id": "69bae94e-9297-4847-81ec-349a986f303d", "name": "method", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8142d390-3f94-4cfa-a4a7-7a9787687123", "valid": true}, {"id": "afb2c443-68a8-47fc-9551-0d3286c5e6d0", "name": "content", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8142d390-3f94-4cfa-a4a7-7a9787687123", "valid": true}, {"id": "b81b37ed-014c-4533-8ded-da6ca4809109", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8142d390-3f94-4cfa-a4a7-7a9787687123", "valid": true}, {"id": "d121c59e-353a-4157-af36-e3fe2117b167", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8142d390-3f94-4cfa-a4a7-7a9787687123", "valid": true}]}