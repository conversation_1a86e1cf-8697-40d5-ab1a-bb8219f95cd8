{"id": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "className": "com.open_care.product.entity.OCHealthExamCategory", "name": "OCHealthExamCategory", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0b5c1889-e855-4877-bdc0-68080e5a9b7a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "13e4df58-1b7f-406e-99f4-b08c3fffdeab", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "30dda5d7-2db8-408e-b45b-42dd60440c69", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "41cf4c5e-bb8b-410c-8475-3f51708593e9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "4a492e17-c451-4e13-97e1-8fa12b59ea5c", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "607b618c-49f1-42ce-8eb0-2e86f7b4b8ef", "name": "productCategory", "title": "产品类别", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "622971d6-b0d0-40a3-8d45-d5619fa77f1a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "64cd407f-104a-4a4d-9b16-35511dfd70df", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "65e7f79a-a397-4fdc-a94a-cd302470f066", "name": "reportTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "660ac1c3-472d-4150-aff0-45b9980483a2", "name": "groupMaxCapacity", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "845e59a8-207e-463b-928d-86d0fa4672cc", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "915bae0a-00bb-4a91-a8a9-dc7e34812960", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "9c9ff48b-275b-4290-8dce-530672e86556", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "9dccf3c6-8df0-4df6-b7b6-ffd955c017d1", "name": "hospitalService", "title": "医院服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "a3d2212b-8827-45ba-b222-c5259502941f", "name": "contract", "title": "合同ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "b3c57cf1-8048-4d14-a85f-b2cf61bc2ff8", "name": "businessHours", "type": "java", "classType": "object", "refEntityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "b4d5479b-1d54-4416-b6e9-7d5ce1bc48d2", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "b680ec3f-d8eb-442b-b707-7adb1fcac5e6", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "d21b2697-3d2b-408d-9573-c4a2cae411d8", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "d3154812-0fba-4966-a580-088337a896cd", "name": "party", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "d3f3aa1b-40de-4681-ac7b-060a74889384", "name": "reportForms", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "f043d17c-e4e0-4d59-8423-baa8342cbc4e", "name": "partyRole", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "f6a89fe4-9ffd-4bc9-8e60-5e38f3ca1fea", "name": "internalService", "title": "内部服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "fa64bc6e-1a88-431f-8849-fc4c542dec5e", "name": "receptionPlace", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}, {"id": "ff2dda05-f4b4-47ac-b5d1-ba0c0e4ae600", "name": "specialInspectItems", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "bc2d5200-4aa0-4e2a-825f-8f61f2e1d6de", "valid": true}]}