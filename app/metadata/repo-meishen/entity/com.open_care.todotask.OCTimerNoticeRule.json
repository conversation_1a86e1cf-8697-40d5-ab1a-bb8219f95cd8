{"id": "75522841-e879-444d-b4d4-cc585b0531cd", "className": "com.open_care.todotask.OCTimerNoticeRule", "name": "OCTimerNoticeRule", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "6a10c75d-4e17-45cb-8093-264797674eaf", "name": "unit", "title": "时长单位", "type": "java", "classType": "OCDateEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0fc8251c8ef79ec8fd93b3933be2a117c57c26f3", "entityId": "75522841-e879-444d-b4d4-cc585b0531cd", "valid": true}, {"id": "d09e9d6f-6cc7-452d-9dc0-58e4117c385f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75522841-e879-444d-b4d4-cc585b0531cd", "valid": true}, {"id": "f890e6e7-e123-4fff-82b1-b258d65df56f", "name": "duration", "title": "提前的时长", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75522841-e879-444d-b4d4-cc585b0531cd", "valid": true}, {"id": "f8bb50ba-4a29-4d75-8deb-b3748e029715", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "75522841-e879-444d-b4d4-cc585b0531cd", "valid": true}]}