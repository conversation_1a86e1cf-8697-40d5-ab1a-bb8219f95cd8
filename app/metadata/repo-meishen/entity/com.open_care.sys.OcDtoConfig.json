{"id": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "className": "com.open_care.sys.OcDtoConfig", "name": "OcDtoConfig", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04b630d0-82e4-4b3f-9da7-66c58a70c417", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "12617598-3659-4e50-b468-d933e6b894e0", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "4ff980ab-4bdc-4995-a566-a027e218eb2c", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "5ef6d599-9152-4aad-85b3-931e10090be3", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "6c762f4a-2880-45a9-af37-0d56f0e227d4", "name": "dtoName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "80d277e2-f924-4efd-8341-5a92d772deb8", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "86c20035-3ade-44a4-884b-8824f7a506dc", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "b51b7d25-027b-4092-b40e-4208efa08a41", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "b8414685-8d58-4ca2-b816-046349a3a224", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "bb9877e6-d8d4-42a1-9574-0e5ea9b6d98a", "name": "microName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "bef3beed-b96e-4a90-bd7e-0256849d5d50", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "c836d593-e0d6-40c6-88a4-5550969d85b2", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "c836d593-e0d6-40c6-88a4-5550969d85b2", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "db80fa9d-8af2-4b3a-b9be-726bdecb41f0", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "e381ad9c-0c95-4bff-b07e-0b8e51834354", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}, {"id": "ed9e5846-67e0-4fca-a814-8a32aa60ad54", "name": "supportMethods", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a017ffef-81ec-499a-bb3c-6b7dfb4551f9", "valid": true}]}