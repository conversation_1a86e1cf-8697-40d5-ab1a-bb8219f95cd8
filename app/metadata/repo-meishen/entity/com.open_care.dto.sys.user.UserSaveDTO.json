{"id": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "className": "com.open_care.dto.sys.user.UserSaveDTO", "name": "UserSaveDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "09c8d0c0-af55-4ba9-81b1-e1e245932d46", "name": "serviceCampusOrgs", "title": "工作院区Ids", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "1bee4dff-5ae3-49b3-babb-fafaa925dab6", "name": "roleNames", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "283662b9-46b1-46b3-a77a-e61b1df5b905", "name": "status", "title": "状态", "type": "java", "classType": "UserStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ace4a7424bc8cce6f5a88fdf91813503246d032e", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "2a0e472f-2cb8-4503-abbc-6f3bea0d2d19", "name": "groups", "title": "所属小组", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "3720cfe0-0493-4c4e-9202-8dce9d17225a", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "422251e6-ea19-4176-b0c3-07620deb85a7", "name": "roles", "title": "角色", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "3c7e0003-4d5e-4e73-a79d-abd40d00249b", "style": "Select", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "properties": {"roles": {"$id": "422251e6-ea19-4176-b0c3-07620deb85a7", "type": "standard", "title": "角色"}}}}, "valid": true}, {"id": "4b65951d-65c3-49a3-9b84-dadda30cc461", "name": "groupNames", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "4ecaa75e-c286-4c82-9c01-350c97e13f54", "name": "username", "title": "用户名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "51dc6b4e-d064-4427-98eb-38a39761581a", "name": "certNo", "title": "证件号码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "5b803081-688d-4d69-8aee-b679505362e2", "name": "subsidiaryOrgs", "title": "工作机构Ids", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "61a4d9a5-0ada-4974-979b-788b1e357602", "name": "password", "title": "密码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "62d1ae8f-a7ea-4e8d-8d1c-f44507eb561e", "name": "name", "title": "姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "62fa9de4-3e90-4177-a61f-64ceec816099", "name": "superiorId", "title": "上级用户编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "68a112e2-5b80-4517-840a-b846d130f6c7", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "6a9585eb-4510-4c0a-8218-b587f7aa061a", "name": "workSubsidiary", "title": "工作机构", "type": "java", "classType": "object", "refEntityId": "5787a7be-d490-4092-bddf-984f399da6b9", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "6eba671d-b1be-4a03-b0ad-dc3fcaabd561", "name": "phone", "title": "手机号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "72522e3c-ae73-4fd3-86e0-f33a4e7161c5", "name": "hiredate", "title": "雇佣日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "76fb79f2-777a-4973-a588-4173b7816ba4", "name": "userNo", "title": "用户编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "7715513d-45a7-4166-88ac-1709c2a98e67", "name": "certType", "title": "证件类型", "type": "java", "classType": "CertTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3c0c713f4a40c01d2c9a28a771ee45aafb43e943", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "7c4d8b3c-2e8b-4ec1-8f39-d50eb25a6b22", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "85c08c6c-9c06-4bf7-8e99-e770ebc0ca4e", "name": "ownOrg", "title": "直属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "properties": {"ownOrg": {"$id": "85c08c6c-9c06-4bf7-8e99-e770ebc0ca4e", "type": "standard", "title": "直属机构"}}}}, "valid": true}, {"id": "8cd1a03d-c1dd-477c-8fbb-36beb2546d09", "name": "qualifications", "title": "资质证书", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "938706e3-22bb-4496-b2fa-164627d8bb5e", "name": "identity", "title": "身份", "type": "java", "classType": "IdentityEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "91706b564484367b04c6a20431ef88b086204d8c", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "b376451e-ba4a-474d-be4a-14636ba22032", "name": "roleIds", "title": "角色编码", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "b7464dfb-2de6-4132-90eb-de45ea4d4b18", "name": "employeeId", "title": "员工id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "ba99b2c1-af5a-48dd-8f58-a26a95384697", "name": "userType", "title": "用户类型", "type": "java", "classType": "UserTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "94ccd4b0f2b0b3a886ae3077300a61dd4087c70d", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "c50df56e-89a7-4b99-a071-dcc3758d1786", "name": "branchId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "c6665069-ac3e-4b78-8591-f29baea221a3", "name": "ownOrgName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "c92b0008-6902-435f-9459-a4252111da5f", "name": "workCampus", "title": "工作院区", "type": "java", "classType": "object", "refEntityId": "5787a7be-d490-4092-bddf-984f399da6b9", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "d5211683-621a-4c72-88d3-1b26dd71863f", "name": "attribute", "title": "属性", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "d84ffeb1-19d5-4d8d-bf37-cf7e4f760b1c", "name": "supplierId", "title": "供应商Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "e26fa1f4-ef9d-4b80-a3d1-bbc71a35eb61", "name": "email", "title": "邮箱", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "e41e75f2-dba5-4ad8-bd23-c6846e2386c0", "name": "superior", "title": "上级用户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "e6da9fed-f0c9-4a9d-b799-e8f9810f1a99", "name": "sex", "title": "性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "ebc6cab0-4d70-4ec0-acf5-39b2159bb3f9", "name": "superiorNames", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}, {"id": "f7fde357-f26a-4d6b-9d19-4396c052ea05", "name": "extensionNumber", "title": "分机号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "861e6cc4-3e63-4418-b0de-66cc2df3abe9", "valid": true}]}