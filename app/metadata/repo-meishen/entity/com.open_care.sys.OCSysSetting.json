{"id": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "className": "com.open_care.sys.OCSysSetting", "name": "OCSysSetting", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0b2efbc0-cd30-4876-8ab9-ee18bd46a136", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "1874d820-b7b0-4507-8170-769c198a8dda", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "1f24a650-de3b-4e10-85a1-ecdc2d0e2b84", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "31b36a8d-04a6-4b0a-a3b3-7b54b48adb2a", "name": "settingType", "title": "分类", "type": "java", "classType": "object", "refEntityId": "6b07a376-8b09-4c66-96af-d1c366ad79de", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "d4e7dac3-9517-4ae1-a012-0f545be8ab06", "style": "Select", "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"settingType": {"$id": "31b36a8d-04a6-4b0a-a3b3-7b54b48adb2a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "分类", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "48f67159-f659-4afa-a007-d7242e3ed402", "name": "paramName", "title": "参数名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "511d7e6a-e995-4d6e-b4ba-b29d854c9957", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "51d9e1e8-c467-4189-8b53-8a3a3749d203", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "64639347-ba52-4cd2-8b8b-8bee4cbd6dc2", "name": "webUse", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "692e0214-7e34-42e4-b71a-f56fc389b86a", "name": "suitAppName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "75471e2f-2fca-49d1-9a11-7ac321da7d32", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "9add290c-3fb3-4663-849d-8e295125f603", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "ba477f79-5485-4865-87d9-9d857963eac3", "name": "paramValue", "title": "参数值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "dfa5d5b1-f9a5-4142-8bd9-a399dc9e8a98", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "e0d79f4c-c50b-4972-943c-b5f0b3dbaa2f", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "eeed0a8d-ecb4-42de-ab43-df9edcadc032", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "fb9c955d-a828-4f73-8019-c52180af2ac0", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "fd5a520c-e93c-4c07-869e-c1e396cf7744", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}, {"id": "fdb7bdf0-aab0-489e-b625-63d451317de2", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee9deaf4-74e8-41f6-b099-ff33bde8ee21", "valid": true}]}