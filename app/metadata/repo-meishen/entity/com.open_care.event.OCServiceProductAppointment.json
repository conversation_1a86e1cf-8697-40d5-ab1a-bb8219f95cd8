{"id": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "className": "com.open_care.event.OCServiceProductAppointment", "name": "OCServiceProductAppointment", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05f6d61c-ed32-4bbb-b0ff-2531bb6db216", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "08e6871d-447d-431c-bb88-92adb12bc878", "name": "reservationNo", "title": "预约单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "0b938abd-b838-4a3e-ac12-3f0f00fdb197", "name": "productAppointmentAndResourcesInfos", "type": "java", "classType": "object", "refEntityId": "25d17baf-f41f-4f9b-a4ed-088b80820dc4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "0cad4e4d-4375-44a5-af7f-6a8e5806993c", "name": "applyTime", "title": "申请年月日", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "0ccec1a9-b14e-4c7a-8e05-7ec17a2b8150", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "0d9eb93d-34e4-499c-9d76-02551049ca22", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "0e98dfc9-c999-40b5-ba67-c90980805613", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "10b03429-5cb2-4073-a5eb-ead3b8086886", "name": "actuallyBeServedPerson", "title": "实际被服务人", "type": "java", "classType": "object", "refEntityId": "b011f8ee-475f-46b1-b3a5-e18c99ca01b7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "1326361e-302d-421c-af0c-f65c60199bc1", "name": "registrationDate", "title": "登记日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "1365aa96-ae08-4b3e-8fc0-b0aaf2f37e4a", "name": "detailed<PERSON>ddress", "title": "详细地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "1502f0bc-629d-467d-b1c8-8149b56a129c", "name": "hospitalizationForm", "title": "住院单", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "16b1ef85-b6f7-418d-a666-55c2d3c7d4c1", "name": "orderReceivingArrangement", "title": "接单时间", "type": "java", "classType": "object", "refEntityId": "b9c82f3d-5353-4b72-a04c-3a3d2f1be0cb", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "192cb461-0f94-468f-8a96-958ca664dcf9", "name": "serviceBindId", "title": "serviceBindId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "1ead1171-7bf9-4782-996c-b22e3b162998", "name": "operatorIsUser", "title": "操作人是否是使用人", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "1ee5764b-7cee-454e-b333-d33bdea5c02b", "name": "serviceCharge", "title": "医事服务费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "1f997dba-1c88-41fc-8496-3d517fc33849", "name": "baseAttachmentDTOList", "title": "附件列表", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "225324f8-aaca-4d77-a356-7ff374d70323", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "22e15839-1c7d-48fc-a083-30e4ea52bf65", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "240af9cd-ccfe-446f-acce-d2adc9a2016e", "name": "visitingHospital", "title": "就诊医院", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "24bc9041-3457-4f37-9a35-11221ff9baa5", "name": "appointmentNumber", "title": "预约编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "28b22f1a-73b4-4807-aa31-a9550f150c21", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "2d7d5533-3736-41f3-ad9d-24fb1652c506", "name": "processInstId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "2edb1f05-272a-4883-ac9b-01105ddf2a48", "name": "disId", "title": "权益订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "2f941276-8f21-495b-ae35-cb386cc946a0", "name": "whether<PERSON>ccomp<PERSON>", "title": "是否需要陪诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "303e3d04-71c4-4171-9959-59a75f6f651e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "377cfc3a-46c4-4629-a7cb-7bbdad0724f7", "name": "recommendHospital", "title": "是否推荐医院", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "3cb1274e-907c-4bbd-afa2-dcd46d9f958d", "name": "samplingRecord", "title": "采样记录", "type": "java", "classType": "object", "refEntityId": "af4932cb-1480-4eba-9c81-8e8ad808a5ce", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "3d6a9d2e-a445-472c-8b99-95d3d9f93c53", "name": "appointmentGroup", "type": "java", "classType": "object", "refEntityId": "927fc9ac-dc42-44fe-ba55-21764a22c955", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "3e5a44ea-881e-42eb-8fbf-e1cb151954a0", "name": "hasMedicalInsurance", "title": "是否有医保", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "40611811-14b7-465b-9e3b-b8bf0acf3cc9", "name": "surgicalDrape", "title": "手术单", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "4987fb71-3f74-4727-a594-570a39660042", "name": "updateInfo", "title": "修改原因", "type": "java", "classType": "object", "refEntityId": "6b6ba6f8-0f62-4641-b254-2e2f91858f69", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "4fa961c6-8d9d-466c-8c1c-8827a18e8616", "name": "confirmTimeout", "title": "确认超时", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "5018c468-0490-48b1-abec-a9d96dc44799", "name": "followUpResults", "title": "回访记录", "type": "java", "classType": "object", "refEntityId": "0a66bbd3-3e23-4ca7-9828-33a3da8f08df", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "504637b2-e926-4311-ac25-712c255d1a6d", "name": "description", "title": "描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "50743ec7-acd4-43d8-8744-2ee6ea9fa4e4", "name": "serviceArrange", "title": "实际就诊安排", "type": "java", "classType": "object", "refEntityId": "461fb641-1508-4876-9110-19367cdaec61", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "5345fcca-9d7b-4d3b-87c0-f01ea7de62f1", "name": "acceptOrdersTimeout", "title": "接单超时", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "5734841d-cb85-4cb3-b909-98621fb106c6", "name": "accompanyingType", "title": "陪诊类型", "type": "java", "classType": "AccompanyingTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a2c5dca81ba6838bfa411f071f114cd3a5168053", "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "58004915-f4c8-4bab-bfc8-742fe1d62f1b", "name": "svcCode", "title": "服务产品code", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "582fc974-dc0b-4fb4-8daa-02fba9a7e198", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "5892e2ac-0fed-46b4-8972-e97321e1ac62", "name": "attachmentDescription", "title": "附件描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "606b7a08-ec72-4d4f-8165-45cd2202331c", "name": "consultationInfo", "title": "实际被服务人咨询信息", "type": "java", "classType": "object", "refEntityId": "d06e5d12-fa02-417b-b5a2-b8863d9ad512", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "61a01bd4-922e-4efa-b2a7-744031619391", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "上门地址/送药地址", "type": "java", "classType": "object", "refEntityId": "e9097e72-d407-4a68-b6ca-94065614e460", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "6238da09-7b48-4d01-87da-55295e0365be", "name": "incomingCustomerPhone", "title": "来电人手机号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "64f7173c-b702-44a2-a1b3-1416074b871f", "name": "attachmentList", "title": "附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "764f775e-0237-4188-83da-76b900f0a7a8", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "7a149158-8d8c-41bc-a926-992ca7968147", "name": "content", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "7aa5f293-defa-455c-b039-12fe98146e84", "name": "contactPhone", "title": "联系电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "7dae8809-5621-4695-995f-45d8fe7a8091", "name": "accompanyingInfo", "title": "陪诊信息", "type": "java", "classType": "object", "refEntityId": "20186c59-54ac-4602-841b-c7edf420a1e4", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "81553cc2-b360-4fd7-a1d4-719dabcdb76b", "name": "sessionRecordOcId", "title": "会话OcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "82261af3-504d-4646-9442-b2eccfc54f74", "name": "auditStatus", "title": "备注", "type": "java", "classType": "OCAuditStatusEnum", "refEntityId": "9a815837-52c2-4449-9fdb-c0271fd3c337", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "83555e41-e8d9-462c-a9b6-cec2d2abb3fc", "name": "serviceProductAppointmentItem", "title": "操作项目", "type": "java", "classType": "object", "refEntityId": "62091fd3-bbf9-466b-b3cd-ca7c4d237b01", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "880ccd0d-b206-4a7f-a3b9-89ba37dbb7ed", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "88943ecc-bbb8-42c7-b9a5-cf87e5109ddd", "name": "appointmentStatus", "title": "预约状态", "type": "java", "classType": "ServiceAppointmentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "fc23db33da779da0582a09c0cc025273144ac40c", "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "89806318-d782-4103-a737-ee3a04592e96", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "8a8a1ad8-bb53-409f-bc51-393bb030cabc", "name": "inHospitalSamplingInfo", "title": "预约院内采样信息", "type": "java", "classType": "object", "refEntityId": "e5a6d1d0-25ca-4de0-b3e4-f63186ab20e0", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "90d814cf-7fa5-42ba-af64-20bf6ac78dca", "name": "appointmentDate", "title": "预约日期", "type": "java", "classType": "object", "refEntityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "967f26c8-609d-4954-8cf6-ba85ebf587ee", "name": "cancelInfo", "title": "取消原因", "type": "java", "classType": "object", "refEntityId": "c1fb0ab5-64ec-40c8-8e0e-f3a4340d137e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "9777df20-5714-4f6d-b5f3-3dbf476108d4", "name": "orderIds", "title": "订单ID", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "9857da9f-df01-4552-b2dc-834755beea1f", "name": "pendingPriority", "title": "优先级", "type": "java", "classType": "ProcessPriorityEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ebd12d3e01d79d6377c2ebd9af6b5ae07f81138c", "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "a5c7d366-ace7-478d-b28a-0824fa69a60e", "name": "orderReceivingTime", "title": "接单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "a891e890-32fc-4123-b49f-88534363dc09", "name": "channelTypeEnum", "title": "预约渠道", "type": "java", "classType": "AppointmentChannelTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "d8ac03547908f23f4d330a95423bbd98ada5be62", "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "acecb536-1639-409d-add0-5be1359de796", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "acf996da-5830-4a7b-bffc-b4e3b598e2a6", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ae1926b1-a20c-418b-b92f-c08c99973d68", "name": "auditInfo", "title": "预约编号", "type": "java", "classType": "object", "refEntityId": "40cba2ee-4516-47dd-9394-542dc56673cb", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "aeb5b388-b198-49e8-a1b4-7342e71259c4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "b1e01249-2122-4a93-9cc3-0576a4a121d5", "name": "customer", "title": "客户信息", "type": "java", "classType": "object", "refEntityId": "b011f8ee-475f-46b1-b3a5-e18c99ca01b7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "b3ba13fa-0844-4932-815a-dc1fb3f636fb", "name": "process_attachment", "title": "附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "b8900ceb-4773-423f-8236-781debb97ccd", "name": "incomingCustomerName", "title": "来电人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "b9cef885-87af-4bac-9b15-29164a796fc8", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "bd5c20b3-33be-4fea-b965-0f65532456fb", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "bd5f8173-6c47-4d8f-bf93-79c5d9606a60", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "bdadfb18-da8b-44cf-b3e5-c2ced31c34d1", "name": "specialRequirement", "title": "特殊要求", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "bdef9462-5ac7-45fa-987e-d7a81c89d83d", "name": "serviceOrg", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "bf74d8dd-cc54-409f-b217-b4d6709a1373", "name": "appointmentTypeEnum", "title": "预约类型", "type": "java", "classType": "ServiceAppointmentTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4e749752f5c32328169e57ece6cebe1e85909963", "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "c74a4003-6a3e-475d-b85d-3d10110dfa53", "name": "serviceSupplierId", "title": "供应商id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ca62518a-3b62-48ae-b5c5-32275edeeedb", "name": "expectServiceArrange", "title": "期望就诊安排", "type": "java", "classType": "object", "refEntityId": "4f4c423d-483b-4a26-99b1-8c02cd15b98b", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "cbe3ff71-6db9-4555-aa0b-fe6562538801", "name": "timeout", "title": "超时", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "cd8c5af3-7871-4eb1-bfe1-ffbe7f942b2c", "name": "onlineInterpretationInfo", "title": "预约在线解读信息", "type": "java", "classType": "object", "refEntityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "d43e379c-e85c-4bf8-b804-7e4ebb6676d8", "name": "visitingDate", "title": "就诊时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "d9017175-df69-40fd-b9cf-e7dfbeeba979", "name": "contactName", "title": "联系人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "d9f5bd41-c8b2-4ac4-8315-18526fc1babc", "name": "<PERSON><PERSON><PERSON>", "title": "预约名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "e1a46541-09da-4589-97ac-7e1eb21b7c56", "name": "name", "title": "指定使用人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "e3910733-4e52-43ad-bda8-06650cec4b8d", "name": "applicantCustomerId", "title": "申请人ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "e871f5e2-944b-4d27-a4a5-8670c9cee735", "name": "expectVisitTime", "title": "期望上门时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "eac74e39-f629-418c-a3fa-aee62ba2c728", "name": "deliveryAddress", "title": "送药地址", "type": "java", "classType": "object", "refEntityId": "e9097e72-d407-4a68-b6ca-94065614e460", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "eaf02edb-b434-4a0f-9b7d-42f57799a2aa", "name": "prescription", "title": "处方单", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "eb29711e-0600-4463-ac07-eaeda3691147", "name": "incomingCustomerNo", "title": "来电人客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ebb5972c-d674-4ad4-a0e0-292cf3d609a9", "name": "serviceCount", "title": "总计服务次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ec44aa92-bc56-4203-94fd-9a4b517800f5", "name": "productName", "title": "产品名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ec797844-0a33-4dda-9b5b-ecd1ee2657f8", "name": "visitingDepartment", "title": "就诊科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "edad750b-ab85-4522-9ce6-58dac6fd4aae", "name": "storageCertificateRecord", "title": "储存证书信息", "type": "java", "classType": "object", "refEntityId": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ee0717d5-c22c-4278-ac50-188eedcc79e6", "name": "policyholder", "title": "投保人信息", "type": "java", "classType": "object", "refEntityId": "b011f8ee-475f-46b1-b3a5-e18c99ca01b7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ef56f2da-aa1e-489e-8188-d509819fc089", "name": "communicationRecords", "title": "沟通结果", "type": "java", "classType": "object", "refEntityId": "f5306f66-bd31-497d-b483-fac8b547fca2", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "ef9b6fa7-40d3-43f4-a699-4f3d0cd19284", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "f154c7a2-8c69-45a5-b22e-96a0948714e2", "name": "cancelReason", "title": "取消预约原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}, {"id": "f660851a-2e91-4b6a-9066-6e9a5653561b", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "valid": true}]}