{"id": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "className": "com.open_care.invoice.OCCustomerInvoicePrintInfoLog", "name": "OCCustomerInvoicePrintInfoLog", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1c84273c-e9c9-4a4e-abb4-8e5cc3b24d17", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "27cfd215-4aec-4ee5-bd29-8fe95054e6df", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "3831f7b9-1c5a-46e7-b293-1b0de270fb4b", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "4186c88b-7fd1-48fd-a505-b9bc913bb5a3", "name": "orderOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "4c39350e-2533-47ae-8cf5-6b0888c8a0f0", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "4d33c864-a38c-4d90-843a-67f1bd885fc5", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "4e23689e-bca5-4081-9f74-3d970853da39", "name": "chargeTicketDtoStr", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "5495666c-3b96-4ff6-8047-af797cbe3273", "name": "invoiceNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "5af10e35-cfa1-4c27-acf0-0b72a729c5df", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "8965c529-1fd3-4e6f-9872-1a42ba05b17f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "90e8edfe-3220-4ddd-965f-ea8366a228a0", "name": "orderNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "a57d0623-f32a-4795-a26d-72f764966f06", "name": "partyRoleOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "b80b231b-3d10-4e1c-a82f-0d288e1fa4cd", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "c2c1369a-d448-4f2a-92c0-ae47232fbbe3", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "ce9f75ed-352d-4966-bc08-95acc143fa16", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "dde57b45-c157-45e4-8e23-0601915e5ea1", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}, {"id": "f86780ce-fd62-4472-a835-337d39e6b54c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ae99b6e4-7a18-417c-b406-4d620b00b5af", "valid": true}]}