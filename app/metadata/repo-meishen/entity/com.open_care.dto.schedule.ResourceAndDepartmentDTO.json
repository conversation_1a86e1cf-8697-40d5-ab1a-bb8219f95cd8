{"id": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "className": "com.open_care.dto.schedule.ResourceAndDepartmentDTO", "name": "ResourceAndDepartmentDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "063b9876-408c-4ebd-b03f-a8fc7596e7cb", "name": "resourceName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "valid": true}, {"id": "0923fe9a-8eea-4ab0-8d6c-dd721ed14612", "name": "resourceCategoryName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "valid": true}, {"id": "1fbc5976-32e8-4f0f-816a-f1dd170bf670", "name": "departmentId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "valid": true}, {"id": "4b076d7f-8a4a-41cb-a687-52c5a2f53a4d", "name": "resourceId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "valid": true}, {"id": "87d1bad5-3b6d-46e9-b09a-ddf4b707d2c8", "name": "resourceCategory", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "valid": true}, {"id": "e3e310ed-fd06-44f6-9dbb-694354fa3678", "name": "departmentName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "valid": true}]}