{"id": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "className": "com.open_care.medicalMicro.physical.OCBaseGuidanceType", "name": "OCBaseGuidanceType", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "指导类型", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "23a7ef0a-830b-4a1c-86a0-68e6d99e2c09", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "5cba210b-9080-49b0-aeb5-681c309961b3", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "6bfbe57b-b884-450e-8b1b-9f71895d5465", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "6d7631b9-b6ee-4956-a3ed-f75f760ca459", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "6db85043-a16a-4b63-ad53-46bca8fecd1b", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "6ecab365-2578-4784-a7a6-3551a0924d23", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "7cecb0d3-77b1-4ac6-86af-a2d77b80251a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "81e12fa9-bcda-42e5-9f13-80aa175fc8a7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "904f5362-7161-45e2-8456-e9cd97b8dd74", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "9722dcee-9fed-49b2-83b2-dbe79c4a2e60", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "991d331b-50ee-4cfd-ba47-088bd3c628d2", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "991d331b-50ee-4cfd-ba47-088bd3c628d2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9f75dd1b-e6e4-4d5b-849c-99e9ed89f97a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "a3108c31-9a44-4ef8-a647-9c95a177b75c", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "a99a6338-755f-43e5-b8cb-69c94ff0f9a5", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "ac7fb753-822d-4ab4-b011-a1297e554967", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "af1b60c3-4f69-437b-bb7d-93ca8ecd1bbe", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "c184ed89-b13a-400f-903d-d0cd19238de8", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "cd414928-f8e9-4edd-b1c6-9879fc071a15", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "e1f4cd4e-acaf-436a-b3cf-36d5fb76d7e1", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}, {"id": "fad24cc7-b522-44d2-b131-0536f7a757c6", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d2efbe5-6eea-4b50-bf84-b34a0589d0e1", "valid": true}]}