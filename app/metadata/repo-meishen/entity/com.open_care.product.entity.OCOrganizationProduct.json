{"id": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "className": "com.open_care.product.entity.OCOrganizationProduct", "name": "OCOrganizationProduct", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0328d7c7-dd80-4eb0-8442-f067d06beff9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "2a23393c-5522-489a-953b-21033c6d5668", "name": "organization", "title": "对应机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c69a5bbf-fac7-4a47-940a-1d841add43gg", "style": "Select", "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"organization": {"$id": "2a23393c-5522-489a-953b-21033c6d5668", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "对应机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "303b18f3-b1e0-48d5-95dc-26d4dd2117da", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "347bc028-04d9-4e57-b37f-e6de0fa2918f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "682c114d-bb6d-4d75-ab83-9fe3b05fd178", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "6dbab3cd-8e1f-4a8e-acb2-b89d29cbb692", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "70bb0c0a-d15c-4a85-86fb-f5055aa577af", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "72280ed0-51da-409f-b8f2-fd4e3a42ffcb", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "74185258-72f1-4b86-a371-f554c7256974", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "74a0e611-3a23-4891-8ee2-7efe6eb74a9e", "name": "interfaceName", "title": "对应接口项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "775660f0-1ce5-47cf-913f-5f126932b318", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "afcaf76c-530c-4140-acc6-37ec2fcecd9e", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "b5924409-8338-4ccf-8f3e-ca2dc03ac107", "name": "interfaceCode", "title": "对应接口项目编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "bd844558-9d8c-42a3-a940-0f07fa81c263", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "cfb62cd5-24f7-4bfd-a866-c2cdfae113cc", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}, {"id": "d14dcdef-f260-4ef0-a0bf-eecbf9fcda8b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "valid": true}]}