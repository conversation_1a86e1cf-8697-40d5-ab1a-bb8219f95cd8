{"id": "41875607-e8d9-4228-9b63-de1c8cd1571d", "className": "com.open_care.medical.DepartmentUserAuthority", "name": "DepartmentUserAuthority", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "科室人员权限", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0f80fa7d-7dab-4487-9883-97bcb2a10695", "name": "auditable", "title": "可审核", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"auditable": {"$id": "0f80fa7d-7dab-4487-9883-97bcb2a10695", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "可审核", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "28c35570-d557-4d02-b11a-334a7796d428", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "35dd2fd6-5285-4c38-89ca-0257b05cbf42", "name": "department", "title": "科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"department": {"$id": "35dd2fd6-5285-4c38-89ca-0257b05cbf42", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "430edf88-45e0-4c76-8600-a4fd5b0126a6", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "45cccf00-ed0d-4831-b1d0-d99d7de09800", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "665388d9-0b26-4202-b4bb-77107ecf7aa3", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "667828b9-a7bb-47b4-8e25-e9b0511784e5", "name": "user", "type": "java", "classType": "object", "refEntityId": "90cf3f0e-3cdc-422b-bdbb-77b2e7579202", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "7dcdcb89-55d0-439b-a056-ede12a844c43", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "b365036d-f303-45fc-972b-da8dfba781f9", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "b9b276c1-8632-4420-8c25-94b74c7a07cc", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "c35ee1a3-c2e1-493b-8f5e-098dcce2b8e6", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "c6d7f6dc-f089-4768-afb3-0bb98772c0a4", "name": "recordable", "title": "可录入", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"recordable": {"$id": "c6d7f6dc-f089-4768-afb3-0bb98772c0a4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "可录入", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "caa5c0a7-59fd-4ab3-9cc9-132a67cf5999", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "f175b4c1-453d-4f0b-9c20-1ebb5d7793d6", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "f7070d0d-fa5c-48a2-a48f-a0b7481d80a8", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}, {"id": "fe748413-defe-43bd-918e-daa3fe22291f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "41875607-e8d9-4228-9b63-de1c8cd1571d", "valid": true}]}