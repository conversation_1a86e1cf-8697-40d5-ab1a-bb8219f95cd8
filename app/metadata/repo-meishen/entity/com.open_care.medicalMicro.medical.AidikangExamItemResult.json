{"id": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "className": "com.open_care.medicalMicro.medical.AidikangExamItemResult", "name": "AidikangExamItemResult", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "07976fba-49d7-41c4-9d15-ddfade163d23", "name": "ResultUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "15a5bc17-ea23-41ea-9f96-36103ce028b7", "name": "Instrument", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "16b1223e-019b-42bd-a6c3-d1a613383569", "name": "Str5", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "174713c5-b441-47d7-96e0-8c6bd146aac5", "name": "SerialNumber", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "1de6358e-175c-4c52-9d26-61fc082f73df", "name": "Result", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "1eaf3fa6-958f-4d1c-89bb-0054e263e7a9", "name": "LisDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "2cbaff8b-a56c-49a7-8506-bce371223d09", "name": "ClinicalDiagnosis", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "3018165c-f138-4b17-8bf7-9fc251e2afa8", "name": "ResultHint", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "493db6ed-587d-4945-9800-a6db81aa0975", "name": "Technician", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "4dcddb10-b889-4b12-8673-2b6967e88eb6", "name": "Str2", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "51a200b1-871f-4b50-b560-dfe3eb906013", "name": "Doctor", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "55f22e75-961b-4b0a-bf37-e4d5dd2dec1f", "name": "AgeType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "6842af3c-0793-4ea6-bda2-c17f9334a598", "name": "Str3", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "6f37d3e7-4c8f-4d52-97af-9e4b3b0c3cf6", "name": "ItemName_EN", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "6fc006a8-3f40-4c84-8e7f-c46e46f05837", "name": "Checkedby", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "77a7e0d7-6183-45fb-a09a-f45e35e2a500", "name": "Result_Pathology", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "79474029-a412-468f-adb9-19a3a0522f48", "name": "AdiconBarcode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "7bff7b3f-7eec-4af0-9944-9fb09fe6192f", "name": "PatientName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "82f14b7b-1fd4-4f88-a942-f396aa9fd47f", "name": "ReportDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "831fa46b-ffdd-4057-8cbf-b71c0999e5b9", "name": "Str1", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "8a0ab9bf-cd4b-4719-ac84-298ac7e3b050", "name": "Department", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "958cb204-ce1f-46d1-9414-7cb52bc56f7f", "name": "ResultReference", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "9a19d7a9-43fc-4c01-8990-18d1762af3bb", "name": "Str4", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "9eac3473-c205-48a0-8bb2-0400ca9a2be4", "name": "Remark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "9ff661a7-4ad0-4e57-8a7e-b888c3211627", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "a4145355-912b-4416-8ab1-b930c9c2d060", "name": "CollectionDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "aa27fe6d-ac63-4071-aae5-46efb1b57c06", "name": "ItemCode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "af870c7b-7cbb-4b59-bc01-80817cb9452d", "name": "TWBG", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "b6ebc33b-60f8-4ef6-a2e2-9162f4cfa4b5", "name": "detailMsg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "ba1d3783-40c4-4629-8310-d9b3d382a069", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "c26ee37d-0ad0-41f7-add3-6509269129bd", "name": "Age", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "c723c15a-8ca2-44a3-91fd-603d8db0b086", "name": "TestMethod_EN", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "cc0ba9c4-ec98-4631-ae44-8f56db530cc6", "name": "CustomerBarcode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "cf2b9847-8768-4da2-a42f-916cee1a04df", "name": "PatientNumber", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "e264485e-3835-4228-b902-4b0345bf6c3c", "name": "ReceivedDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "ee2f501c-be66-4cf6-88ac-b8d8425947c0", "name": "SampleChar", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "efa40b2f-ef0d-4c8e-9ae8-2922ba4087be", "name": "TestMethod", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "f1f023a4-dd81-497d-8727-316a943c0984", "name": "ItemName_CN", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "f25a7cc9-5c68-4e0f-9b31-3864cfa566a9", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "f5b360ee-d68c-4b79-b8ac-10d5c96337fd", "name": "PatientPhone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "fb1ff0c5-a24d-4769-a878-94c3bba43101", "name": "SampleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}, {"id": "feed428a-5e85-4cfe-a916-0025d32c8e8e", "name": "Sex", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f6bc4e99-cadc-46fd-b8c1-4351da29d4bc", "valid": true}]}