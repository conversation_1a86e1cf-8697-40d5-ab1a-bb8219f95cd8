{"id": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "className": "com.open_care.medicalMicro.customer.OCCustomerBase", "name": "OCCustomerBase", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户基础信息", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "03e972da-3dfc-4ba4-963a-ed0e67c7fbf4", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "062646eb-f557-4e15-9433-dd49c2d0d216", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "077a9e3c-7298-4e4c-aeb8-356dbb0fad8f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "0b4a5044-7473-40fa-a18c-e349353b0ef4", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "2112d189-bca4-4ba8-a4f7-0219e954fd4d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "27d81071-58be-4150-9359-af293a9c785b", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "281c7de1-acc2-4091-a51a-a4c70284355d", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "319c4bac-94bd-4a32-8a21-9b21842d4e28", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "4289136c-eccc-4c4f-908d-a0f7a90f257a", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "5bae0310-1793-4a76-96a9-0b902361bd15", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "5c12a788-dc01-43bd-90d7-7f1c37faa675", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "6bf0f393-7bd1-4213-847c-c8fb201bd195", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "73ae5c97-cc90-4b06-9429-9c95d41ffdb0", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "7591d28a-8031-4dbe-ba59-b0949df22016", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "7b8e7125-e52e-4c5b-a8a5-8a9d0fe27406", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "8c0fe64a-8812-4eea-988b-d041406af31c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "91eb16c6-955a-4c08-925f-d82ab3d29fc7", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "9baee6a7-c0f1-4566-b4d4-cae1d56e17bd", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "ad68e927-4956-4f0f-995f-9eec34f56474", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "b77671a7-2f75-4be9-bb68-637ea117ff73", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "babd67b6-3d70-4457-ab87-d450a009ecfb", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "c4671e7b-6d3f-4700-878f-f5195960275c", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "ce69d742-69d9-4a2f-a5cc-f3eb2a9ce4c2", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "d32678ab-30ba-4fa3-bfd2-d8df06db7de0", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}, {"id": "db1978e4-a37a-444b-a4c3-4ac16594182f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a18", "valid": true}]}