{"id": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "className": "com.open_care.dto.service_order.AppointmentServiceOrderDTO", "name": "AppointmentServiceOrderDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "2a53b0e7-e9c3-4481-b276-25164ab2f02d", "name": "taskInfo", "title": "任务信息", "type": "java", "classType": "ProcessTaskInfoDTO", "refEntityId": "492f5bd0-1e8c-4ef4-8ac2-9e00f486fadc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "52e5e7e9-64ec-4a50-905d-60521e49aa3f", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "62f3ddae-33f3-4d9b-9e9b-1fbe0d1bf5d4", "name": "currentServiceCount", "title": "当前服务次数是第几次", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "7efad096-33a0-42bb-a055-336fc04a93ea", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "939e52e2-2452-4b31-9b63-ad3c1b435ddc", "name": "recipient", "type": "java", "classType": "AppointmentServiceOrderRecipientDTO", "refEntityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "96fa5cc6-1557-420e-a681-8ec60d683f1c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "a0218c28-7b96-4201-b1f6-715fa84d003f", "name": "isServiceEnded", "title": "当前服务是否已结束", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "a457c552-5c20-4b63-8121-2c7a661e299b", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "b229471d-e5d4-4ee3-99e5-6c1ea921ecae", "name": "appointment", "title": "预约单", "type": "java", "classType": "ServiceProductAppointmentDTO", "refEntityId": "4182111c-37a0-4723-8fe9-2032f3ac3da7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "b22d06ba-f233-41d5-8d93-00e97185fe87", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "beb247f4-eb82-47b8-bf68-5a4f0dc49140", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "ca58aaa7-4069-43f5-a066-f54d31776a70", "name": "processInstId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "fbea220b-c0a5-4041-a9b3-1cea0ac4ff78", "name": "isCanEdit", "title": "是否可编辑", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}, {"id": "ffe4967b-e1e4-43d8-8093-7275eae24868", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "6a4b61c8-f3f8-4d93-8bb8-17ad3fcf1c25", "valid": true}]}