{"id": "985c30d6-7514-44a5-9727-64325152b1a6", "className": "com.open_care.sample_collection.OCSampleCollectionCustomer", "name": "OCSampleCollectionCustomer", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "061e0e65-823c-4825-ab3d-797b34881098", "name": "name", "title": "客户姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}, {"id": "08940d7f-2df0-46c5-bab0-19eb4134f49f", "name": "certNo", "title": "证件号码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}, {"id": "432688ab-b783-44f4-bf7f-64be3fdd46a1", "name": "certType", "title": "证件类型", "type": "java", "classType": "CertTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3c0c713f4a40c01d2c9a28a771ee45aafb43e943", "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}, {"id": "4e6dec08-62e1-46e3-95bd-c96fe22c6b4d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}, {"id": "950d61fa-ce3c-4f89-8b39-c2ef836ade41", "name": "sex", "title": "性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}, {"id": "9cd454ad-1bb4-4039-b8c2-b7044369b645", "name": "customerId", "title": "客户ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}, {"id": "b6766f7f-aea1-4310-95e4-0e00a7b6519d", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}, {"id": "e31afa4e-5fdc-4f75-9056-87772a40cf01", "name": "attachments", "title": "检测问卷附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "985c30d6-7514-44a5-9727-64325152b1a6", "valid": true}]}