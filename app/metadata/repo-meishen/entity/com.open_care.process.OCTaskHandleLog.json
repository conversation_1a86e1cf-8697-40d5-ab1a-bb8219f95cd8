{"id": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "className": "com.open_care.process.OCTaskHandleLog", "name": "OCTaskHandleLog", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "024006f9-0874-4954-a0d7-a79079b5e8d5", "name": "handleResult", "title": "处理结果", "type": "java", "classType": "object", "refEntityId": "c685dd30-8acc-4a38-a5b1-8c498589f867", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "0c15788d-096e-40a0-8ea2-e4545d9a438c", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "19b4fb73-5cb1-4b3d-9fdf-3c09e3a73c87", "name": "comments", "title": "处理意见/备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "4853add4-e0eb-4eff-b6c3-62c8080d09c7", "name": "processInfo", "title": "流程", "type": "java", "classType": "object", "refEntityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "5370a819-1601-47a2-aee9-b157d9509361", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "577c7ba0-7dd1-49ea-94c5-90c67888eb04", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "5fa76b91-c46b-4658-b477-ef7bce747e65", "name": "operatorTime", "title": "处理时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "61fcd858-e441-43f5-880d-2a9a746230c6", "name": "operator", "title": "处理人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "69e151da-d1d0-43b4-94c0-ff771958a6d0", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "6a84261b-fe21-4cf4-9c79-0a75a3cd57d4", "name": "seqNo", "title": "任务排序号", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "6f599a9b-82ee-43f7-9404-709f98d53dff", "name": "priorityEnum", "title": "处理的任务优先级", "type": "java", "classType": "ProcessPriorityEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ebd12d3e01d79d6377c2ebd9af6b5ae07f81138c", "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "74c36883-ee70-4279-8de7-be36ba4f8fd8", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "7aee3bf8-f0af-48ec-9129-4c8642002abf", "name": "taskTime", "title": "任务创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "859cba5c-c070-4cea-b01c-9eaa87e30b77", "name": "taskName", "title": "任务名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "90b74ec8-3208-427e-99f1-455c7e36826d", "name": "taskTypeName", "title": "任务类型名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "91e9f136-5d61-4e7d-96f7-af9645670d6e", "name": "taskStatus", "title": "处理后任务状态", "type": "java", "classType": "object", "refEntityId": "9a815837-52c2-4449-9fdb-c0271fd3c337", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "94009d62-4d9f-4f2f-ab68-ecf051c9f6f3", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "98a8a3e7-78ff-41bc-8c00-7f209cadff0e", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "9943afcc-41ea-4b9e-a425-2a39e076b6bc", "name": "processTrace", "title": "处理轨迹/结论", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "995c755f-0ab6-4d3e-a569-39cc3ac0123e", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "9d9108bf-1a69-4d61-b6fc-f443cb9c670f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "ac91950c-0ea5-4900-8b06-15b6e67f9745", "name": "taskId", "title": "任务id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "b343ef9c-0b88-4574-8b3f-591e3e932e52", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "be32d16e-85f9-46e3-865d-1db7229f14cd", "name": "taskDefinitionKey", "title": "任务的定义key", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "d0dbedc0-32f0-49f6-84a2-04b989daf842", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "dd975eae-1690-448b-a161-e2544e297e87", "name": "attachments", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "f3d4f0e2-9447-410d-935c-0a220ac7e7ee", "name": "taskType", "title": "任务类型", "type": "java", "classType": "ProcessTaskTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "24cc2ccdfcb4c7f92bb645a767b1201937664ace", "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}, {"id": "fe8abc5a-59c7-4a79-9ec6-0e1d43669879", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "valid": true}]}