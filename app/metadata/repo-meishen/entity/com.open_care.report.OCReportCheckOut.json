{"id": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "className": "com.open_care.report.OCReportCheckOut", "name": "OCReportCheckOut", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01068eb3-dd07-4c54-b7e5-b9b52a2a05b3", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "11fe6d1d-bcbc-44a0-a1d6-788597c1f247", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "24605efb-4f7e-4c73-9abc-59d6fd519af4", "name": "summary", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "2e5a7ea8-043a-4e5e-93ee-c6c3cab0ec54", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0bfab962-6a77-4527-8256-e046e25w3455", "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "3269a1b8-cee8-44b0-9364-c14bc6fd8ca7", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "3b038460-4377-4aa6-a190-dbf60d3e83e9", "name": "reportName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "4564930f-d9d9-453f-bef4-a4d450764097", "name": "reportMonth", "title": "结算月", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "56f5fa53-580c-4e4f-9b8c-6d531ad601f5", "name": "reportDate", "title": "结算日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "616788b4-dbbb-4684-b3a1-60fb45c259e6", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "6b5885fc-7d7e-4fba-b64d-035e89c6dde7", "name": "reportKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "6cb866bd-5e98-4020-a80f-f0b69bcfaebb", "name": "endTime", "title": "结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "7c5584d6-2cd8-49d5-b4f6-5c98980edbfa", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "8c580aee-2cf5-4025-8223-bc5bc5a86c80", "name": "parentId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "96b4d1b2-05c4-4c87-b1cf-7e6f05586a15", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0bfab962-6a77-4527-8256-e046e25w3423", "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "9bf1f0c2-0b59-4abf-a080-d95a0efd31fa", "name": "cashier", "title": "收银员", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "9ef87ae4-2e06-436a-bbde-406105feb53a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "9f65bd9a-71ca-45e1-9c73-6c026c4132bf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "ccd5c4b0-65ba-4c38-9919-5babd3b0912a", "name": "beginTime", "title": "开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "cde41b1e-e85e-4d3a-b242-8d0165f40f4f", "name": "reportData", "type": "java", "classType": "JsonElement", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "de191e0b-98e8-4ac4-9a80-6d7110d467c1", "name": "auditor", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "df4385dd-aa00-4da1-8fe2-7d3f3b3a7a1f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "e5440b4f-df51-4ca6-b990-f26e405f8a83", "name": "auditDate", "title": "审核日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "e5b18817-746c-48fd-b763-7b17ef90827a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "fb3e208f-2068-412c-a87e-f7e19e580163", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "fc69c7b2-cd0d-4848-978e-8fefdbb5e696", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}, {"id": "fe7c335c-7ea2-4f90-8b10-bbfbe05b0088", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "61899d71-f4c3-4dae-99fb-387e2ec140b9", "valid": true}]}