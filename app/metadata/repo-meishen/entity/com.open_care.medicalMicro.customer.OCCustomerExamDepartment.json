{"id": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "className": "com.open_care.medicalMicro.customer.OCCustomerExamDepartment", "name": "OCCustomerExamDepartment", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户科室检查结果", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "031a349a-50ff-4ac6-bf1c-d5ebbc6159fc", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "1aa0bddf-40b6-4ef6-bada-cd27c4352e83", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "1abf7111-27cd-4496-a7c9-e13901c93bd3", "name": "departmentSummary", "title": "科室小结", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "1ce10f89-940d-4fa3-9812-e87c79311740", "name": "postponed", "title": "延期", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "1e3c44a5-2dff-4c43-b74c-0bebeb16c416", "name": "severeDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "style": "Select", "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "2175fb56-3d2e-4813-b68c-b826d955fe84", "name": "departmentDisplayOrder", "title": "显示顺序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "2513159f-9d39-44e0-8970-92a5b5cdef29", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "2529c532-7982-408f-9bb5-1596917aa6be", "name": "entered", "title": "已录入", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "32a4946c-d6bc-4990-b691-b1df7b29ab19", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "34193a6c-e39d-48f4-9050-33c71d7854c8", "name": "departmentSummaryDesc", "title": "科室小结详情", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "35e2fc57-8774-4838-afd9-d631be790944", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "37a3f306-f664-470a-8a20-2c17138f41ef", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "37fab719-b3f6-4c84-bea0-290556cdc272", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "42394c44-0555-42a2-857a-75b0c1f336d8", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "44770827-6ccc-4972-b8db-2373ff62aaa7", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "44c1baff-3b72-4e39-8570-459f4f919fbc", "name": "customerExamProducts", "title": "产品", "type": "java", "classType": "object", "refEntityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "4ae4fdd1-f60b-45eb-a476-307b54a68c25", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "4dbcd25d-05f4-4850-8d82-2746c28d5c5a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "54c36add-2674-42b9-ab13-937b239a10cd", "name": "audited", "title": "已审核", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "5f63b2e6-e8fe-4c3a-ab6f-bc7527372559", "name": "postponeDeadline", "title": "延期到期日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "5f74d011-6f2a-4856-9330-5dd0e504df76", "name": "lastAuditorName", "title": "最后审核者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "6131f8ac-a83b-4f00-879f-463f74e57078", "name": "customerMedicalExamInfo", "type": "java", "classType": "object", "refEntityId": "231a4781-2abb-419a-9079-467c06dde0cc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "63e6d40a-b035-48ef-8665-abe14f3bf127", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "6545f6b9-068f-41d4-b177-c7ab8893ead5", "name": "operator", "title": "弃检延期操作者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "6720b25a-15cd-4d9b-b14e-c0c268fb27ad", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "6da303e1-24b9-420d-a108-3532d8a3f237", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "72264a7f-2595-4319-8b45-c930144d7cb9", "name": "departmentSummaryTitle", "title": "科室小结标题", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "74fd9e06-65d8-4021-bb0f-65fcc7c6451e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "78b1da46-c340-42d7-b16b-6f45e967e082", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "82b61316-4799-4c07-95ba-07140ec5dc59", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "871af859-99ae-44f6-ad28-3efec195dffe", "name": "firstEntry", "title": "首次录入者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "893659c0-4f81-4acc-9a92-e0e889874153", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "8a87c15e-fb05-43c7-8402-1fa678ec8d12", "name": "operationTime", "title": "弃检延期操作日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "8b0aabf3-8dfa-46fb-82ac-d2d0fa8e401d", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "8e842b23-d63d-4547-bc42-729f7964266d", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "91339704-9893-4da0-bfa9-69189f709d49", "name": "lastAuditTime", "title": "最后审核时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "a3c1c379-78aa-4b4c-87a2-4f931b8edc54", "name": "departmentPositiveSummary", "title": "科室阳性小结", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "c67a8ba5-c52d-419e-96db-40c51f569748", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "d5ff7562-cfdf-4e37-8461-e7f2e181aa07", "name": "firstEntryName", "title": "录入者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "db5938bd-c527-4e8f-a18a-f9cdffd84169", "name": "firstEntryTime", "title": "首次录入时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "dfabd1db-c374-4878-867c-516a4b073bd1", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "dfc2c41d-23bd-4fe2-9236-7f7dd21c528a", "name": "abandoned", "title": "弃检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "e22ba6d9-c899-481d-bd78-6ba286956e4c", "name": "last<PERSON><PERSON><PERSON>", "title": "最后审核者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "e3c4b031-4a1b-4cae-8aa7-c9665f867d82", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "e54de773-5f3c-40db-a2fb-b7443c903685", "name": "department", "title": "科室", "type": "java", "classType": "object", "refEntityId": "6c6fcaa1-55f5-410d-b5fb-ad2ece9240a1", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "da973ffe-91ee-4e34-bf16-d78cf63aa13v", "style": "Select", "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"department": {"$id": "e54de773-5f3c-40db-a2fb-b7443c903685", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f25ef61a-c3e8-44ae-a8ab-fbecccb9d8c6", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "f80d95be-d2fe-4d2e-a63f-6f44b95ce8d5", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}, {"id": "ff064147-afc0-4e4a-bedd-3925a29b67d8", "name": "entryAndAuditInfo", "title": "项目操作信息", "type": "java", "classType": "object", "refEntityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "valid": true}]}