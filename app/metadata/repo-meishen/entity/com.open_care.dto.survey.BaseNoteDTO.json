{"id": "17a6fd4e-d17a-4bb4-bc1e-d9dcd6d8cf2d", "className": "com.open_care.dto.survey.BaseNoteDTO", "name": "BaseNoteDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1b6829ba-99f0-44a9-a639-038aa436cb36", "name": "handleResult", "title": "审批结果", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "17a6fd4e-d17a-4bb4-bc1e-d9dcd6d8cf2d", "valid": true}, {"id": "9e8b3c09-a5df-4743-8ca3-65b21c6a3066", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "17a6fd4e-d17a-4bb4-bc1e-d9dcd6d8cf2d", "valid": true}, {"id": "9eba99dc-56e3-4d59-84ba-c1d5d6096f4c", "name": "operateType", "title": "操作类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "17a6fd4e-d17a-4bb4-bc1e-d9dcd6d8cf2d", "valid": true}, {"id": "bac83898-7b7f-42b3-a31b-b646ca8f6117", "name": "attachments", "type": "java", "classType": "BaseAttachmentDTO", "refEntityId": "1b7bbd57-13b6-4820-a4ea-c0b27e0bd644", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "17a6fd4e-d17a-4bb4-bc1e-d9dcd6d8cf2d", "valid": true}, {"id": "c3bab1f3-7359-4b08-bfb3-23b547012de9", "name": "operator", "title": "操作人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "17a6fd4e-d17a-4bb4-bc1e-d9dcd6d8cf2d", "valid": true}, {"id": "f894775a-7d2b-4f56-b346-8d3d2b6651ba", "name": "operateTime", "title": "操作时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "17a6fd4e-d17a-4bb4-bc1e-d9dcd6d8cf2d", "valid": true}]}