{"id": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "className": "com.open_care.resource.OCCapacityWarningSetting", "name": "OCCapacityWarningSetting", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "076a2fe3-ec02-4131-9190-3b001397ac28", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "0a1be1cb-dbbe-4e9c-9f93-5e86663a408b", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "29879415-41de-4dff-ab40-46d44d306850", "name": "products", "title": "产品", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "32384228-8c48-4020-bc07-28698a351d73", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "47476e5b-f405-481a-99d0-b6454fb2c3a2", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "60bcca1b-46c0-4d0c-bafc-6a8ff6d1cf57", "name": "bookingChannel", "title": "预约渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "647a38c9-c0f3-43c9-a092-59e6c3815d97", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "64c75211-d5e8-4c23-bde5-5162c654639d", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "6c7a094a-aa9f-4dea-98a5-268b72707933", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "7d508ed2-c518-46b9-8c5c-9e77f8227453", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "8efd4507-1330-4b03-92fc-ac70e9ec064a", "name": "customerType", "title": "客人类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "ae8d9636-2007-48f4-820a-fbeb3eed5faa", "name": "department", "title": "科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "bb942ebb-2518-4175-ad96-533383ef51ec", "name": "blockShift", "title": "阻止预约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "c807b63c-c0bb-487e-83ab-708f52871397", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "de3d6a22-a127-4728-b513-7f0d31a38fc8", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "ebdadbd5-395b-4548-8f8d-50b2428a2cc4", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}, {"id": "fb27e648-9399-441f-8b16-118573fd163b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dab7f45f-7b61-41b0-a55f-b16<PERSON>de5af4", "valid": true}]}