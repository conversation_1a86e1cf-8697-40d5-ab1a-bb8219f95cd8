{"id": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "className": "com.open_care.medicalMicro.customer.OCCustomerPrintData", "name": "OCCustomerPrintData", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "013c5459-2fcc-4695-9d66-0be6826c9852", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "089f3d92-e19b-454e-8d8a-760a97e581fb", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "0d55331d-8fbd-442f-a57f-7da901d7454a", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "1d913338-51b8-4aa4-9818-bb021ce88126", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "1da1bfc5-81e4-4d9d-9d7c-488f6c8d0a33", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "1f7176ab-ca5e-4ac9-a66c-b30e22b87563", "name": "filePath", "title": "打印文件路径", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "1f775422-5840-4be6-b85c-77aefba3a44e", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "32abc35d-17de-42c6-bfe0-e287954a9327", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "3e2bd5e7-51c2-4c14-9187-af5e89a1baa1", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "4da155ee-cd62-4eba-af42-74735e71e4e0", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "528a1aeb-01ef-4ddc-bcee-3f5d4b6384d4", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "59dcd444-e144-4498-a4b5-159a2046c289", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "5ac74701-1057-4d51-a8bc-825c53b630af", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "60ce6481-cf0c-4c57-9f08-4362d4ec991f", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "671ca59b-ee0d-45cb-a650-89b6a30416d2", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "6f8a3992-2abf-4885-afaf-d189f61eb4bf", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "792fe98f-e58c-4029-bf95-c5d59f3cc240", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "870f13a1-339a-4af3-96f8-f0c43f1a4262", "name": "printTimes", "title": "打印次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "97f89000-26cb-43e6-add8-8a7e1107657b", "name": "printType", "title": "类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "9bfbebdc-f5c3-43da-806f-0a16ea420486", "name": "printerName", "title": "报告打印人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "a4db9144-f309-4fdc-ac0e-7235bb422a6a", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "a5b7d626-7baf-421e-af02-70add161915d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "afedf20b-f4a3-47d0-b590-c19a86ee5061", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "b50637f7-61ec-44c4-b9db-fa2926b35542", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "b8bc08dd-1429-4959-8931-47bedd50df33", "name": "printData", "title": "打印数据", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "bafde16c-6cf9-4e70-b501-cbb8ca44a0f2", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "c312fbb6-1b10-4a9c-98af-f6c8e999e32c", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "c46ed5c2-7369-4561-bfa8-305497276c80", "name": "printTime", "title": "打印时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "c7fc6d3b-bb9d-498c-bbfb-daa34030df43", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "ce2e1db0-3f08-45c4-87bd-d6410fddfe6f", "name": "isPrinted", "title": "已打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "d05db971-4f27-48d3-80e0-8e96067b54d8", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "e20cf2b0-da61-4c6b-8bee-0f8e7b4d2bea", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "f55ea66c-e4ea-479b-a5e0-d35145cacd90", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}, {"id": "f8612414-9a60-4541-9037-3b34f19b9836", "name": "printerOcId", "title": "报告打印人ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e7d0bfe-bbae-4276-8ecf-f557dbe00321", "valid": true}]}