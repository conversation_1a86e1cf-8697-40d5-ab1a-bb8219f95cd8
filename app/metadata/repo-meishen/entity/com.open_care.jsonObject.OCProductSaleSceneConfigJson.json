{"id": "e20aeb2c-97de-4109-9ce4-933c8fec65ae", "className": "com.open_care.jsonObject.OCProductSaleSceneConfigJson", "name": "OCProductSaleSceneConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "69acac6f-a939-47f9-b607-7c6c9ea9ebe4", "name": "chargingMethod", "title": "计费方式", "type": "java", "classType": "BillingMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "baa55b6d34ca3c4014a24602e07065b52fd0bb5f", "entityId": "e20aeb2c-97de-4109-9ce4-933c8fec65ae", "valid": true}, {"id": "7d2e5d6a-f0e0-4c41-8df0-c86df8868d65", "name": "floorPrice", "title": "底价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e20aeb2c-97de-4109-9ce4-933c8fec65ae", "valid": true}, {"id": "b2de8369-91cd-4510-9b95-3615cd70412a", "name": "unitPrice", "title": "销售指导价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e20aeb2c-97de-4109-9ce4-933c8fec65ae", "valid": true}, {"id": "b5bc8240-1e95-4d8f-ab08-ed4cbd168d9e", "name": "scene", "title": "销售场景", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e20aeb2c-97de-4109-9ce4-933c8fec65ae", "valid": true}, {"id": "d6f71e47-ee73-4efb-bcb4-bc9cfbde21e8", "name": "internalCostPrice", "title": "内部成本价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e20aeb2c-97de-4109-9ce4-933c8fec65ae", "valid": true}]}