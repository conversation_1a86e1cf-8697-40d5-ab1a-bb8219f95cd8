{"id": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "className": "com.open_care.product.rights.OCServiceImport", "name": "OCServiceImport", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "03d616ee-ee19-4ebd-a7ea-0f1895b0f7b1", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "15c5b559-75e6-4a08-b3c9-5a20cf7975ff", "name": "endDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "1bd519fc-e502-4e88-9a11-39a9262461e9", "name": "startDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "328a92fd-5b8a-4d24-a7c0-21cf80414814", "name": "sourceId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "3ba03db3-a9ea-4721-ad70-8c2cf81e9e61", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "436e00dc-214e-4f36-a345-b1b8af828098", "name": "procode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "4cef1684-3d95-4a99-b6fe-a980dc06540e", "name": "idType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "4d80c554-b934-47d5-b44d-0d36bb11f124", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "51dbc51b-8220-419f-ad23-58b55e3fc05d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "5a1573af-65dd-4b97-acaf-c83523b32411", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "5fb78104-7f12-4256-b67e-4a37785c10b1", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "68f586c3-6dc7-4e64-bc81-65aa7c402f97", "name": "remainTime", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "74a8a14c-dc06-401a-ae13-a05aa96edce2", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "869b50ed-cf4c-4c5d-93fb-f7ad08b80aab", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "9cffcfa7-715a-4ab9-82e1-def9eb761622", "name": "companyNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "9d493bf7-1cee-4381-b2f8-3db6dd685beb", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "a46952b9-eaf0-4330-85c4-638b5b349f70", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "a697315c-6b1f-4cd7-9249-d52251f2ecb0", "name": "idNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "ac56cfd3-1971-4ef8-9283-999e58c0d9c5", "name": "serviceName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "b06dfe94-d3e0-4eb9-9e9b-bf098c1cf547", "name": "source", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "ba731417-b3bf-480f-a7b4-9c071e7ea97f", "name": "serviceCode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "bb3441ee-d754-40e7-a4ca-c9e17f4e626f", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "c7217e71-b179-419d-ba7b-bcd612142c7f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "c848f638-2809-4192-82e5-bc91c15204e7", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "d4cc69cb-ca12-4124-ae1d-3920437499e1", "name": "customerNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "d542639b-1c43-41aa-833b-17a4362dd274", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "ebbd1f24-dc93-41a8-b803-bdae80f71380", "name": "totalTime", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}, {"id": "f26dbb57-e8e5-468e-bff4-0f95d379915a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3bcb28e8-7616-42d9-9b79-ac57d8549e57", "valid": true}]}