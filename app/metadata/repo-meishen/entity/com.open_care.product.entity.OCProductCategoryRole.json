{"id": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "className": "com.open_care.product.entity.OCProductCategoryRole", "name": "OCProductCategoryRole", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0a88968f-ed04-499e-912e-e011c171fbb4", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "210bdfeb-edae-4b9d-8fda-bf3bf62cadcd", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "40c9cff1-7a9b-421d-a6db-204026d9db7e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "5f0d4e93-e219-469a-a320-5fa96adbcd3c", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "676179de-7497-4f1e-9a3d-5efbdb396a3f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "757cff76-4deb-4742-9eed-7a05138c2c08", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "7b8c76b8-fdab-4336-bf84-c446c89c9308", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "81c12af4-db91-4ccb-973d-60267cb3321e", "name": "partyRole", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "83873342-2f9d-41d8-a5b5-1dd75d324982", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "8c7919b9-26b2-4f92-973f-6ef969363769", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "a23361be-9559-4fc6-8b44-611f235c0556", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "a87d593c-84c1-4691-b35e-b09d80c32b06", "name": "contract", "title": "合同ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "a8b47257-f99d-4d78-946c-4a6608a18693", "name": "productCategory", "title": "产品类别", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "b4fbd06e-9983-4745-9e1b-5d9c07fb4918", "name": "internalService", "title": "内部服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "cdb47810-79ed-41c9-b06a-c96923805c1e", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "d6cc6e02-594d-489e-9f6a-e6ab5d5a22d1", "name": "party", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "ebbdc1fd-516e-4556-9c29-eda52f01955a", "name": "hospitalService", "title": "医院服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "ec148bc5-2d65-4c0a-8421-ff0804fabcde", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}, {"id": "ef886e96-c87b-4ee0-85b5-b4c798431d43", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d75fdc22-fb64-4434-b533-6663ac4ce4e4", "valid": true}]}