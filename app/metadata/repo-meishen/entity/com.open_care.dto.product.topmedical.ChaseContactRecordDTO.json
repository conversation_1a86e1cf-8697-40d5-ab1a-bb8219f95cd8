{"id": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "className": "com.open_care.dto.product.topmedical.ChaseContactRecordDTO", "name": "ChaseContactRecordDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0462a740-45c7-4e84-b445-d438f192417d", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "0f22bdad-839a-4f6b-8dad-f57889b8e8d8", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "24d4a9cc-6b0e-4fee-9241-e52dcb206c07", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "2e5c4c53-5fa2-408a-b5b3-85adeaec5760", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "4670c63c-09ab-40af-97c9-2909c6d895e7", "name": "role", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "4abd3337-8011-4707-919e-d14e4bb70e8e", "name": "phone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "51c87279-5047-483f-811a-81343d86db06", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "608b59bd-25c7-4f01-9758-f834316489ff", "name": "contactTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "61dc768b-8b38-4713-a539-569cf8d79d7c", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "737155f7-2795-44ed-9a2b-4c81c24c3028", "name": "note", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "7dc83958-eb9a-44ba-8a3d-c65633517837", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "92f68307-d312-4600-bebf-42bec042f4ef", "name": "isRepayment", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "975dcf49-ef0c-4aa4-bb21-0f7bee5f2dd3", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "bb72fb82-14bf-4de6-9856-adee9<PERSON>bade88", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "c559b16e-0afa-4412-9e57-c406d37a7b7c", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "d1dce24f-da2a-477d-8f1a-56237e107ed1", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "dbbfd0cc-fe63-41ac-afc0-b9160c3e07f7", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "dc210add-c74d-47d9-8bf8-3a7d473d0d4c", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}, {"id": "fc6cc66f-f8f8-4944-84a0-ca322127f871", "name": "chaseTask", "type": "java", "classType": "object", "refEntityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e25c1019-b205-4cf7-a8ef-39babc5bb6c6", "valid": true}]}