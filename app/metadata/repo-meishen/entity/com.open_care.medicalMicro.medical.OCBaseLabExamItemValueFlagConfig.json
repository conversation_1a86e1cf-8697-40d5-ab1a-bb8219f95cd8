{"id": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "className": "com.open_care.medicalMicro.medical.OCBaseLabExamItemValueFlagConfig", "name": "OCBaseLabExamItemValueFlagConfig", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0c5a2f6b-ad94-42e8-9870-830f66cd3ec5", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "12ca5dba-01a6-4b4d-badf-207cbfcb3fad", "name": "baseLabExamItemValueFlag", "type": "java", "classType": "object", "refEntityId": "23ba0726-f29b-4ef3-a4d0-b9d995a92cd6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "1dc75450-ba82-4ebe-a01e-dccbee2639a4", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "28e1ecfc-523a-4626-ac71-d678a337db78", "name": "deliveryOrgItemValueFlag", "title": "送检机构结果值标识", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "2db4b43a-c9f2-4d27-a22d-b5d04e8f34f2", "name": "emptyFlagType", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "4cff78ae-79b7-47ae-bb38-3fcb63d1f701", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "5a4876ac-926f-4527-88d1-75fd630c571f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "75016dfe-747a-458b-a60d-4571a9c563d7", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "760abeb5-b526-4c2b-8205-9e06703308f3", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "8031e18b-d2cf-4650-a865-68019fa1dcdc", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "8c60f91c-5683-4810-a901-c94dfa03de79", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "8f678cca-fc4a-4583-a582-06b87fb0d707", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "91b7d831-c620-4be2-a5f2-230820754b94", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "925ff194-b503-43c8-8534-21b0ca23c977", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "b238e18e-2e8a-4b4b-a0e4-81f76718b617", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "ba471968-ec06-40b5-916c-e266b19f85fc", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "c151e42e-2554-4879-90f0-7a16bfb398ad", "name": "deliveryOrgId", "title": "送检机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c69a5bbf-fac7-4a47-940a-1d841add43gg", "style": "Select", "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"deliveryOrgList": {"$id": "c151e42e-2554-4879-90f0-7a16bfb398ad", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "送检机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c8939003-16f6-4728-b8a5-05a433c9b5f1", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "cd18b7fa-8d23-4846-97be-d532ea4c2b5b", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "d9754a13-775f-4a36-b973-896fedbeffb1", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "dcf5cf1a-ddc0-4a06-909b-c8266d9134e1", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "f043b6ff-8075-4dc2-85af-93e68bad40cf", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}, {"id": "f65afc5e-1e19-4be7-95d9-391c4c5a2a83", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c3f5a97-b84c-4b8a-a290-314af47b56c0", "valid": true}]}