{"id": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "className": "com.open_care.resource.queue.triage.OCBaseTriageArea", "name": "OCBaseTriageArea", "standard": true, "authority": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "06e4b145-bdcd-4e9d-aca3-b1c013b79f31", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "06f84c8c-06e6-495e-8b91-b31095760350", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "195697f1-4fc7-4692-9289-e823420cdbc2", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "1dd77e54-6c17-44ff-a33a-a1a8dc69412d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "205ccad5-8421-45a7-bc16-725e37a21d14", "name": "canAppointment", "title": "是否可被预约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "23a182c4-e31c-45df-b49f-b58896865cf1", "name": "servicePlans", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "3ad3a5b7-d023-4739-a4d9-f92ce8411a80", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "40cb5f00-b870-4f9e-a5c3-1d446ca5c0cb", "name": "planAndContexts", "type": "java", "classType": "object", "refEntityId": "5df4fe41-a389-4c02-a1b0-1a74ef3cb5ce", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "4bf41bb0-1447-4204-915b-f0e57a7f1802", "name": "partyRoleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "526c258d-5038-4f65-a3f2-902dabd7aaef", "name": "areaCode", "title": "区域代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "54a22c5d-6ad8-4147-b135-2b9ef03dbc53", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "558c88fc-8d5d-4a45-a83c-88a0cd151507", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "577de485-0dc5-4a4c-a748-b4ef137090fe", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "57df7a29-d73e-4043-b601-131735235d00", "name": "baseTriageRoomGroupList", "type": "java", "classType": "object", "refEntityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "587c0ac6-1fc9-4a69-b648-b5c3c43c9a20", "name": "profitLevels", "type": "java", "classType": "String", "refEntityId": "ed205dce-6594-4773-b0d7-1b2ce4d21f46", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "6454cbde-09af-4383-984c-94f7b1f50aa1", "name": "baseTriageAreaDistances", "type": "java", "classType": "object", "refEntityId": "705feeaa-5552-4d19-9f95-6301f22d7e0c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "6715b23d-3231-4476-9319-a3524c946694", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "693ef9a4-df47-40f3-966a-2a5a7b84ea1e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "71714762-13f6-42fe-9e04-fde91c6c1583", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "727fd901-b9d6-4695-965f-b38ae1499979", "name": "inService", "title": "可服务", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "true", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["inService"], "properties": {"inService": {"$id": "727fd901-b9d6-4695-965f-b38ae1499979", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "可服务", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7be7d5aa-e357-470f-8952-f7d4c5857a64", "name": "description", "title": "描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "7fe95ca8-e0f3-4dc7-af99-9bb4413fb717", "name": "medicalDepartmentInfo", "title": "医疗科室信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "8c619155-deba-4feb-bfcd-13b356759b31", "name": "children", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "8e8b8926-a808-44ef-9634-36dd70ddcbcf", "name": "weights", "title": "权重", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "0", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["weights"], "properties": {"weights": {"$id": "8e8b8926-a808-44ef-9634-36dd70ddcbcf", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "权重", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "93ef81d0-50db-4d2a-a20e-73577bd14aef", "name": "resourceCategory", "title": "资源类别", "type": "java", "classType": "object", "refEntityId": "516dcdb4-8f83-4098-ad59-65da46ff2174", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3682d84b-26c7-499b-893e-a7fcfbaddded", "style": "Select", "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"resourceCategory": {"$id": "93ef81d0-50db-4d2a-a20e-73577bd14aef", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "资源类别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9a886460-cc53-4c00-a2bd-fc141fa05a2b", "name": "name", "title": "分诊区名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "9a886460-cc53-4c00-a2bd-fc141fa05a2b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "分诊区名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9d7e902f-45e4-4a9f-834c-7b96102a8632", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "a678989a-1a0d-4057-ba16-3d64520f571e", "name": "matchSex", "title": "适用性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "aa796605-c9a9-44ca-931d-ecc306fe3656", "name": "code", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "ab3ef250-578e-46d5-b7e3-b3aee87770f9", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "abc70085-b97f-43d0-9309-e918f5be96fc", "name": "resourceGroups", "title": "资源分组信息", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "b0bed14d-a5c8-4dc6-9942-dd985aac11cf", "name": "resourceProperties", "type": "java", "classType": "object", "refEntityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "b4d46499-8c1e-4f08-8846-935734b36bee", "name": "shortName", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "b55a7e1c-5851-4f7f-a528-ded4befd9f2a", "name": "canBeSpanned", "title": "可跨区", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "true", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["canBeSpanned"], "properties": {"canBeSpanned": {"$id": "b55a7e1c-5851-4f7f-a528-ded4befd9f2a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "可跨区", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c30b2165-015d-4a45-b1e8-fefc4022aa52", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "cca99e7f-881e-4026-8831-7e429e4074b6", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "ce5d70e0-e493-4f8b-968f-f378a9ba9998", "name": "tags", "title": "标签", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "db477a3a-62f2-4672-b32f-769b3ca32b14", "name": "party", "type": "java", "classType": "object", "refEntityId": "39f9bf4f-b0fa-41eb-abf1-0d2dfbb2c2ab", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "dc00f2d8-4f5c-4384-b108-3dacf69c4dee", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "dda60a88-6613-4215-b922-6b37fdc216f8", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "e0c21731-0d26-4e06-97c4-58989612e45e", "name": "parent", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "e69eadc6-17e8-447e-9d82-96811523a8b4", "name": "resourceName", "title": "资源名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "f23587b9-080a-4c33-8949-6e7ec800a52e", "name": "job", "title": "职位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "f44cdb16-f261-4151-ad1f-3c2d22adc6b3", "name": "resources", "title": "资源", "type": "java", "classType": "object", "refEntityId": "7dd3506b-c0a7-4d8b-8def-73c2cc121012", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}, {"id": "ff9b7ce7-0b60-4d30-a644-6a1b08d8975e", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "valid": true}]}