{"id": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "className": "com.open_care.medicalMicro.customer.finalExam.OCCustomerFinalExamInfo", "name": "OCCustomerFinalExamInfo", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户总检信息", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0525f069-c6ab-4fb4-93c2-b013b099b38d", "name": "reportPrinted", "title": "报告打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "095062d6-185d-4910-96fb-bf8c8dbae7c3", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "0ae58b08-7348-4aae-8237-5c7793777ac1", "name": "nurseAuditorName", "title": "护士审核者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "0b3ed89c-afee-4327-a304-1bee7ceef218", "name": "startFinalExamTime", "title": "开始初评时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "0d405a0e-462b-45f4-a33b-93607a31350b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "147374c7-7a66-4435-9a99-6dccd90f4146", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "16475b1d-a732-4309-8bd7-ad6d2781cacd", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "169a4bb6-e870-450f-a71e-a04952e15001", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "1f5fb26b-b3ac-4efb-b232-a71ecceadbc5", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "268434c3-dd6a-4181-925f-8eba810f5db2", "name": "finalAuditLockedOperator", "title": "总审锁定者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "297bb401-70d9-417e-a22f-877e3e70543b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "2bbe613d-c4bd-4c41-9a6f-0bb5a8de4d44", "name": "finalAuditOperatorName", "title": "总审者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "2e1df056-88b7-4c56-9549-1a2ff33f2fcf", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "3035bc4b-28ae-4af6-945a-0b83a36717b4", "name": "sourceProducts", "title": "来源产品列表", "type": "java", "classType": "object", "refEntityId": "5ca8497f-311f-408a-8486-6bffd8ecbd08", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "354854dc-f1e2-461f-963f-c0e1f615f14e", "name": "pdfReportUploadTime", "title": "电子报告上传时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "3743759a-e850-4dc5-a1a7-b5d10e83f64f", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"sex": {"$id": "3743759a-e850-4dc5-a1a7-b5d10e83f64f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3fb0922b-bfd4-43bd-9a26-09bc26004929", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "4294c5f5-a689-4c1e-9cf6-b1c4edfc6d62", "name": "completedFinalAudit", "title": "已总审", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "445e3431-c4e3-428e-ac02-9c3b924ebd4c", "name": "pdfReportUploaded", "title": "电子报告已上传", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "45482c96-8240-4e93-8320-47de98dd088f", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "4683dee9-f808-446b-a596-f5d8b817f3a2", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "4c62c64a-5ad6-4ea2-bc34-91df9fbcc9c3", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "54cdf705-6f80-49c5-8be5-4da7a87cc79e", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "5a15a891-a0c4-4b62-845f-2ef7fb74f7af", "name": "pdfReportUploadOperatorName", "title": "电子报告上传者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "61fe430f-5882-4ee8-a620-8a13a093406e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "63b9cf29-62fd-4648-97c4-ca6fa64a3872", "name": "finalExamOperator", "title": "总检者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "6a56cab1-7da4-4b81-a805-5a9d6f513aeb", "name": "finalAuditOperator", "title": "总审者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "70dd603e-11cd-40d0-b640-a9f8755e57cb", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "79059d09-e1a7-46df-a442-a6626daaba1a", "name": "finalExamLockedOperatorName", "title": "总检锁定者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "798694e7-548a-45ff-9d46-058978d47cf1", "name": "customerFinalExamConclusionInfos", "title": "总检结论建议信息", "type": "java", "classType": "object", "refEntityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "7af7699d-4f8f-4631-afc7-756e88c00c30", "name": "finalExamLockedOperator", "title": "总检锁定者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "80d71bcd-fb3b-4be6-b232-bd1ce3932ab9", "name": "readToPrintReport", "title": "待打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "82f3c372-9ecb-43a7-98b4-ccd03d9c3c15", "name": "completedFinalExamTime", "title": "完成总检时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "837e8598-8702-475c-bd55-7631ad397a64", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "86638a87-930f-4d8f-9503-f5c586965cfc", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "8d86a27b-0c04-47c7-8285-5d9d069c9dd8", "name": "pdfReportUploadOperator", "title": "电子报告上传者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "91ac3233-64f3-4b83-a821-d90fee9d4edf", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "9653d282-b2f3-47bb-8565-e80cac1bbdcc", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "a5848102-9e11-4b63-bff2-b92f0aa75cc0", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "a8dde060-0892-498a-985a-c8605dfc2834", "name": "finalAuditLockedOperatorName", "title": "总审锁定者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "b10fc0b4-9ce0-4164-a188-1e9063fb3dfd", "name": "reportPrintTime", "title": "报告打印时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "b745ab6e-1c0f-4b68-a5de-8934c2a9f430", "name": "dataType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "baf5b7ed-7898-477c-bf61-b198220e3807", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "护士审核者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "c12b6a80-4046-4ec2-b2b2-84370ff7a36c", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "c37b0df8-3f72-4b6d-866e-23a06e02d9b4", "name": "startFinalAuditTime", "title": "开始总检时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "ca3f6cf2-cba8-4ecf-a9dc-57dd0b28065e", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "d049b4b7-c514-41ed-b562-d103b908d07a", "name": "needReturnVisit", "title": "需要回访", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "d12f8b1d-9419-4f7a-a113-0d6f0ac6255e", "name": "processInstanceOcId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "d37c2c00-9a07-4bc9-87ae-864597dd242b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "d5c71cf3-6dc8-4dca-9a17-0c002da07511", "name": "severeDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "d87cb33c-212c-4d51-9bbe-462921c26763", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "d9c25557-015d-4ff4-ab8c-d9aa1a84d74e", "name": "departmentsSummary", "title": "科室小结汇总信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "df62ab3c-d095-4a28-91d9-9c0bc16c0f9e", "name": "completedFinalAuditTime", "title": "完成总审时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "e4cd6f4e-54b5-4ce1-a23b-66944fbca739", "name": "examOperationInfos", "title": "操作信息", "type": "java", "classType": "object", "refEntityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "ebbb23f1-244f-44ea-9c1d-1ea293a8ec5c", "name": "finalExamOperatorName", "title": "总检者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "ebf67dfe-6139-4824-a3a8-7386208c3391", "name": "earlierServiceTime", "title": "最早服务时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "efbae1ff-c070-4c46-9f08-37dc604847dc", "name": "nurseAuditTime", "title": "护士审核时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}, {"id": "f00dcedc-4cf1-429c-a670-7f16a4013f18", "name": "completedFinalExam", "title": "已总检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "valid": true}]}