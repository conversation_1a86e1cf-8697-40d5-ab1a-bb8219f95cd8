{"id": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "className": "com.open_care.product.rights.OCSalesRecord", "name": "OCSalesRecord", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01ef93fd-ccc8-456b-9186-c08bee6d6563", "name": "gainDatetime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "198bd1c0-f724-4753-ab51-94258733d948", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "49214fa5-ab4e-4401-8888-2c07d0ef552c", "name": "clientRights", "type": "java", "classType": "object", "refEntityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "4bbf0acf-efb2-4de0-bfdd-9e5888040e40", "name": "gainAmount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "60d25d3d-f836-41e9-8776-fe0da691ef96", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "6f5cad8b-0354-4974-8f97-1b343038327a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "81e4d33e-ba86-4408-8151-41fd618b94a3", "name": "salesmanNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "8b404bb9-cedb-4658-ba41-161d23669f32", "name": "salesmanOrgName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "933b5240-05ed-4ac9-80d5-16f199379330", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "aa9d9f73-f905-40ff-af80-684852e654e7", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "b1517b06-3b84-4785-b103-495c78ebcfce", "name": "<PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "b713625e-e147-4fab-9be6-ab587b3bc1d4", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "cc37ad12-117e-4309-b922-d7a461b33bc2", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "d542ca76-9e14-4369-85b3-3e70afd29633", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "ed604dd6-544e-4606-b033-95546636f15f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "f12a607f-2380-4e97-8f76-8a23f2e0c761", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "f445514e-c7c4-40dc-bb01-6480662a9629", "name": "salesmanOrgNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "f4e879b3-9453-4851-9345-cf07816cc256", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}, {"id": "fe3f7fdd-46b0-4f53-bac2-cde65aa343d3", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "valid": true}]}