{"id": "fbb60776-6bb8-4262-b36a-310716ef83d1", "className": "com.open_care.huagui.rule.embedded.TieredRateConfigJson", "name": "TieredRateConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "97ed43f7-f6e7-4c89-8a98-b88138457783", "name": "matchRule", "title": "规则", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fbb60776-6bb8-4262-b36a-310716ef83d1", "valid": true}, {"id": "d3c0c5db-652c-48b7-a710-24fb54769c99", "name": "executeAction", "title": "基础费率", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fbb60776-6bb8-4262-b36a-310716ef83d1", "valid": true}]}