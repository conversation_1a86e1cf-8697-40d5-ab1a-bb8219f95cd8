{"id": "c35457a4-4fef-4155-85f0-f378c2af507b", "className": "com.open_care.medicalMicro.medical.OCBaseExamItem", "name": "OCBaseExamItem", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "基础检查项目", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04e885bf-5b09-45d1-b32b-9160d78672e0", "name": "labItem", "title": "检验项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "062cb85f-7b64-4534-9b12-3d3b859d5240", "name": "dateTypeResult", "title": "日期类型结果", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "08d7a1db-72ce-4242-9abe-579023563851", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "0dec1455-ed7c-4846-af11-1ac6d6317b13", "name": "describeIntoSummary", "title": "描述进汇总", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "106fb605-d773-4a46-a5bb-2d51c8e8245e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "135c1346-5d5a-4878-811b-04c478230439", "name": "needToCalculate", "title": "需要计算", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "1f406e3b-8050-463c-a6dd-eee114cb75a4", "name": "displayReferencePrefix", "title": "对比时显示参考范围前缀", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "1f96369a-6738-4e8d-a88b-edc7a4d2d66e", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "1f96369a-6738-4e8d-a88b-edc7a4d2d66e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2700693f-c193-4bdb-8b65-4e02238d91a6", "name": "qualitativeDataTypeProductAttributes", "title": "定性检查项目属性", "type": "java", "classType": "object", "refEntityId": "84f50ddf-d60f-4277-bc1a-e6544788b038", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "2a6ae110-95f2-426a-9961-a8db6235d090", "name": "medicalAtomicProductOcId", "title": "医疗原子产品OcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "2ccc4092-1cd8-43c9-bb88-39bf394be9a5", "name": "baseExamItemResultType", "title": "结果值类型", "type": "java", "classType": "object", "refEntityId": "6692b40c-27e3-41ab-bb5c-39a10a455124", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "feecd52f-03bb-4765-88a9-5160fee713d9", "style": "Select", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["baseExamItemResultType"], "properties": {"baseExamItemResultType": {"$id": "2ccc4092-1cd8-43c9-bb88-39bf394be9a5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结果值类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2e801043-9823-4498-8c61-2346babbcbca", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "2fab04bb-e2b9-4681-8f2e-7f29f5a01bc6", "name": "noReportGenerated", "title": "不生成报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "306a2114-f74b-44ba-acce-343e84ab38d0", "name": "displayFlagWhenAbnormal", "title": "异常时显示标识", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "3a412f02-5ada-49e6-ba53-73d619c1b14f", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "3ca58901-d0dc-4724-8986-470c966e5277", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "3ca7213e-a4e7-4e69-ae12-410416ce8cd2", "name": "displayReferenceWhenComparing", "title": "对比时显示参考范围", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "3cb2b67d-75c5-4c98-bcbf-7d3272f7169c", "name": "examPartDirections", "title": "检查方位", "type": "java", "classType": "object", "refEntityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "aa9d3b24-8026-45c6-81d7-493daf540e13", "style": "Select", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"examPartDirectionList": {"$id": "3cb2b67d-75c5-4c98-bcbf-7d3272f7169c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "检查方位", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3f872350-5e04-46ba-8faf-f6146285ce5e", "name": "displayConclusionWhenComparing", "title": "对比时显示结论", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "4302d742-100a-4b83-a958-91458a3fbc22", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "45a4349f-3b05-4deb-8248-6fa3b9147836", "name": "considerAgeRange", "title": "考虑年龄范围", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "defaultValue": "false", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"considerAgeRange": {"$id": "45a4349f-3b05-4deb-8248-6fa3b9147836", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "考虑年龄范围", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "46c9da52-948e-416e-b6b5-3b7bc2068bba", "name": "displayResultWhenAbnormal", "title": "异常时显示结果值", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "4f45e708-2d79-4d74-9876-14d2ff853c5b", "name": "medicalAtomicProductName", "title": "产品名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["medicalAtomicProductName"], "properties": {"medicalAtomicProductName": {"$id": "4f45e708-2d79-4d74-9876-14d2ff853c5b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "55fd54a2-19a7-459d-abe3-9ea3422e7da7", "name": "baseChargesForLocalMedicalServices", "title": "对应医疗服务收费标准项目名称", "type": "java", "classType": "object", "refEntityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "59d06d28-4976-4d51-8c6b-e0fe31772f99", "name": "needNormalConclusion", "title": "需正常结论", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "defaultValue": "false", "jsonSchemaField": {"required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"needNormalConclusion": {"$id": "59d06d28-4976-4d51-8c6b-e0fe31772f99", "type": "standard", "title": "需正常结论", "entityName": "com.open_care.jsonschema.JsonSchemaObject"}}}}, "valid": true}, {"id": "59d06d28-4976-4d51-8c6b-e0fe31772fd0", "name": "reportDisplayOrder", "title": "报告行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "65dd2873-4c5c-467d-aec6-a1a17d3158a1", "name": "displayDescriptionWhenComparing", "title": "对比时显示描述", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "6b3a3f7c-e8dc-40e0-b80a-8a5244248767", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "6b3a3f7c-e8dc-40e0-b80a-8a5244248767", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "711b0f19-1653-473b-9cbe-9ab4508e4a6f", "name": "sex", "title": "适用性别", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["sex"], "properties": {"sex": {"$id": "711b0f19-1653-473b-9cbe-9ab4508e4a6f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "适用性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7cbd91b5-8b9b-4d1f-846c-3ff6e6f5ba7d", "name": "displayUnitWhenAbnormal", "title": "异常时显示单位", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "97978a29-b6a9-499e-a268-c79aac4227e9", "name": "numberOfPictures", "title": "采图数量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "98120d87-efc0-4770-bba5-c0f27b2cc02a", "name": "canSell", "title": "是否可销售", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "98f71350-252f-49ab-9edd-1bad061a9af9", "name": "displayConclusionWhenAbnormal", "title": "异常时显示结论", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "a47b4b13-1231-44e2-9cbf-779fce476475", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "a5b2c75d-c1d6-4813-bf25-d5257a00cf28", "name": "useKeywordDetermineInterfaceValueSevereDegreeLevel", "title": "用关键字判断接口结果值重症级", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "a7f833c9-33d0-445e-858d-fbf2db963f8c", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "a8a530de-17e2-4bb7-a46a-03c43e1ccb14", "name": "displayFlagWhenComparing", "title": "对比时显示标识", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "abe27409-ba3a-4f9a-9972-78da035941d9", "name": "examItemConclusionsDisplayType", "title": "结论词显示方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "91589f64-7c02-4b67-896a-09d6038bdf66", "style": "Select", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "defaultValue": "popup", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"examItemConclusionsDisplayType": {"$id": "abe27409-ba3a-4f9a-9972-78da035941d9", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结论词显示方式", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b0957af3-77e9-4c9f-88d5-2c0487e4a41f", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "b527128d-3e05-4fdf-9d80-34abfca30fd8", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "b56a3d83-eecb-4a78-889a-6984a625768f", "name": "examItemType", "title": "项目类型", "type": "java", "classType": "object", "refEntityId": "b7d944c9-182b-4017-be00-e4e594e3ff69", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2e30df4c-68cc-44a7-8555-5f973ee1b7c7", "style": "Select", "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"examItemType": {"$id": "b56a3d83-eecb-4a78-889a-6984a625768f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "bbf68dbb-cbcf-49e5-99e7-a9592e147409", "name": "displayUnitWhenComparing", "title": "对比时显示单位", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "c7ee9091-3c90-4416-8cec-80d3fba4201e", "name": "useConclusionsToProductSummary", "title": "使用结论词当产品汇总", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "c915cf3b-08df-4469-876a-2808f86e342c", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "cd0c26d9-d5e8-4fe8-86d3-2b844cb9a978", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "ce2dfa4d-c33f-4963-add4-302e484911d8", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "cedc48ff-6f7d-439c-b15c-6e4d48672aa9", "name": "numberDataTypeExamItemAttributes", "title": "数字型检查项目属性", "type": "java", "classType": "object", "refEntityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "db277711-a856-4def-a292-8815c1a71042", "name": "baseExamItemConclusionAttributes", "title": "结论词列表", "type": "java", "classType": "object", "refEntityId": "21673039-e520-440e-9fd2-3fa4fff39e95", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "de1f7c9a-14dc-4267-afea-889477a4d748", "name": "optionalResultItem", "title": "非必要结果项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "e1f80c82-d0af-461f-834c-a06e467e37b8", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "e4330bff-e17c-4c42-bb8e-516c5f4f6436", "name": "showValueInLabel", "title": "需要界面显示", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "e44f4a3e-8460-49d9-9bcd-04e799654df3", "name": "physicalCode", "title": "体格检查编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "e4fd5d77-a281-4dc9-8ed8-ab2973f9cdbd", "name": "noSummaryGenerated", "title": "不生成汇总", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "e7f3dab0-2717-43ba-b2a2-89bc26c3e67d", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "ea8f5cb7-a7e8-4845-aef6-a51f09ae2d3f", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "eacdf2bd-e762-4571-9eb8-8c0716c6d829", "name": "noCompareReportGenerated", "title": "不生成对比报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "ef70adc5-cad0-4c3f-abdb-03870492b653", "name": "manuallyJudge", "title": "手工判断", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "f0bc36c7-3c2f-4253-b7a0-6ccf35689657", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "f11b2dae-faf8-4a08-a4ee-c77899bd03c3", "name": "displayResultWhenComparing", "title": "对比时显示结果值", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "f231e56e-a64f-44b5-9c46-9b47240b6ee6", "name": "displayItemNameWhenAbnormal", "title": "异常时显示名称", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "f231e56e-a64f-44b5-9c46-9b47240b6esdf8", "name": "summaryWhenNormalResults", "title": "正常结果也进小结", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}, {"id": "f231e56e-a64f-44b5-9c46-9b47240b6esdf9", "name": "displaySymbolWhenDisplayItemName", "title": "显示名称时后面符号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "valid": true}]}