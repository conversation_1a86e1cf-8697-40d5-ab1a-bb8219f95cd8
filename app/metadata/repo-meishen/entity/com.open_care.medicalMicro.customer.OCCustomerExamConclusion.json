{"id": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "className": "com.open_care.medicalMicro.customer.OCCustomerExamConclusion", "name": "OCCustomerExamConclusion", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户科室检查结论", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0ec75ba8-dc2d-4613-a4e1-c3e10887b4d1", "name": "baseConclusion", "title": "结论", "type": "java", "classType": "object", "refEntityId": "33adf15e-809a-42c8-bfb7-667c1c086445", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0d92d857-e3b8-4180-9067-6d4a5999490e", "style": "Select", "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseConclusion": {"$id": "0ec75ba8-dc2d-4613-a4e1-c3e10887b4d1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结论", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "188a72df-8c48-4086-8703-033fb561581c", "name": "description", "title": "体征描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "1d1442fd-ac2d-477b-827d-e5388d1748db", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "23e9364c-bea6-4515-85a3-7d7134ef0ca9", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "2d39704c-78ce-4c54-b8d6-253d13ba6609", "name": "sourceDepartment", "title": "来源科室", "type": "java", "classType": "object", "refEntityId": "6c6fcaa1-55f5-410d-b5fb-ad2ece9240a1", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a0a1fd48-21ab-42f5-aa19-a637ff87e436", "style": "Select", "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"sourceDepartment": {"$id": "2d39704c-78ce-4c54-b8d6-253d13ba6609", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "来源科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3664a237-75bf-4716-ae66-124bed5033dc", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "43ed0ac0-a822-4032-a5bb-ab6aea503b1e", "name": "examProductCode", "title": "来源产品", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "490fff60-43cc-41fb-a8ae-10f18b482ee8", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "49c6eb00-141c-41e1-b3cd-d209c1353221", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "52949072-fc91-45a8-888a-3d0d8e0e3d40", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "59098f3e-4b2b-4b62-8777-c28ae9a28940", "name": "suffix", "title": "后缀", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "5fd8321f-367e-4b6a-abd6-45a93ea7df80", "name": "conclusionProposalKeyword", "title": "结论建议关键字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "649a2f55-a543-449d-8dfa-963265aa5908", "name": "highRisk", "title": "高危", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "6d212957-e204-4ce8-afa7-7a25d5efbfa6", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "71ee180c-ba83-49fd-b1cf-b42c114e77f1", "name": "trackLevel", "title": "追踪等级", "type": "java", "classType": "object", "refEntityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "6159dfd4-3dec-43b7-8152-dc18fb3a5641", "style": "Select", "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"trackLevel": {"$id": "71ee180c-ba83-49fd-b1cf-b42c114e77f1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "追踪等级", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7981542b-b97f-4689-a631-589d1ff91981", "name": "conclusionSevereDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "style": "Select", "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"conclusionSevereDegreeLevel": {"$id": "7981542b-b97f-4689-a631-589d1ff91981", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "重症级别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7a239c91-8d20-48de-b34e-a0868ec89a26", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "7f28d3cc-5583-4c05-876f-b2af1abed657", "name": "customerExamItem", "type": "java", "classType": "object", "refEntityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "83904f09-6058-4689-b07a-811b3f2d6654", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "88050996-131b-4805-9088-23b45cc3cbe0", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "89213b32-2915-4682-838b-fbdf91953412", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "8d81c5f9-1037-43bb-bc2e-d91ead55dcdd", "name": "conclusionProposalContent", "title": "结论建议详情", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "9a77ffac-e8fa-42e3-98b9-e91c7544ec0b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "a8100042-f0a4-4300-9b5c-bc551a528006", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "a8b035b0-b0a4-40ad-ab7d-cd3903c9ab3d", "name": "baseExamItem", "title": "来源基础项目", "type": "java", "classType": "object", "refEntityId": "c35457a4-4fef-4155-85f0-f378c2af507b", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "510212ff-59b3-495e-a4e4-3b6eb151860e", "style": "Select", "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseExamItem": {"$id": "a8b035b0-b0a4-40ad-ab7d-cd3903c9ab3d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "来源基础项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "aaaf58f4-532c-48bc-b1ad-7ff11b84e404", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "af201248-0b7b-47af-925f-c36b1e14cdd4", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "b3f86651-22a8-4045-94dc-153aa72496b2", "name": "conclusionDisplayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "b69baf61-9bf2-4058-a3f5-ee31a1e8a139", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "c73436c4-1bac-4000-97ce-f58df9903d2a", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "ca51f50c-d425-41c6-851b-dc77d6031f61", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "是否当面告知", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "cf3605f2-9d17-437b-ba97-87bcd4cced24", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "d4d3c25b-347f-4f9a-9b3b-a7d80db50a06", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "d6bb7923-04c0-4ad1-8a9b-e53cd971d3f9", "name": "examProductOcId", "title": "来源产品OcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "dacde29e-2a2d-4bee-bbd0-7e8315cda172", "name": "noEnterPositive", "title": "不进入阳性小结", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "ddaa0e9b-970d-4d4b-a914-4f022c63c843", "name": "baseDiagnoseICD10", "title": "对应ICD10", "type": "java", "classType": "object", "refEntityId": "e87de778-8520-4460-860a-cfaa92e4efdc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "403c8624-eab5-460d-ad26-29f9a0623fd0", "style": "Select", "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseDiagnoseICD10": {"$id": "ddaa0e9b-970d-4d4b-a914-4f022c63c843", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "对应ICD10", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e0cf6df6-d8fe-4f1a-b989-1e863784de76", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "e297903d-0339-473b-83ab-953d800432c6", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "e2cd4836-48b7-46fd-82ec-5da655bd2757", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "e6b2ba54-75e3-425a-862a-d53bdeb19b83", "name": "examProductName", "title": "来源产品", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "ed1781bf-409d-4373-bde0-1f9d9a5580bf", "name": "prefix", "title": "前缀", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "f4f6e930-e516-4b6d-aad9-896b3175796c", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "f5e06cd6-a502-40d1-a1ad-b14f5157bd6f", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}, {"id": "ffe306db-5d54-4d96-9f3a-92f6df9c7df1", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "91d2a2dc-1e26-4d5c-9b17-75f9924bd3b4", "valid": true}]}