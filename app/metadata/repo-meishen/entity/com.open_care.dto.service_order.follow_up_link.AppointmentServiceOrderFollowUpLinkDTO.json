{"id": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "className": "com.open_care.dto.service_order.follow_up_link.AppointmentServiceOrderFollowUpLinkDTO", "name": "AppointmentServiceOrderFollowUpLinkDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "08d29167-0bd6-4f28-a442-c729e7103093", "name": "autoNextServicePowerJobInstId", "title": "超时检测任务id", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "22dc9a9d-2cab-4e19-bf9d-b570256d39b8", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "4f40d5af-bc3b-46b4-9d0c-0784f913ee0b", "name": "annualSummary", "title": "年度情况汇总", "type": "java", "classType": "object", "refEntityId": "f174a176-c569-47cb-bad1-a21e9df31ec3", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "55d6cc10-e709-4bf4-b291-5ef19fb1e760", "name": "taskInfo", "title": "任务信息", "type": "java", "classType": "ProcessTaskInfoDTO", "refEntityId": "492f5bd0-1e8c-4ef4-8ac2-9e00f486fadc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "6519feae-9fb5-47eb-8945-e509379ad62e", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "6710b5e3-aa88-4a99-a4fb-78b730d7e1ef", "name": "isCanEdit", "title": "是否可编辑", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "703a3ae2-a720-4d2c-bed8-2019bde04a96", "name": "appointmentRec<PERSON><PERSON>", "title": "预约记录", "type": "java", "classType": "FollowUpLinkAppointmentRecordDTO", "refEntityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "7d7104c7-81d2-4103-87b8-10a30d6096e1", "name": "rehabilitationPlanPowerJobInstanceId", "title": "康复方案规划的powerjob定时任务id", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "84743c06-7ff7-4523-ae65-6aaadaa11164", "name": "isServiceEnded", "title": "当前服务是否已结束", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "942d5054-4d55-4b92-9904-4710f4450589", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "9d478eef-0d71-4a2f-97a3-20375f4851c6", "name": "appointmentPlan", "title": "随访方案", "type": "java", "classType": "FollowUpPlanDTO", "refEntityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "a959c771-7ec0-470b-859a-959eed14bd9d", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "b8584b66-04fb-4180-be8b-3c35c023286e", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "c562a21c-0c99-46d8-ab06-0749cc23cd47", "name": "currentServiceCount", "title": "当前服务次数是第几次", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "c76c5985-907a-49cf-9248-4f7564561803", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "ce8cfdd3-f285-407b-bbe8-17a3f8c4eb96", "name": "recipient", "type": "java", "classType": "AppointmentServiceOrderRecipientDTO", "refEntityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "d39f7c32-20b4-49c8-8649-6f4a36c73edb", "name": "appointment", "title": "预约单", "type": "java", "classType": "ServiceProductAppointmentDTO", "refEntityId": "4182111c-37a0-4723-8fe9-2032f3ac3da7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "e0498d59-342d-4f32-a91a-f71279ca8421", "name": "recoveryPlan", "title": "康复方案", "type": "java", "classType": "object", "refEntityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "e240bff6-b5b8-4046-a23d-1e7457061bcd", "name": "followUpMedicationRecord", "title": "用药服务记录", "type": "java", "classType": "object", "refEntityId": "5bb7ef6e-2cc7-451f-904a-8690086c5691", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "f786703a-8ace-4f36-ae69-2756fb78a31d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}, {"id": "fa82609c-f1c0-4e60-aa9d-bc58f21e9a75", "name": "processInstId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ee542b91-ab65-4520-a00d-1cca6ed634b4", "valid": true}]}