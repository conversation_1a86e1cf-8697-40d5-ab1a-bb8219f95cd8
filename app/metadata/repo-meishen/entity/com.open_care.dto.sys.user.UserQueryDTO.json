{"id": "25c787f6-4326-438f-b182-9a6500f2fcdc", "className": "com.open_care.dto.sys.user.UserQueryDTO", "name": "UserQueryDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0742a83b-a4bf-4a6e-97ab-40013f011fd2", "name": "userNo", "title": "工号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "07a38abe-9c55-4feb-b97b-0cff040af796", "name": "userType", "title": "用户类型", "type": "java", "classType": "UserTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "94ccd4b0f2b0b3a886ae3077300a61dd4087c70d", "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "0a301a75-6bdb-4c07-b31b-0805b8efe4da", "name": "name", "title": "姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "0d26e086-71fa-4120-8117-15e69d78de7a", "name": "employeeNo", "title": "员工编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "133df5a2-a8c2-4a6d-8789-861e52a345a0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "13dae812-6031-481d-a7b6-c7ef481e6bec", "name": "status", "title": "状态", "type": "java", "classType": "UserStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ace4a7424bc8cce6f5a88fdf91813503246d032e", "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "1ae55017-4468-4cc9-8ad7-f5ee61b3c1d9", "name": "ownOrgName", "title": "直属机构名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "20a8a4a3-a6d9-4e7d-a7b4-9ed89e236b73", "name": "<PERSON><PERSON><PERSON>", "title": "角色名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "2302cbf4-e1bb-45df-bb9c-d018257f8305", "name": "superiors", "title": "上级用户", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "23985215-d859-4bcd-92c0-307a472da89b", "name": "groups", "title": "所属小组", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "4a2a1a2d-7102-4309-a151-a2e27bd4818e", "name": "supplierName", "title": "供应商名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "4d803972-13df-4e00-91ab-80be43b15ccf", "name": "username", "title": "用户名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "5a6dca40-349a-49a4-b338-97dc604bbce0", "name": "supplierId", "title": "供应商", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "6a6c55e7-e807-4afd-a50f-fb659dbd4222", "name": "roles", "title": "角色", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "3c7e0003-4d5e-4e73-a79d-abd40d00249b", "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"roles": {"$id": "6a6c55e7-e807-4afd-a50f-fb659dbd4222", "type": "standard", "title": "角色", "entityName": "com.open_care.jsonschema.JsonSchemaObject"}}}}, "valid": true}, {"id": "847613ff-e784-4bbc-9627-8553a38d269e", "name": "certType", "title": "证件类型", "type": "java", "classType": "CertTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3c0c713f4a40c01d2c9a28a771ee45aafb43e943", "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "896ecebd-2a58-418b-be8f-183c6ea8cb6d", "name": "businessName", "title": "商务名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "b0a39f57-16ba-42f3-8e41-1428e2b26c23", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "b2a08ff1-187c-41f8-9283-d0a1c9e68ad5", "name": "birthday", "title": "出生日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "b94f2e4b-91d0-4ef7-94bc-edce33755f67", "name": "ownOrg", "title": "直属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "be5c8af3-9d3b-4e97-be35-a088440fc24e", "name": "certNo", "title": "证件号码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "c47506cb-035d-48de-ad2f-ecada0d4915b", "name": "phone", "title": "手机号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "ce988f88-726c-465e-97bc-3c1a8ae10154", "name": "qualifications", "title": "资质证书", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "d393a182-e5aa-464e-b789-d1f0b5fac9a0", "name": "hiredate", "title": "雇佣日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "de5a732e-79a6-47ed-a991-1f7458083ae6", "name": "discountRate", "title": "折扣率", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}, {"id": "e378dc1d-4875-4f1a-acd9-98924759f84b", "name": "extensionNumber", "title": "分机号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "25c787f6-4326-438f-b182-9a6500f2fcdc", "valid": true}]}