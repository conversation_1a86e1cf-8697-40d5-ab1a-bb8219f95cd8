{"id": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "className": "com.open_care.medicalMicro.medical.OCBaseChargesForLocalMedicalServices", "name": "OCBaseChargesForLocalMedicalServices", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "本地医疗服务收费标准", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "125c9bfb-90db-4e30-aad4-f6e717ae5b77", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "1da3d073-0481-4161-8f00-04365a663c6b", "name": "code", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "480029ae-398d-4ac2-bc72-3f48f06b3210", "name": "unit", "title": "计量单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "4c8a71ea-376f-483a-8ae7-e42b3e78aac1", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "4ecb2273-0e9f-4395-ae74-c81a719c9534", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "508031f8-68ac-4837-a863-bbf8fab8f8e9", "name": "tertiaryHospitalCharges", "title": "三级医院收费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "69eb488a-c354-4891-99bc-40d42eed484c", "name": "chargesForSanitaryMaterials", "title": "卫生材料收费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "6b197cc1-db28-4832-987a-07275b48a10f", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "6c455ca3-1c23-4d45-a1d5-65d7ef0d499d", "name": "note", "title": "内容说明", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "99632f05-c386-4da8-ac84-e5d5fab6ef1b", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "b00128e6-1a7e-443c-855c-a1dbec65b2e0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "b6175424-5f02-475f-8e93-52052b230099", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "cb6f4094-6ad1-45d0-857c-a646b594894d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "d179c0a1-0129-40cd-9cc0-14fd47811398", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "d28a5f9f-b378-4ceb-b364-ce073bc14aed", "name": "itemName", "title": "项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "d2beea9c-d060-44a1-b9cb-43706a2d9c13", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "d43b3ff7-ca73-46fc-90f4-f302209459ee", "name": "baseCharge", "title": "基本收费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "d6a0b23b-3c98-461a-81ac-85c7dd2170dd", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "db3bd925-df2f-427d-9a15-afde63d7e034", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "df019520-f126-472d-8c8b-319e47c44087", "name": "parentType", "title": "父类", "type": "java", "classType": "object", "refEntityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}, {"id": "df618f11-3e6e-47e6-9de4-834e723de717", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b0e35b7-8cc8-4c06-95fa-5383fb3d9910", "valid": true}]}