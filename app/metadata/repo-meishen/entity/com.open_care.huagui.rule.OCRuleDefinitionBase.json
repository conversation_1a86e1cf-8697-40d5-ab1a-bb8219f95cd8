{"id": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "className": "com.open_care.huagui.rule.OCRuleDefinitionBase", "name": "OCRuleDefinitionBase", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "4d567de1-0acc-48d2-9457-1569a6bcb322", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "675c9cb3-124b-4288-b351-38b53970cd31", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "73223473-45c3-4955-827c-322bcc32bca6", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "7aaadddc-abcf-451f-a588-99549a52485f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "89f38f4d-eba7-467a-b788-22364d8b4928", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "8cfe1f99-7f71-4c90-857c-e13eac566d3e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "ad59d64c-5f64-4dd4-bbf5-13ba11a8539e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "be07ef0f-8428-4cf6-988e-fd559bf3a430", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "c94db4fb-914a-4435-9c0a-a089f6b97c61", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "cbdb2fcd-eb75-4e4d-b56f-0762b302458f", "name": "expiryTime", "title": "失效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "d3df68c5-c7a4-4073-8401-6d5c39a91b1f", "name": "ruleName", "title": "规则名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "d4b52d09-effe-4d66-a51b-a8cc52d7234c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "d90bf433-3866-44a4-8faa-14c6c00b3c7d", "name": "ruleDescription", "title": "规则描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "e59947be-6065-4ab9-b7bd-9d9da918e1c0", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "e7f91191-dcd6-43b0-a1f8-d86e9670799d", "name": "ruleType", "title": "规则类型", "type": "java", "classType": "RuleTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8d22f76ebdf5256c56b8bb2ae1cff8eb2eebb91a", "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "f00bd64d-bad8-4b33-abdd-5b6491274b78", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}, {"id": "fb8dc0b8-a509-40de-98f5-ea4467d9a773", "name": "effectiveTime", "title": "生效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a0319d3e-b742-4b9e-bec8-f1d8356b4dd2", "valid": true}]}