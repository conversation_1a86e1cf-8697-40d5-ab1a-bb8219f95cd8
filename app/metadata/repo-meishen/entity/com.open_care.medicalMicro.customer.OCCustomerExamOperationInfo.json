{"id": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "className": "com.open_care.medicalMicro.customer.OCCustomerExamOperationInfo", "name": "OCCustomerExamOperationInfo", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "操作明细", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1205da6b-cb93-4fe8-a0c4-cc3b22f805ec", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "16c1c258-054e-4065-b305-c8c2f3fad89d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "48e246c8-fa5f-4363-8851-d73a2610b322", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "4db07462-4e36-4a0a-809e-02e63f3adc36", "name": "operatorName", "title": "操作者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "6020f996-a583-4b21-b180-95d554c8d0eb", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "70011889-b5de-4d54-809f-fd3bd2ce45db", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "76d39d19-c3bd-4e38-8d6c-11a61451cc97", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "8b773863-32c4-42c0-bee4-36ac06635fad", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "91ef4df0-3781-41c2-a58f-28c182e62866", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "a935af86-1a96-4966-a6dc-972e364b6a04", "name": "operator", "title": "操作者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "adf54e11-7c00-4d26-87c8-f83e6482a439", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "ba336409-49e2-4d24-ad2a-35dcfdd5381d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "d69b070d-1a17-4306-a18b-0f833a142ace", "name": "operated", "title": "已操作", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "dcd8617f-17cd-4a49-a15c-c5777b053f1d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "de37086a-376e-4ecf-a588-a2b01740bf21", "name": "operationType", "title": "操作类型", "type": "java", "classType": "object", "refEntityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0102e025-6978-4978-a822-b49ffb5e9765", "style": "Select", "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"operationType": {"$id": "de37086a-376e-4ecf-a588-a2b01740bf21", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "操作类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ef79423b-97c7-4fee-8c50-ecb8fd454df1", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}, {"id": "f1b02f9b-29ab-4440-8fe7-60384bd95429", "name": "operatingTime", "title": "操作时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "valid": true}]}