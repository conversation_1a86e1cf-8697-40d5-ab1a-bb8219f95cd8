{"id": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "className": "com.open_care.service.process.task_hook.diabetes.JDChronicDiseasePrescribeMedicationTaskHook$CompleteEditTaskDataDTO", "name": "CompleteEditTaskDataDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0442a1bb-9087-42a5-ac12-af6b816cff97", "name": "attachments", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "valid": true}, {"id": "10b87523-9be7-451f-b472-b925642154d9", "name": "remark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "valid": true}, {"id": "4f2c200d-c6d4-4da0-a25d-d94c76872997", "name": "editEntityDTO", "type": "java", "classType": "Object", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "valid": true}, {"id": "6e32eee0-d608-4d37-834c-02affe2f238c", "name": "result", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "valid": true}, {"id": "dbdf48f0-3255-4c65-b1e4-0b4046477bbb", "name": "extensionVariables", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "valid": true}, {"id": "dd1b5104-d5c4-49c8-a659-98328671ad76", "name": "edit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "valid": true}, {"id": "fe2a516b-4029-40bb-8c9a-f0ea070187db", "name": "summary", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2f0eded3-66ee-40b1-a4ca-9a02ec1345da", "valid": true}]}