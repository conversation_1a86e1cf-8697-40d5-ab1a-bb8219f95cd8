{"id": "bba29499-49b5-4dfe-84db-41fe83c7309e", "className": "com.open_care.medicalMicro.log.OCGetCustomerDeliveryItemDTOListLog", "name": "OCGetCustomerDeliveryItemDTOListLog", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0d7ddbae-0dbe-448e-82e7-e84df5652de9", "name": "examProductCode", "title": "产品编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "1c87cd1d-1f1f-4f68-a4a5-9984cb02fdee", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "2126ffca-25ea-4c79-9f06-b89b3b7a9934", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "22b6d1df-faed-418a-b115-28b2044fdfd8", "name": "examItemName", "title": "明细项目", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "29509ae4-9364-4942-8dd9-5c839c8daa7b", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "2ad16d67-5156-4185-aaba-984cfb4c8931", "name": "patientName", "title": "姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "2cb56b2e-519d-45a9-aa20-2a10392d1b33", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "2d94ae18-0d6e-4d22-ac5e-0369f4343e9f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "3b2e1173-60b5-4df8-99b1-d6760b63552f", "name": "sampleDoctorId", "title": "送检医生id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "4166f6b8-ad9b-43f4-a568-dc049ce5d45f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "619dcad7-18aa-4f50-b61d-c908602decdb", "name": "examItemCode", "title": "明细项目编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "6523aa24-cb6f-4214-93e0-37faf0a197c7", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "7628789b-5fbc-46ff-8f1e-2b61366a27c7", "name": "barcode", "title": "条码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "7ddb46d7-0548-46cd-8910-6e1fe98e0049", "name": "deliveryOrgOcId", "title": "送检机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c69a5bbf-fac7-4a47-940a-1d841add43gg", "style": "Select", "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"deliveryOrgOcId": {"$id": "7ddb46d7-0548-46cd-8910-6e1fe98e0049", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "送检机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8bb3dbc9-f9b2-463c-9293-7e2491910414", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "9a26f25a-d98f-46a2-8146-3efa31602001", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "9afedae2-0644-4146-bec5-0b70bdbadd6d", "name": "sampleDoctorName", "title": "送检医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "a9200b57-a732-4575-b836-05f4c74e0920", "name": "examProductName", "title": "产品名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "bb2e082d-2b17-4cb3-aee4-89672218f76c", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "c6240d46-b4c9-466b-ae6c-f6a566ad8591", "name": "sampleTime", "title": "送检时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "cac7b895-abfb-41fd-b723-bd578e7fc624", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "cba21e26-a1fa-43f6-8650-04949817da3d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}, {"id": "cd3df71c-1cea-4d93-b971-c9d7800acdf9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "bba29499-49b5-4dfe-84db-41fe83c7309e", "valid": true}]}