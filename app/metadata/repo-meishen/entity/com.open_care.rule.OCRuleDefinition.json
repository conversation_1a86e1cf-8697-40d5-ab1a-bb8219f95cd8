{"id": "a45efad5-872a-4f45-90ff-89d2dfc9a787", "className": "com.open_care.rule.OCRuleDefinition", "name": "OCRuleDefinition", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "35db6162-fbd7-4ec7-8fdc-db15fa8c8fa2", "name": "ruleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a45efad5-872a-4f45-90ff-89d2dfc9a787", "valid": true}, {"id": "36dc3dda-5fa0-4f64-bba7-281bd9fb298d", "name": "jsonLogicValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a45efad5-872a-4f45-90ff-89d2dfc9a787", "valid": true}, {"id": "51d1cb34-dfa4-4dd9-bd8d-ebd57cbcb9e0", "name": "weightedRelations", "type": "java", "classType": "object", "refEntityId": "08498e29-6be1-4203-af22-95690901f6fc", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a45efad5-872a-4f45-90ff-89d2dfc9a787", "valid": true}, {"id": "a678d1c5-3fea-41e2-b11a-b1ec9ddf77ab", "name": "ruleExpression", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a45efad5-872a-4f45-90ff-89d2dfc9a787", "valid": true}, {"id": "be042930-35fb-4cde-addc-4db4ece461be", "name": "relationQuestionIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a45efad5-872a-4f45-90ff-89d2dfc9a787", "valid": true}]}