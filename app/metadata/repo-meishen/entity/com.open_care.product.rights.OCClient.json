{"id": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "className": "com.open_care.product.rights.OCClient", "name": "OCClient", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0c1de08c-6474-4126-9c62-afc3a71618c2", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "0e049328-552b-4705-acec-80ce54dcbcfb", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "0f0b05c2-43cd-49ab-affa-2973acc343f9", "name": "idInVendor", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "11c49b90-d291-43be-8df2-165583110c4e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "15e0d8c1-ce6a-406e-9949-dbfeeb0e1ad1", "name": "if<PERSON><PERSON><PERSON>", "title": "婚姻状况", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "993ee6a4-3473-40cb-8b57-cd303f295a11", "style": "Input", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"ifMarried": {"$id": "15e0d8c1-ce6a-406e-9949-dbfeeb0e1ad1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "婚姻状况", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "27b41bab-4ff4-4035-951a-62ab0e8303eb", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "34e7c7a9-7dd1-407b-811b-4259281cef03", "name": "groupId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "34e88a02-3403-49d6-b12d-f1bf9fd8a130", "name": "certNo", "title": "证件编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["certNo"], "properties": {"certNo": {"$id": "34e88a02-3403-49d6-b12d-f1bf9fd8a130", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "证件编号", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3b3e1883-3e38-4614-9ee3-6b6645b1dc5d", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "43e0551b-1958-4166-9c06-1ce744893a36", "name": "birthday", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "4946cc10-3a19-4aa4-84a1-601e172b8926", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"age": {"$id": "4946cc10-3a19-4aa4-84a1-601e172b8926", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "年龄", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "495db88f-210f-4ff9-b134-fc2a55fd0d34", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "504af360-d454-43ed-ad8d-77029dbd6ceb", "name": "name", "title": "姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "504af360-d454-43ed-ad8d-77029dbd6ceb", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "姓名", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "5cea8b8c-a69e-4304-96ff-f2b951fc114c", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "609e45f1-1767-4ff2-913c-a5bb3cf08d0b", "name": "userToken", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "68472a7d-3196-4b3a-b499-acb65f2a5d6c", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "74cfe8f3-03d8-40c5-85b0-6fce187ad26a", "name": "address", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "759abb7e-4f45-4df9-b01f-d3ee8a192a8b", "name": "preClientLevel", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "766690f2-aa6a-455e-be6e-8bdeb640db5d", "name": "postcode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "89103135-986f-4bd5-a87c-047825c0703f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "9e4c09b2-7feb-4877-abfd-00242a0a7ef3", "name": "certNoValidityDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "9e520240-155e-47a5-84fd-f49d09ba9f56", "name": "curOrgId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "9ea2399d-cad1-4609-ad3a-cdd06159f0f1", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "a7867006-a978-4e7f-88a8-c5500bf62ad4", "name": "telephone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "aa465454-1de5-49c4-9cb2-244436f1cdf8", "name": "isSync", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "aa4ce1b2-25df-42e6-bab9-f0ee744d9923", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "b6644fc0-a9a5-459a-b6c4-0a84e5889fb0", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "b6be1df2-da7c-43d8-8446-0a31371012e3", "name": "gender", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "db84b1db-086c-4824-9e63-44e415b3c498", "style": "Select", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"gender": {"$id": "b6be1df2-da7c-43d8-8446-0a31371012e3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d229d8ff-3a32-4b2f-9951-eb58c6b9dd6d", "name": "phoneNo", "title": "手机号码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [{"type": "pattern", "value": "^(13[0-9]|14[5-9]|15[012356789]|166|17[0-8]|18[0-9]|19[8-9])[0-9]{8}$", "message": "请输入正确的手机号"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"phoneNo": {"$id": "d229d8ff-3a32-4b2f-9951-eb58c6b9dd6d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "手机号码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d869ebde-d775-43f9-9a74-e2b00c468842", "name": "income", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "ddda6afe-2ec1-4b8e-94c0-353a9b80ab29", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "e091c2ab-058b-47b0-8cfa-aab3a203c590", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "e2050dfd-2d73-4973-afb8-647e74de5f78", "name": "certType", "title": "证件类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4897ce83-999e-4c57-955d-a830a1228bf1", "style": "Select", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["certType"], "properties": {"certType": {"$id": "e2050dfd-2d73-4973-afb8-647e74de5f78", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "证件类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e8b3f477-2462-4825-9ff3-81ae7339bcf6", "name": "clientLevel", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "efd3610d-d9ee-430c-bd20-b376509c0d03", "name": "occupation", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}, {"id": "f3e48eba-eeae-4821-b353-53aedfb2d71a", "name": "email", "title": "邮箱", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"email": {"$id": "f3e48eba-eeae-4821-b353-53aedfb2d71a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "邮箱", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f427e342-da0b-486d-bff8-229a997bf839", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "valid": true}]}