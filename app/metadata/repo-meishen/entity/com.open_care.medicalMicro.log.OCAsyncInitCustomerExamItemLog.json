{"id": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "className": "com.open_care.medicalMicro.log.OCAsyncInitCustomerExamItemLog", "name": "OCAsyncInitCustomerExamItemLog", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "032129a6-acec-4802-b00e-fb37b95cc84b", "name": "customerMedicalInfoInitValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "0e056e7d-4370-4f89-9275-059efb206845", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "14b83717-5571-4966-809b-00d58c96fa87", "name": "customerExamProductId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "17c7a51e-8e64-4315-afd3-bee9bc6f118f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "2d73cda6-cfc6-4913-91d4-0ef1b3cd62c8", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "31b87d9f-9c58-4cc8-a731-7d7cf729d2a8", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "5ec52644-312b-4493-8068-a8b2cd5c02fe", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "7dea35ff-98ba-4318-8953-e840653e8b41", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "80175c88-2e3e-425a-9936-4670fc89a59a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "8bdbc0e3-957e-4e17-82cf-e82e0ee0a66a", "name": "customerMedicalExamInfoId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "991d9aa0-c40e-4290-9bba-b0df930534ae", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "b0534b54-4d3d-4010-9a38-f83547f600f6", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "b9b93b9e-e140-4653-8128-b8a88b1df2ab", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "c15247ad-25e1-4b06-a709-7395cfe11f85", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "c69dc484-5340-44c2-b972-238870528c70", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}, {"id": "cd56073f-b499-4458-b3a2-f99c79254c7b", "name": "serviceCode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d30d35bf-7c01-4ffc-bc2a-2a1f265d440b", "valid": true}]}