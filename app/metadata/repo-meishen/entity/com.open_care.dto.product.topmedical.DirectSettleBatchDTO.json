{"id": "06df03fd-98da-47af-be17-adad01e969cb", "className": "com.open_care.dto.product.topmedical.DirectSettleBatchDTO", "name": "DirectSettleBatchDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "052d3771-0a2c-41e0-8f02-0e8d09e39e5a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "06b2cafa-2f09-4173-80fe-86359781a1c0", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "07955e21-e1ee-4f07-a877-86521cb76d21", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "1ccfe149-60a1-4754-a369-412a0fdb9a46", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "1d418ff6-76d0-4ba3-813f-30468074bd51", "name": "isSingleInvoice", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "22b5a579-1ace-4f22-9f5a-bb787c72e931", "name": "batchSumAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "2c9ebaad-35ff-455d-aff4-b9e8d51dcd98", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "2d48bdc9-e88d-4757-8592-4c2b7e2ff575", "name": "hospital", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "303cf530-e585-4717-8234-06633db5305f", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "438839e2-36a2-4d56-9c76-1fe23bcc6c0e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "43f97063-5d63-4ab6-9977-a7bc4092f152", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "468174e9-13fd-48b2-ba87-cce757c38c18", "name": "payEndDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "47981ad3-9f4a-4815-ab4f-53da540d50ef", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "6cb255b8-f969-4001-8c48-cdbac46b99b3", "name": "hospitalName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "74f5ce7b-9689-4342-be96-ad628692b1c0", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "7d983bd1-72e8-422c-a239-0291a3079ba2", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "8ff07835-8dd8-4902-9202-819ae27d4b30", "name": "ifInvoiceSubmit", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "9cc2ab95-4b71-4d06-afc8-0e298cc89acb", "name": "billSubmitDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "a5b8bd56-c334-4c55-8331-af5319989f79", "name": "backReason", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "a67a2ee6-8775-4b73-9820-2e3d4c7365a5", "name": "caseCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "c89bfdc7-fe16-4eff-96b4-f905a16125e7", "name": "operatorOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "cad88fa4-e061-46f5-9812-6ae4b53d7698", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "d2a38884-f1bd-491c-9565-b7815197503f", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "da8441b4-8d88-42e3-9103-b8f009830628", "name": "currency", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "f3474069-9c5b-4c4c-ab9b-9475b4c75410", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "f844a470-5fe1-48d3-ad04-d7219743dd3b", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}, {"id": "f9921273-7bbe-43ea-a6a4-690bd009ff67", "name": "billReceiveDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "06df03fd-98da-47af-be17-adad01e969cb", "valid": true}]}