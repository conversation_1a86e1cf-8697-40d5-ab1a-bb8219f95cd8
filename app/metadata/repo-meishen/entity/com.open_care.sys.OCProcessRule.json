{"id": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "className": "com.open_care.sys.OCProcessRule", "name": "OCProcessRule", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00f44c53-1e75-4847-817c-907cf4e3501d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "3d901111-6ff1-4ca0-9695-219daf5fd684", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "43273659-c537-4882-ac7e-4f129cb6a37d", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "4465f9b3-c498-4063-b28f-ad43e05e9558", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "4b4b6dbd-9fc0-4bf2-a7d9-e3ec25bbed0e", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "6ba1a910-4c82-4cd1-8ecc-1d9e9ed6a05b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "72e826d5-0ec9-4258-bcad-6d4755cf3f34", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "78cf8ce7-dfcd-4597-abd4-b7e67fbec218", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "8d8ea695-0b3b-4793-9089-6a0b070cc43f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "9c0c49c4-573a-49b7-a767-103a94a40310", "name": "flowNodeRules", "type": "java", "classType": "object", "refEntityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "ad2a59d8-ef07-43e9-b55a-24d545d8f0c3", "name": "processDefKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "ad93d9c5-0f29-4bd5-a92c-e6ba6052477b", "name": "prevMainProcessFlowNodeKeys", "type": "java", "classType": "object", "refEntityId": "9d555168-7a0d-490c-bc32-a69ab01ec993", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "b5c4da96-5f51-4471-a480-bc56c727cceb", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "bb54448a-a310-4267-9f29-893512205f6a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}, {"id": "f502dc09-b28b-48f2-9aa9-8d995fb10015", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "valid": true}]}