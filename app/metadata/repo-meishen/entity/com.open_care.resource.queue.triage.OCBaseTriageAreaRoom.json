{"id": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "className": "com.open_care.resource.queue.triage.OCBaseTriageAreaRoom", "name": "OCBaseTriageAreaRoom", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02303626-d941-4c82-90c1-7b90bf8f22e0", "name": "available", "title": "可服务", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "05c82607-0080-497b-9b10-7d4a23a28078", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "0f71721c-054f-4bde-8d12-b79fdde1eafa", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "0fa594ca-9728-4668-ad28-7a2e061b6c95", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "1a5b3da1-bafe-457a-9147-0eeca24093c1", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "1f299a5f-08cc-44ee-a8d4-89a827f175c5", "name": "occupiedCalling", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "25a4a0b8-a4af-4a3d-bb24-d45e85197db2", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "37a9e12d-0357-45b9-a9b7-d5ed029f884c", "name": "resourceName", "title": "资源名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "388891fd-1dc7-4e39-ba96-bc5d2d6bdd73", "name": "shortName", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "3e5ed08e-b5c3-4202-920b-d8404976248f", "name": "resourceGroups", "title": "资源分组信息", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "66c1671c-7d2b-43a7-83b8-913d3f85d56e", "style": "Select", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"resourceGroups": {"$id": "3e5ed08e-b5c3-4202-920b-d8404976248f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "资源分组信息", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "46a1387d-3947-4d8c-aad8-414ba27ff8e6", "name": "initialNumber", "title": "初始人数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "46ab45c7-e3a0-4828-9e6b-a117b311944e", "name": "party", "type": "java", "classType": "object", "refEntityId": "39f9bf4f-b0fa-41eb-abf1-0d2dfbb2c2ab", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "47507375-6361-411e-ace7-63ef16260b78", "name": "doctorIds", "title": "诊室大夫", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "84eee9c7-2c11-4751-94e3-8fa86258664c", "style": "Select", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["doctorIds"], "properties": {"doctorIds": {"$id": "47507375-6361-411e-ace7-63ef16260b78", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "诊室大夫", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4b3a5788-61ca-49f6-888e-671eeb79fbde", "name": "weight", "title": "权重", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "51aad83e-23bf-4761-adbb-56379df964c4", "name": "calcServiceDurationStrategy", "title": "服务时长计算规则", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f0b8c546-5ee5-428e-8221-7830d4d01944", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "5574b5a4-7d19-4460-ae6b-ce3ef71020a7", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "5cd5e5ce-1328-4f28-b127-8138028a9b41", "name": "job", "title": "职位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "6246eaa2-16de-4e89-95b8-8dab4cfbeb14", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "6526e8a6-2339-4587-b20e-6dbdd5c4dc49", "name": "display", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "67aea89e-8d1b-4586-84fd-adddca8f4d9c", "name": "resourceProperties", "type": "java", "classType": "object", "refEntityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "6c4917c9-d1e2-442e-b875-2ba1aaecfe5f", "name": "departmentResourceGroup", "title": "科室资源", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "66c1671c-7d2b-43a7-83b8-913d3f85d56e", "style": "Select", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"departmentResourceGroup": {"$id": "6c4917c9-d1e2-442e-b875-2ba1aaecfe5f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "科室资源", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "70147096-90bf-475b-9810-a327a568cff8", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "77907f97-4fcf-467b-b85a-6ac09597ee33", "name": "baseTriageRoomGroup", "type": "java", "classType": "object", "refEntityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "7801a710-c7d8-4731-acbb-371474c96665", "name": "locked", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "793f46ff-443f-4e6f-98ae-afc9b98f35bc", "name": "remindMessage", "title": "提醒话术", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "872029a8-ab6f-40f5-87cf-29c9cdee9e61", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "88ee0e10-4c68-4452-80eb-cb3263181180", "name": "matchSex", "title": "适用性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "8dd7fdbe-dd67-47f2-bb1b-40252ad5b31a", "name": "servicePlans", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "9047b302-51d3-4258-b8fa-554d4ddebeb9", "name": "tags", "title": "标签", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "965092ff-694d-49cf-927c-2240a3357853", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "989ea653-b2a8-4864-91e5-272691065626", "name": "serviceProductList", "title": "服务产品列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "36da498f-388e-493a-bfe6-80058963245c", "style": "Select", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"serviceProductList": {"$id": "989ea653-b2a8-4864-91e5-272691065626", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "服务产品列表", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9b5c97bc-0409-439d-b343-84c03627ee68", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "aa5127b0-ccec-4221-9c49-4aa8e749d27d", "name": "maxNumber", "title": "最多人数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "ace00f66-9c2f-4c2d-95b7-db93248372e0", "name": "notEmptyAsPossible", "title": "不可为空", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "b70cc89f-0f83-416d-aa06-f50897b4ffa0", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "b92ae717-2e9d-4807-aef4-729fb6d60ab6", "name": "resourceCategory", "title": "资源类别", "type": "java", "classType": "object", "refEntityId": "516dcdb4-8f83-4098-ad59-65da46ff2174", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "bead185d-bafa-423a-97d9-0834236dd0d2", "name": "ipOrHostName", "title": "IP或机器名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "c1a67bda-517f-40d4-a6e1-686acca43c6a", "name": "canAppointment", "title": "是否可被预约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "c5211fd2-477d-411b-bc05-e48021ceaf79", "name": "name", "title": "房间名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "c5211fd2-477d-411b-bc05-e48021ceaf79", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "房间名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c77bcd66-ce42-4d56-bc36-27703c469e1c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "c7a9fe60-dc0f-4b22-9ec1-2508642e7f5c", "name": "partyRoleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "cad19708-5359-47fc-afff-974ea640ee04", "name": "description", "title": "描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "cb115fb8-bf43-4e8f-a365-72fd0a3c0a8e", "name": "no", "title": "房间号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["no"], "properties": {"no": {"$id": "cb115fb8-bf43-4e8f-a365-72fd0a3c0a8e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "房间号", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d13fa753-3ec3-4067-8a95-0033ab5eaafb", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "dbbdeaf0-fd74-4a99-8a85-ade5acb38f2f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "dcd59813-d479-4f4d-b401-76f5f7f2e3d1", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "e4f3bae4-f5b7-48a9-ab11-829074d41875", "name": "occupiedUpperLimit", "title": "占位人数上限", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "ea0b458c-e3e5-4b50-a8ef-81a235cac1c2", "name": "code", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "f4feee0a-c1b4-4509-ac4a-65312acd7be7", "name": "planAndContexts", "type": "java", "classType": "object", "refEntityId": "5df4fe41-a389-4c02-a1b0-1a74ef3cb5ce", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "f5335d45-f0a7-4175-9949-1a0a06e60ce7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "f5cd6479-dfc5-47ff-b102-7868111f55f3", "name": "profitLevels", "type": "java", "classType": "String", "refEntityId": "ed205dce-6594-4773-b0d7-1b2ce4d21f46", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}, {"id": "f62db016-f99b-4dda-80c8-7fbfeb8b35ca", "name": "baseTriageRoomDistances", "title": "分诊室与工作组关系", "type": "java", "classType": "object", "refEntityId": "97bbb1e6-c57f-4dd3-a352-c693a2a00e52", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "valid": true}]}