{"id": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "className": "com.open_care.product.supplier.OCMedicalGuide", "name": "OCMedicalGuide", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01eaf35b-abc0-4f36-8023-3b361297eca1", "name": "invalidReason", "title": "无效原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "06e652a6-ae72-45ea-a0e8-18ecbb6c71cb", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "0bd3abcb-9a54-472f-93ac-024a734f2861", "name": "supplier", "title": "供应商", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "10c913da-2929-42b1-8da9-7cf81281d46d", "name": "currentAuditStatus", "title": "当前审批状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "1c956bc4-33d0-4c1c-acfd-e8ddbc38cdd6", "name": "name", "title": "供应商服务项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "2952bf81-e3c7-4e02-80fc-1d3c188a9138", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "3716d946-0a39-495f-9978-84f562f825c1", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "3c765633-877b-4e07-8cc0-0986bdde8b82", "name": "material", "title": "是否有物料", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "535fded9-c3dc-4156-b286-ead981c03ce4", "name": "deliveryMethod", "title": "服务提供方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "5662241e-568f-4109-a4aa-79a27658d2b5", "name": "contract", "title": "合同ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "5dee87ea-d97e-4b94-b58d-9be33d6ad642", "name": "materialInfo", "title": "物料信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "637e2710-26e8-4ba7-a1a0-a334ce67fc89", "name": "productNo", "title": "供应商服务项目编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "65a00a3c-56d5-48f5-bdcc-dd06d4955025", "name": "explanation", "title": "服务项目释义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "67879e90-fc64-4201-bf83-90aaea59095c", "name": "canInspects", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "6c42c71b-2742-417e-809b-ec9760b52eb3", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "6d6df5eb-5c97-42e3-b133-2f3167106abf", "name": "draftsOperator", "title": "草稿箱操作人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "71754eca-81a0-45fd-bcd1-625df5fe0d04", "name": "doctors", "type": "java", "classType": "object", "refEntityId": "22415515-59c9-494f-b142-b88191410ab1", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "72f6a008-6a98-4c34-96e1-99fd2176093c", "name": "enable", "title": "有效", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "7e0f8c38-7e0e-4c60-b82b-ffab5fc11548", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "85317062-b7d3-4e9f-8283-35558a117334", "name": "content", "title": "供应商服务项目内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "8e7bc86a-8e10-4b2b-9828-122f2f98ffb9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "91a56d86-45e0-4341-92ae-20b4aad51254", "name": "currentAuditBusiness", "title": "当前审核业务", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "969b42b6-91ff-4727-ab6c-fb5a42747641", "name": "externalRemark", "title": "对外备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "a947335f-216a-47c8-9df7-ab67e89e29e3", "name": "product", "title": "产品", "type": "java", "classType": "object", "refEntityId": "83b77f63-f957-46ca-a1b8-e5d556f59fcb", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "b4767351-80d6-4b5c-858f-9e768cda19d2", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "b4dbb9b8-b9c3-4504-83b9-9914ab486479", "name": "thruDate", "title": "结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "b7bd52cf-d861-410f-b2f5-0e0040f867e1", "name": "comments", "title": "提交备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "b8d4cb38-46a6-4536-9ac7-f0141d92048b", "name": "drafts", "title": "草稿箱", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "b8dd8136-b68f-41d9-bc68-f440bfac8f1b", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "bb1f7f6f-f9f5-4b66-9d5c-2e0039e01e86", "name": "departments", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "bd671062-5f9f-438c-a1da-37fde6b8bfcf", "name": "lastSaveDate", "title": "最后一次保存时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "be1c53ae-dd02-4209-aaa8-9c3aa51f4b5f", "name": "fromDate", "title": "开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "c6de9050-d94f-42ce-a45b-e7cb433c7653", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "c7b5518a-a711-4120-ae4c-7037789793b6", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "cf499cc1-787b-490a-8db6-d1e2acb6e9fa", "name": "preStageId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "d13f5f2f-3233-48c9-bdaf-4fe6424ee082", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "deb00290-1905-41ea-a320-adb5c1706996", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "e2ecdded-ae2a-4006-b87d-169fa463c278", "name": "internalRemark", "title": "对内备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "eab4a097-57a6-41ad-93d7-9e7fc2553444", "name": "advanceBookingTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "eb0b1866-6f5d-4145-9133-27a307787468", "name": "h5Url", "title": "供应商服务项目H5页面Url", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "f9534e8f-75e2-46b8-89d5-ec987366e57c", "name": "landingForm", "title": "落地形式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}, {"id": "fce9746f-d15f-4465-9607-0c48fc348191", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "cb727a1f-1abf-4e84-b351-abd1b6ab105c", "valid": true}]}