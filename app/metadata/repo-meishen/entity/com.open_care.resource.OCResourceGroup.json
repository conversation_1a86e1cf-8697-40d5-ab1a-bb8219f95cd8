{"id": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "className": "com.open_care.resource.OCResourceGroup", "name": "OCResourceGroup", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "08d8c3b4-fc9a-41fc-b2dc-06d16bce5348", "name": "servicePlans", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "0aa6a978-5e51-40c0-bd78-c8c2f833913a", "name": "resourceCategory", "title": "资源类别", "type": "java", "classType": "object", "refEntityId": "516dcdb4-8f83-4098-ad59-65da46ff2174", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3f42148d-759e-44e7-9db7-615f8450c891", "style": "Select", "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"resourceCategory": {"$id": "0aa6a978-5e51-40c0-bd78-c8c2f833913a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "资源类别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0d8989f6-4598-41a9-b858-5e65a31533a5", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "21a9c9bc-4add-448c-9625-761ce7580ed0", "name": "partyRoleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "2895b61c-fd28-46c0-a8e3-cab706b0b022", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "34b8fe9c-6d50-4314-be57-c37d1c643b92", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "38d95bee-71af-4da9-b6f1-8acda39643f8", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "3a1446fa-848c-44b6-9fa9-97d57c69dcc5", "name": "description", "title": "描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "450d784a-232f-4fdf-9a41-5cddb2d57294", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "47948c70-dc4c-464e-be26-71fa21dadc38", "name": "resourceGroups", "title": "资源分组信息", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "558ed7a5-dc16-4fb9-b7ab-b7cc70c8aebe", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "581ed3bb-676c-4eaa-93f8-e4eaccbd64c2", "name": "medicalDepartmentInfo", "title": "医疗科室信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "5c83704a-9ad5-433b-ba00-21ae8766d776", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "5e9be884-b5ee-4189-a44a-28bf56262993", "name": "resourceProperties", "type": "java", "classType": "object", "refEntityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "717789e4-41d9-4ef7-9093-b384d7be56c0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "72cae7b5-5eb9-4f22-9cde-764d43dbe943", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "76822ef8-8855-40a4-a0f8-11f26a268fb0", "name": "resources", "title": "资源", "type": "java", "classType": "object", "refEntityId": "7dd3506b-c0a7-4d8b-8def-73c2cc121012", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "79284477-91d8-4ddb-8a14-ed240e37d4bc", "name": "children", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "7ed6d6d6-1364-496c-829c-f4515de3f85a", "name": "canAppointment", "title": "是否可被预约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "81fea598-387c-4ac1-95a4-a65c7a799407", "name": "profitLevels", "type": "java", "classType": "String", "refEntityId": "ed205dce-6594-4773-b0d7-1b2ce4d21f46", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "86642f4c-96c6-4fa4-abc1-270b2d4ddc81", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "8e9b402b-25fc-41d6-904e-52ac4150c983", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "8f15bb02-39db-43e6-a8cd-46fc3184bcbe", "name": "party", "type": "java", "classType": "object", "refEntityId": "39f9bf4f-b0fa-41eb-abf1-0d2dfbb2c2ab", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "9b3e9f73-7037-4dc7-925d-6e3001204c1f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "9e5c3ce7-8164-4ca3-a6ff-f4f1ad5a6931", "name": "planAndContexts", "type": "java", "classType": "object", "refEntityId": "5df4fe41-a389-4c02-a1b0-1a74ef3cb5ce", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "a42d5816-ea09-4052-9087-97f0c3fdb9ef", "name": "parent", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "bb16b8cd-c092-49ba-8f4d-648d6d0ace4b", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "c72a4ce1-1aa3-4ada-ac18-560e54e11c4e", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "c86adb62-70e9-423f-9eb1-c067a88529d6", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "c8ae4899-06d2-439f-8a95-ca2e6098b7be", "name": "tags", "title": "标签", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "cc941d0a-05b0-48a1-8c7f-7b2cff07ed10", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "cd4ef897-900e-45e3-afbe-2951b5ac7d45", "name": "resourceName", "title": "资源名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["resourceName"], "properties": {"resourceName": {"$id": "cd4ef897-900e-45e3-afbe-2951b5ac7d45", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "资源名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d73eff43-16ca-443b-9eb7-cb5cc1295edc", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "d75a858c-92cd-4362-82fd-883f33f8e84a", "name": "job", "title": "职位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "d8de2c98-b653-4d5a-b7b8-839457d53bba", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "e31a6f38-07ff-4685-bcb2-f59c62ef94ca", "name": "shortName", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}, {"id": "e9dceeaa-5c39-460f-ad11-9d06343c9699", "name": "code", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "valid": true}]}