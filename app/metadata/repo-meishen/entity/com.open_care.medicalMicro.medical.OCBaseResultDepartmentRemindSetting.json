{"id": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "className": "com.open_care.medicalMicro.medical.OCBaseResultDepartmentRemindSetting", "name": "OCBaseResultDepartmentRemindSetting", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "科室提醒", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0506599c-6d32-4e8e-92d6-7d3bd850177b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "0d9936d3-523f-497b-afa5-960ff4dd26aa", "name": "baseResultRemindSetting", "type": "java", "classType": "object", "refEntityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "257344f3-8afb-469b-809d-1d9d16be580e", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "4092eb18-1b4d-449f-b57c-ca092a603f3a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "4216749e-8af9-4717-b10b-84862b0569a3", "name": "remindMsg", "title": "提醒话术", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "6c084f9a-8fb2-475e-be47-e82fcffb90d0", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "822ca004-0881-43fb-a9d1-a3ee28b3a5e4", "name": "remindDepartment", "title": "提醒科室", "type": "java", "classType": "object", "refEntityId": "6c6fcaa1-55f5-410d-b5fb-ad2ece9240a1", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "da973ffe-91ee-4e34-bf16-d78cf63aa13v", "style": "Select", "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"remindDepartments": {"$id": "822ca004-0881-43fb-a9d1-a3ee28b3a5e4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "提醒科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "844e3260-5582-4f11-8c40-566bf35f2e85", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "85a32a9c-0e06-422b-894a-e71609777720", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "a599cae0-f96a-4e03-993f-16e24890359b", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "bdfd063f-6b26-412d-8a8d-362210060805", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "cfd7c1ea-d5cf-4f9e-afed-95cf3d6dd0d5", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "f0f6238e-1d8d-4e79-a707-2f2d60e9c6be", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "f1b1951e-ce94-45c3-a568-92ec595c3a3d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}, {"id": "fd799117-a9a8-4b03-bfd8-73f6e852c085", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "valid": true}]}