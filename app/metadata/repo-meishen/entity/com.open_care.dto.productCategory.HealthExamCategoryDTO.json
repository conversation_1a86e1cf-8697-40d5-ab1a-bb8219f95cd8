{"id": "4135faaf-2026-48b0-9d70-e6525850d869", "className": "com.open_care.dto.productCategory.HealthExamCategoryDTO", "name": "HealthExamCategoryDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "06175aa0-0f22-4f21-a6f7-949367763d46", "name": "hospitalService", "title": "医院服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "0d3a41e2-16c0-496f-a123-f747e18a815b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "47bf69e5-05ad-47ac-ba0d-a390f74d77a1", "name": "groupMaxCapacity", "title": "团检最大容纳量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "4f7edd6e-93d6-40fc-b5b0-93f02e25db2d", "name": "partyRole", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "5011f71e-43ee-4fce-bcc4-001bcfdfd18f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "5b42421a-6918-47a4-8fc6-c258b59ea109", "name": "specialInspectItems", "title": "特殊检查项目", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "77795ea0-bb6a-4b75-b30a-5f5f5cb28519", "style": "Select", "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"specialInspectItems": {"$id": "5b42421a-6918-47a4-8fc6-c258b59ea109", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "特殊检查项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "69439d2c-f6e7-43c8-bfea-07219f65a376", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "6f3adfbd-9946-4db1-addc-7f62a7de5499", "name": "reportTime", "title": "报告提供时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "82c5c937-f588-4646-9124-064b827bb400", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "952d10dd-6746-4af3-80bd-581649346a50", "name": "parentCategory", "title": "父类别", "type": "java", "classType": "String", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "af9bfa57-4781-werb-a1b0-f81dffeeb35e", "style": "Select", "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"parentCategory": {"$id": "952d10dd-6746-4af3-80bd-581649346a50", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "父类别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9c5b94de-ddb0-4e38-ac86-a5101d1cc477", "name": "reportForms", "title": "报告提供形式", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "20e4b89a-b11e-4117-9fe1-9cfd677077cc", "style": "Select", "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"reportForm": {"$id": "9c5b94de-ddb0-4e38-ac86-a5101d1cc477", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "报告提供形式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}, "reportForms": {"$id": "9c5b94de-ddb0-4e38-ac86-a5101d1cc477", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "报告提供形式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a2fb372c-b466-4856-b652-966884d7a593", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "a4eed83f-0884-4a6f-91d5-4726ec09081b", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "a64e140e-7d84-42c3-877c-2d23f071bfab", "name": "internalService", "title": "内部服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "ae20fc0f-c9b6-4071-940d-2ce90cd360af", "name": "party", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "b26588cc-1230-4fed-9307-7bf743195cfc", "name": "businessHours", "title": "营业时间", "type": "java", "classType": "object", "refEntityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "b9becf34-0c15-46f0-a731-35131f43cb29", "name": "productCategory", "title": "服务类型", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f657a2ec-e3aa-4b1d-a621-3dcceea15325", "style": "Select", "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"productCategory": {"$id": "b9becf34-0c15-46f0-a731-35131f43cb29", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "服务类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "bd38e44b-5ba9-4ed6-afef-57864fa83b62", "name": "receptionPlace", "title": "体检接待地点", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "c1b596eb-d44f-47ef-894a-2a284dc7a447", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "c64fe29e-f5de-4c50-8034-ab420526243a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "ca4d06c3-2da3-4c24-832a-c4eeba72d3c5", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "db102906-88f7-4dbe-9db9-2089d3719b06", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "e3f582aa-e4f9-41fc-aa37-6ecce0457519", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "f55607a6-d6d0-4206-a28a-f35b9cd400c7", "name": "contract", "title": "合同ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "fc9af960-4650-4f05-9917-b39775428eae", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}, {"id": "fcadc2aa-c3dd-4352-92f6-8ac52cf53cb8", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4135faaf-2026-48b0-9d70-e6525850d869", "valid": true}]}