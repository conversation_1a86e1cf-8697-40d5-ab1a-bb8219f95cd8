{"id": "5a79ed74-4a16-427e-b37d-542abddbcd58", "className": "com.open_care.dto.supplier.ContractRelationShipDTO", "name": "ContractRelationShipDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01ef4ba0-3c5e-4dd5-b4b6-aa62f36d7604", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "1dc4883a-bf3f-4298-a9bb-42594a98fdcd", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "233daa1a-e262-4f4e-a828-f7124c2b85f0", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "36c2746f-632d-4ee4-98f9-16bd8809d68a", "name": "contractOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "44488a3a-4b1e-40cf-a2c8-542c675efc20", "name": "contractType", "title": "合同类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "bf0c1b19-4388-480b-a41e-88e7d149b19e", "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "51c95e17-94d8-4bfe-9b7c-2ea715104b53", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "88ab8b90-b4e6-47cc-9f53-77273e3ae991", "name": "contractStartDateEndDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "b25a4fa8-0c0a-45a3-9c86-040205249ba5", "name": "contractRelationShip", "title": "合同关联类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "052bb4ab-f6a0-44ec-94ac-b0016e5f3c4b", "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "b2caba29-d34d-406d-ae13-b0982534a61f", "name": "contract", "title": "关联合同详细信息", "type": "java", "classType": "ContractDTO", "refEntityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "b31ef1f1-16d8-45bb-a415-2adb74d8706c", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "cd67dda9-3ec7-4fcf-9944-37245910d95d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "ce3c9f3d-b6fd-41fa-a837-d34a59d7af4e", "name": "contractName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "cec6ed79-396d-4e33-b112-106374c8e402", "name": "contractCode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}, {"id": "d25d1112-3da1-44fd-80c6-c49c00033204", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "valid": true}]}