{"id": "f2213066-01ef-49d2-9cd2-8d238291e24b", "className": "com.open_care.dto.service_order.AppointmentServiceOrderRecipientDTO", "name": "AppointmentServiceOrderRecipientDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "07e442e5-3561-4619-a0aa-18922fb28fcf", "name": "date", "title": "付费日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "0d78c776-216b-4d61-b5fa-e4d5e62cff4e", "name": "actualDateForHospitalArrival", "title": "实际到院日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "1a706a3d-60ae-4911-a5bb-3eb4b52b02c0", "name": "age", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "204987bf-db55-47d7-8cb0-8d194be483e1", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "22562942-7d1f-476c-be01-ea7e91e76d45", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "4d33b105-7928-4aab-aee1-5eff16809e48", "name": "contraindications", "title": "已告知禁忌项", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "5800106f-3446-46ea-b722-32d1e68777d4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "62f75086-7a45-4fe4-b689-064c0dd9f3f3", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "683df2df-7311-46c5-a1c1-0a9683174838", "name": "appeals", "title": "客户诉求", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "69802f78-1aad-4b8a-8ba2-b629e39a5635", "name": "appointmentTimeRangeForHospitalArrival", "title": "预约到院时段", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ffc09488-95cb-4ee3-b81b-822ase72cdc", "style": "Input", "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "properties": {"appointmentTimeRangeForHospitalArrival": {"$id": "69802f78-1aad-4b8a-8ba2-b629e39a5635", "type": "standard", "title": "预约到院时段"}}}}, "valid": true}, {"id": "83441248-f88d-4c47-bcb9-fd1f83724932", "name": "weiFangLocal", "title": "潍坊本地客户", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "878665a7-a654-4da4-8227-c44ae8ae9bc5", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "8cfda70a-ca7d-408e-bef9-c95be39935b3", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "96f59a84-bced-4079-9723-3ac2d5532cc5", "name": "amount", "title": "付费金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "99502af4-d5f7-4948-b9da-abdf8c883b91", "name": "serviceIntention", "title": "服务意向", "type": "java", "classType": "ServiceIntentionEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "262003322f993d4e6e8f0e6121fb60c3fe098658", "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "a18889af-398d-4ed6-b23f-da7b75768d8a", "name": "appointmentDateForHospitalArrival", "title": "预约到院日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "a3bd1db6-07dc-4b49-844d-994719dcff25", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "b3d31c6c-457b-499b-a602-e29b7411c5da", "name": "isPayCompleted", "title": "已经完成付费", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "d989e4bd-2166-494c-ae81-2aaea2b3c36a", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "ea9337ca-e722-4c1d-a0c0-4503151091e1", "name": "customer", "title": "实际被服务人", "type": "java", "classType": "object", "refEntityId": "716f8e1d-db68-4bba-bef0-b4e132b3fe51", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}, {"id": "fbe8bc32-1a75-4aa0-a1e5-50e3bdec30e1", "name": "description", "title": "病情描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "valid": true}]}