{"id": "7c0baaee-89d1-42be-99c1-d675e45d816c", "className": "com.open_care.medicalMicro.physical.OCBaseProposal", "name": "OCBaseProposal", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "结论", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0884b2e0-1466-4116-934d-8f4eb24aa6f3", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "0884b2e0-1466-4116-934d-8f4eb24aa6f3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "13637deb-14b7-4890-8f07-cc1ef141677a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "22698dec-90c9-4ef8-8b45-9ae2c931be01", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "234a86c8-9a0b-4bdc-a661-c0c6a5c07fa9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "28c21479-d28e-441c-8735-b20de976fb10", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "32369e61-1dd6-4e9f-b22a-55c81361ede3", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "56cd211d-903e-465f-ace5-940083a2b954", "name": "sex", "title": "适用性别", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["sex"], "properties": {"sex": {"$id": "56cd211d-903e-465f-ace5-940083a2b954", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "适用性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9028916f-a68a-4845-8565-0a982e4b1ee2", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "9562cf8e-bc4c-43f6-b4c7-a4fcb4a6de6b", "name": "commonProposal", "title": "通用建议", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "970a4fd8-b6a3-45ec-8f86-8db049e8e688", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "a3e1d6ae-297c-40d9-9f6b-f7c05df81fa0", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "aa76b609-7393-4640-b8e1-f7930acf4877", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "ab50f6d8-9f3a-4112-a10c-452dcf758475", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "ad1cb203-0b6f-4b80-b6b8-dd230f77c39f", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "b9963c56-63ac-4621-8220-5f56c2d06b2e", "name": "examServiceTypes", "title": "适用服务类型", "type": "java", "classType": "object", "refEntityId": "e2793e20-5280-403c-8003-28cb1bc29135", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "577fe51c-e6d7-4092-9268-1af004a4dd5e", "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["examServiceTypeList"], "properties": {"examServiceTypeList": {"$id": "b9963c56-63ac-4621-8220-5f56c2d06b2e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "适用服务类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "bec941c9-8415-416f-8d28-e1536d417909", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "c8c460a1-16d0-4844-b58e-b88f1a023482", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "cd9f8dea-17c1-42a0-9f5f-3292389e9b80", "name": "proposalTypes", "title": "建议词分类", "type": "java", "classType": "object", "refEntityId": "131e3cb9-16c5-4e41-b8cf-950504745e74", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "9e40ed6e-82d7-4d20-98d6-7792e05f1725", "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"proposalTypeList": {"$id": "cd9f8dea-17c1-42a0-9f5f-3292389e9b80", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "建议词分类", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "da4b0609-07ac-4504-b546-9381c57fb6eb", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "dbd98471-084f-4b83-8a2c-fd5f943b75dc", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "e67d6920-0bf6-4bd0-ac3a-cce514018d05", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "e7f64668-3d60-4d86-833c-7a88c583f9a2", "name": "code", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "e7f64668-3d60-4d86-833c-7a88c583f9a2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "编码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e88dd91e-f8de-485d-abc4-eac10e9df7c4", "name": "termContent", "title": "建议词详细内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "eacfbc3b-c412-431e-838d-814c7017fcca", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}, {"id": "ed27323f-34d4-4628-b117-62ced239cc75", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c0baaee-89d1-42be-99c1-d675e45d816c", "valid": true}]}