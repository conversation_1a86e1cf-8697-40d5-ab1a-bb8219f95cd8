{"id": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "className": "com.open_care.resource.OCShiftSetting", "name": "OCShiftSetting", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "班次设置", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "020a8975-7f6e-460c-b0db-e9307dae4dab", "name": "endTime", "title": "结束时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["endTime"], "properties": {"endTime": {"$id": "020a8975-7f6e-460c-b0db-e9307dae4dab", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结束时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0d1773e3-84c8-44f7-8cf2-6ccf5e8ac5bc", "name": "startTime", "title": "开始时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["startTime"], "properties": {"startTime": {"$id": "0d1773e3-84c8-44f7-8cf2-6ccf5e8ac5bc", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "开始时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0d9504f7-96d8-4100-b917-8fb143201299", "name": "alarmValue", "title": "预警值", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "237e2e41-e72e-45a1-9cb0-52449cb5669f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "27ed36bf-d719-456d-b0e2-3e22c47958a6", "name": "serviceCapacitySettings", "type": "java", "classType": "object", "refEntityId": "eb7f2b44-ce69-4991-ac4b-e34cac2b9a71", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "297bf6a0-edd7-4d77-be35-be6539fd2082", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "2cf8d928-14b8-49c2-a4da-bb6cc59ff0a4", "name": "serviceCapacity", "title": "服务人数容量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["serviceCapacity"], "properties": {"serviceCapacity": {"$id": "2cf8d928-14b8-49c2-a4da-bb6cc59ff0a4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "服务人数容量", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3728e69b-9c3b-435d-879c-43be9eeaf0a1", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "3ac397e0-b169-4408-b389-43b0175361bc", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "407112bb-8b12-4d6d-84a1-ade9c2b2205d", "name": "shiftName", "title": "班次名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["shiftName"], "properties": {"shiftName": {"$id": "407112bb-8b12-4d6d-84a1-ade9c2b2205d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "班次名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "463f0237-f79d-4fd2-8625-dc1e72d45418", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "47159ac4-a985-462b-81e2-3dd71b488640", "name": "onlyForGroupApproximation", "title": "仅用于团体约数", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "defaultValue": "false", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"onlyForGroupApproximation": {"$id": "47159ac4-a985-462b-81e2-3dd71b488640", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "仅用于团体约数", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "47159ac4-a985-462b-81e2-3dd71b488644", "name": "roughControl", "title": "粗略控制", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "defaultValue": "true", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"roughControl": {"$id": "47159ac4-a985-462b-81e2-3dd71b488644", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "粗略控制", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "55ed4541-4043-494c-a0f8-d7ee76bffb09", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "575468a7-9345-45e4-9dc9-a123aa746a20", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "57e90418-cc0d-430d-b3ac-df27fe8837ca", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "6dcec400-f97e-4007-9857-89c5c014b63f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "7166933c-3816-41a4-9fe9-ffdaa5d02559", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "84558294-d019-429a-8db9-a44174f0a03a", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "91f40369-a4f7-4e35-97fe-56679dc54b49", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "ad8b9f13-4d6d-4d2e-b008-328c7ffc1616", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "d9f742bd-91be-4897-955e-ad5e904e7197", "name": "orgId", "title": "机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ac09f8b9-88ae-4e95-839c-53f726232d9b", "style": "Input", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["orgId"], "properties": {"orgId": {"$id": "d9f742bd-91be-4897-955e-ad5e904e7197", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "dcd60912-f0c8-433c-92eb-777ce45d077b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "e4263e61-e629-4f49-92f5-7ff0f490f699", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "eb01da0f-07e1-4f2b-8991-8dac7d1ec0ca", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}, {"id": "f708197a-ee45-49a4-a4e6-cbd6fd7cfbcb", "name": "shiftType", "title": "班次类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0644518f-236e-4972-9b6f-777d562fa086", "style": "Select", "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["shiftType"], "properties": {"shiftType": {"$id": "f708197a-ee45-49a4-a4e6-cbd6fd7cfbcb", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "班次类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f8a58a43-0933-4814-9cdb-3db923c1a986", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b973fc45-bec5-402e-aab6-9c4a9df3b70a", "valid": true}]}