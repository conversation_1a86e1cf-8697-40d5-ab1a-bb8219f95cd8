{"id": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "className": "com.open_care.product.rights.OCProductInPack", "name": "OCProductInPack", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02713a61-7cbb-47a4-82dd-dd556cecfa27", "name": "surveyId", "title": "问卷ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "071eae1e-9252-4742-972e-65436d9af418", "name": "useLimitWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "0ef22670-4740-42ed-89ba-d72eac592c96", "name": "shareAble", "title": "是否可共享", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "122fa053-a804-4fb1-9fb1-622d4e88a602", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "1bbf44dd-d845-4822-9e2f-e22160d4fe89", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "1db3f449-0bb6-4c83-8bce-6e529534137e", "name": "durationCancelSubsribe", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "1fbdc20c-9d82-4938-9f2a-ea21d3f21a45", "name": "isNeedSubcribe", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "23da1874-0d05-402f-9db4-21f069102254", "name": "isLimitedServTime", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "2466f225-6b89-491a-a06c-0c0a4b27da66", "name": "cardCategory", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "2cc17279-cdc7-4101-9d3d-f064a535ac47", "name": "quota", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "2dbb25fc-1ca2-4c5f-85ec-b15886f54101", "name": "isCancelable", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "30bc440c-a81e-46fb-985a-5abeba3dc4b0", "name": "sortNo", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "3191646e-209f-424c-b2c9-ab571dc26526", "name": "waitPeriodDuration", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "327f3cce-893f-4f71-99b8-83af2e60dee8", "name": "durationBeforeSubsribe", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "3fb195e2-6428-42f7-9a91-4fa19ecbe550", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "443fc932-3399-433b-84b1-22d2962762be", "name": "displaySupplierName", "title": "是否显示供应商名称", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "45ab3abf-0c50-4420-a00e-2c804657f8b0", "name": "repeatSubcribeAtSameTime", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "45f38f92-1e97-4fe5-98ec-1755ab2aa78e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "46d89f35-fe83-4807-a0d9-617982d4c838", "name": "serviceContent", "title": "服务内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "49db0df5-ec4d-4388-a104-3e8795224414", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "55db8a64-f9cd-44ef-91f8-116985c8dfb0", "name": "department", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "5812b38e-06a5-470b-a021-a53ced25b117", "name": "imgUrl", "title": "图片地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "5ba31dcf-9f6e-4699-9f51-8e7fc936a41c", "name": "entityInstanceVersionId", "title": "版本号", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "5c0f6188-0ce8-4979-bb1a-ff481e92eaf6", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "5c73012a-e041-4359-b78a-f6e4ba04664f", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "62a8c99f-dc81-4f5d-992a-c62b8a5d4f9d", "name": "genLatestVersion", "title": "是否生成Latest版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "631453f8-bb96-4633-b7b0-ffeebd9108f0", "name": "servTimes", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "636cbb5c-f848-41b2-ac90-85fcf0b76e12", "name": "ownOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "6881bff5-807a-4751-900d-bf283edd8d44", "name": "productPack", "type": "java", "classType": "object", "refEntityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "68935af1-4bbc-4ee7-b0e4-7abe39e26e27", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "69c5e5a4-3e49-42ea-8fc7-3deacd9737fa", "name": "shallowCopyFieldNames", "title": "浅拷贝字段属性", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "6a7c5e2e-0eb9-4992-8bf8-921946a7b21d", "name": "alterSupplierIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "6ee587ec-2ec7-4b0a-9489-4f6f3dd9df81", "name": "isNeedAttach", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "6f65f943-db18-46f9-971d-37f77320210e", "name": "servedCheckduration", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "75d4c6bb-e85c-4900-af9a-e7bdcd7d5a56", "name": "ifOwnUseOnly", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "77afa1c9-0817-456d-b68f-9bd7d94a0234", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "7d69d2ca-c2a4-4315-8a9a-4014ae4e2bd6", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "7d6b45a1-e5b9-4c86-90cf-c48082bfea63", "name": "genPublishVersion", "title": "是否生成发布版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "85abd2de-8953-4f88-b6a9-e65f900abd53", "name": "ifHasWaitPeriod", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "86368220-ac60-4ef7-bfac-3107a81c021d", "name": "providers", "type": "java", "classType": "object", "refEntityId": "9eca9025-0f61-4f97-820b-5404e9a32f79", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "8cfd9d79-5786-491d-8ce9-be1e9ce31d18", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "8ef5b87f-03f2-4d60-9edc-03eeac98b0f0", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "8fb6c444-6f88-436a-ab50-b059b642604e", "name": "cutWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "90c50970-8ce5-4e80-b162-5884bed1002d", "name": "ocProduct", "type": "java", "classType": "object", "refEntityId": "83b77f63-f957-46ca-a1b8-e5d556f59fcb", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "930379d5-4d5d-47ed-986d-c26e7602e192", "name": "phoneNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "99617f8a-5509-4f01-a92b-5f807275db64", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "9a314b6d-58b2-4d86-b85b-0885f95dc66c", "name": "isNeedAudit", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "9cc80f40-c49f-4a0f-a754-ac5cb05aa7f0", "name": "validityUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "9ee557d7-fc16-4055-814a-159c5175c818", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "a1d75c61-a1f3-4814-8f59-882b35b14dd0", "name": "versionEnable", "title": "是否启用版本控制", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "a81d8ce4-5af7-425f-8439-6619f9e85f92", "name": "versionStartTimestamp", "title": "版本有效开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "a9ed3ced-8603-4f96-9073-3ba3774f492e", "name": "waitPeriodType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "aeebccfc-6b4e-4877-b226-e30cbeb114bf", "name": "mainSupplierId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "c042f938-68ae-4d82-9ce9-d41de4a4caa9", "name": "currentFlag", "title": "版本状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "c1e2487c-e253-4cf0-badc-624e8a010412", "name": "subProductInPacks", "type": "java", "classType": "object", "refEntityId": "9440d14c-9d87-4fab-9097-d0ea081ffdfa", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "dd978e44-3c98-4fc9-8dab-f4df7f584496", "name": "branch", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "e06ddfcd-b1fc-4b5b-80e3-d8ff2e7f2fdc", "name": "duration", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "e7f3d87d-dd5c-45f1-a489-284c79e648cb", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "e95c8910-4c65-419f-819f-4488ff90ba02", "name": "durationSubsribe", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "ed48c672-22c8-4afd-89b6-538abec6015d", "name": "contractId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "f812e4f6-980a-4937-be19-02f93dd5de48", "name": "cancelTimeUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "f8fedd23-930f-4466-9c51-3e0e981df5be", "name": "productSettle", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "fcf06c5e-f243-4206-a544-03b706b125e5", "name": "versionEndTimestamp", "title": "版本有效结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}, {"id": "ffc44ae7-aa26-4a84-91be-3f93a3a2282b", "name": "entityInstanceUid", "title": "版本公用实体ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "valid": true}]}