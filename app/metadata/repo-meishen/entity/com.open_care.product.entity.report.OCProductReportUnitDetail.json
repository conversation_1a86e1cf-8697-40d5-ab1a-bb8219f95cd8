{"id": "5eed8237-3b36-4613-93e1-77dbdce3c429", "className": "com.open_care.product.entity.report.OCProductReportUnitDetail", "name": "OCProductReportUnitDetail", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04e6c6e8-60da-4ba6-9db5-47550b3c20e8", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "0c26308b-0056-454d-9dc2-8946b8b8b523", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "13c60f01-1fb9-49ff-8611-ec0605a62617", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "169998ac-15e8-46d8-a308-2a60fa36a6bf", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "24c4e4d9-0b54-42b7-a4a3-82f0f3ab801f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "26c815cb-722d-40fa-a310-2e28fce6c95e", "name": "productReportTemplateFactory", "title": "报告模板", "type": "java", "classType": "object", "refEntityId": "484dc456-be29-4b00-9274-cd8b604e2b01", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a8fb1392-c5db-4f8a-80e9-a2c473de6933", "style": "Select", "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"productReportTemplateFactory": {"$id": "26c815cb-722d-40fa-a310-2e28fce6c95e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "报告模板", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "43077ab5-5547-4b0d-a9fe-f357de743847", "name": "examOrgList", "title": "适用团体", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "46b5d41f-45c0-4fcb-b11b-27568978bbdf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "53adb0e2-445a-4919-b376-24f061709379", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "69c13799-fd24-45af-a44b-c1f4d5e1c836", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "72b36f7e-00ed-4f70-aba0-bc015702b0cd", "name": "customerTypeList", "title": "适用客户类型", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "9d696eea-9d27-4abe-9bff-9f8f25195c09", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "a43ac65e-703c-4392-af9e-eedfa1087bbe", "name": "examServiceTypeList", "title": "适用场景", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "a48fce86-928b-4150-bb58-df9f60086af3", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "cab9389c-7772-4247-95a8-5da44aa05f3f", "name": "sex", "title": "适用性别", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"sex": {"$id": "cab9389c-7772-4247-95a8-5da44aa05f3f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "适用性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "cb0240d2-124e-44c5-86a7-c974ecaa839e", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}, {"id": "ee90d55b-0478-46b3-8b64-bfdb092dff2e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "5eed8237-3b36-4613-93e1-77dbdce3c429", "valid": true}]}