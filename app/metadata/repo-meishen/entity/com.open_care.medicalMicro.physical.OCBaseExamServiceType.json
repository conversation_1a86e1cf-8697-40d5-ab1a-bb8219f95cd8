{"id": "e2793e20-5280-403c-8003-28cb1bc29135", "className": "com.open_care.medicalMicro.physical.OCBaseExamServiceType", "name": "OCBaseExamServiceType", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "服务类别", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0a8abeec-7553-454f-8834-d06aee30bf55", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "0b00758e-96c1-4e1c-8860-4a894192d28a", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "0db923dc-6f52-4a1b-9a7d-9e3655e0c3c4", "name": "needReport", "title": "需要报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "0f9582d2-d74b-4963-aee6-2f32a07d3ce9", "name": "weights", "title": "权重", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "2402ffbb-8c8f-4ad8-9753-d7b4d68d6c1d", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "2bf55ec9-304a-4b82-a0a1-8b0ea5eda564", "name": "automaticallySplitHepatitisReport", "title": "自动拆分乙肝报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "3b9d20b0-658f-400a-8a86-b636d927733b", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "5e5e0135-30b5-4065-a5d4-37b2e7e2b9f0", "name": "parentExamServiceType", "title": "父级类别", "type": "java", "classType": "object", "refEntityId": "e2793e20-5280-403c-8003-28cb1bc29135", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "7a4b2209-0b10-422b-a0ee-bda1e88fb3ab", "style": "Select", "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"parentExamServiceType": {"$id": "5e5e0135-30b5-4065-a5d4-37b2e7e2b9f0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "父级类别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "63d68860-0694-4de6-90fe-0fc41b7a924a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "6e814395-b8c7-4de0-9fea-d511be756ad8", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "7a775a64-57e5-4503-a804-9855c334a47e", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "7b66dd58-eb3a-4347-9c23-6799dd23569f", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "82fe0462-f987-4194-99aa-5579e6fee2ab", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "84acd7ec-86c8-445d-9307-809531281601", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "8ec7dfb4-e46b-429b-9eff-45dc70a30778", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "97729265-7d71-4016-ae49-db31e6a698b9", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "9a3e8a21-f466-4207-96c1-2238a0b5eb0f", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "c099761e-a827-494d-9e2e-e3523a4bb106", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "cc5e1265-3b4e-40e9-a683-afe04c6d9b0c", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "dc4615ea-9246-4122-bef4-547c4704afe9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "e2988651-18ef-471d-a044-bd21a9e9c2a8", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "e749aac5-e48d-4aaa-881b-e99de3c33ab3", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "e8420d16-8994-4003-875b-f398b27b131f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "ee860996-d051-47ab-9e7f-e8a2ecc8e245", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}, {"id": "f925a970-4a8e-4892-bbb8-6826eea08971", "name": "needToQueue", "title": "需排队", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e2793e20-5280-403c-8003-28cb1bc29135", "valid": true}]}