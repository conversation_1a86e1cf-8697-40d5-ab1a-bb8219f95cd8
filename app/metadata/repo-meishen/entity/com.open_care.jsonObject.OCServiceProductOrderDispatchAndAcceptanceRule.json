{"id": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "className": "com.open_care.jsonObject.OCServiceProductOrderDispatchAndAcceptanceRule", "name": "OCServiceProductOrderDispatchAndAcceptanceRule", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0bbb829f-987e-42a1-8e09-8633dff66865", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "18fd7407-5d18-4e00-97e0-d7acd7050d66", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "1b0c897b-9ac3-4270-b4d7-5e32fb5f6d87", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "1d02b776-3b54-4c65-b45f-9e21fcc2e5b4", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "21fcf60b-c17f-450d-86a7-ef7d7d28745a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "5bf5a7bb-ba9c-4b28-a6c3-dd1a1a01c4b0", "name": "assignmentInfos", "title": "接单规则", "type": "java", "classType": "object", "refEntityId": "71e19e1e-1d78-4456-835b-607274352e2d", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "6eef7c13-f450-4e58-9ecb-048bef8a524a", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "773267eb-5384-4061-8996-c11d26596352", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "7befc0bc-55b4-4c38-a6e7-65454b24c85d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "80290676-c803-4836-8885-0eeebba8e991", "name": "supplierInfos", "title": "供应商信息", "type": "java", "classType": "object", "refEntityId": "a71b477c-db28-41b2-b259-aa60132fb688", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "9db66e38-e79c-4bf5-932b-2ff77b7d7b85", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "bd9c3469-9473-427d-97fa-6ea1781902ee", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "cae967e4-3dc3-4af6-91f7-013ce2b60720", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "e3282de0-c013-45c5-9e7c-3e215ae9020d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}, {"id": "fc6db77c-e0af-4356-a089-87bd12161d73", "name": "multiSupplier", "title": "是否支持多供应商", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "valid": true}]}