{"id": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "className": "com.open_care.medicalMicro.medical.OCBaseNumberDataTypeExamItemAttributes", "name": "OCBaseNumberDataTypeExamItemAttributes", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "数字型检查项目属性", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02e031f8-f05d-455e-9855-48a45fac6ac9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "07d5af3e-4e13-4edc-a300-d7ca24c44598", "name": "submitInfos", "title": "送检信息", "type": "java", "classType": "object", "refEntityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"submitInfos": {"$id": "07d5af3e-4e13-4edc-a300-d7ca24c44598", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "送检信息", "required": [], "entityName": "OCBaseNumberDataTypeExamItemSubmitInfo", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "08630639-6264-40b3-b85f-3944cc683d04", "name": "expression", "title": "表达式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "0a52bb54-e711-453d-8610-0b1d3a2a845b", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "0ef99e80-682a-4069-9ab5-398af086de0e", "name": "lowValueConclusionCode", "title": "低值结论词", "type": "java", "classType": "object", "refEntityId": "33adf15e-809a-42c8-bfb7-667c1c086445", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0d92d857-e3b8-4180-9067-6d4a5999490e", "style": "Select", "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"lowValueConclusionCode": {"$id": "0ef99e80-682a-4069-9ab5-398af086de0e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "低值结论词", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "614cda01-a70e-45d4-a047-a5ba2aeed738", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "6ff8ce49-01df-4cc1-96fa-abefacdd08e2", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "70c39649-a458-40a9-9e84-87a6607770ac", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "8363070d-3663-418b-8908-e42307ba1f3d", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "83e34fc9-8176-4107-8e4c-29bc75b6d6a6", "name": "highValueConclusionCode", "title": "高值结论词", "type": "java", "classType": "object", "refEntityId": "33adf15e-809a-42c8-bfb7-667c1c086445", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0d92d857-e3b8-4180-9067-6d4a5999490e", "style": "Select", "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"highValueConclusionCode": {"$id": "83e34fc9-8176-4107-8e4c-29bc75b6d6a6", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "高值结论词", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "bedc6a18-ed5c-4b58-8aa8-51745becc9b6", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "c27155cc-bb6c-42ee-8ad1-1b0de496abd5", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "cf5ee89a-27e4-4f6a-8ff5-aa6c43ce6c20", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "d8ee2eaf-0536-4264-a64e-c2e799d8e946", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "f80f7e85-957a-424f-b1e1-0f4afa003eae", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}, {"id": "feedd664-3648-4afb-8369-8e72496183bc", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "valid": true}]}