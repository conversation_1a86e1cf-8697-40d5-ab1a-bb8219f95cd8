{"id": "d35dc5af-e9e0-4bf5-90ff-4113abaf4d65", "className": "com.open_care.dto.roleModel.ExampleQuotaImportLogDTO", "name": "ExampleQuotaImportLogDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "3183da30-1560-4bfd-8d7b-e7a2d63246a4", "name": "roleModelType", "type": "java", "classType": "RoleModelTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a91838d215b7c66fd7ae929bef05af72db732c0d", "entityId": "d35dc5af-e9e0-4bf5-90ff-4113abaf4d65", "valid": true}, {"id": "8bcd8101-3cd4-4e2b-8d3e-b0e01e74d523", "name": "importTime", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d35dc5af-e9e0-4bf5-90ff-4113abaf4d65", "valid": true}, {"id": "c4657f85-8277-4be6-a2a2-d3d5b216e143", "name": "operator", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d35dc5af-e9e0-4bf5-90ff-4113abaf4d65", "valid": true}, {"id": "dde0053a-026f-4ef1-968c-5fd28f48f213", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d35dc5af-e9e0-4bf5-90ff-4113abaf4d65", "valid": true}]}