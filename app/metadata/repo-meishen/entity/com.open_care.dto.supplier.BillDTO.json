{"id": "9476008e-c239-4b27-b4b0-405862ad83a3", "className": "com.open_care.dto.supplier.BillDTO", "name": "BillDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "079f07b6-ede1-4f83-b17f-0cfe45b0c6df", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "0a18ad39-d20d-4507-a014-43b65effaff6", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "16b1aa74-5c36-4ff9-8fff-95a952bb83a2", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "16d4217d-c37b-433f-b215-b67f2ad291e0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "1d34a01c-3a6e-453c-81a3-ef35b6644691", "name": "account", "type": "java", "classType": "object", "refEntityId": "79b4895c-694b-459e-8ce4-e5f10b01b445", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "201271d1-283e-42f6-9935-86f9c747653f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "30ce0790-eac5-4ed8-8d7d-2597438a7023", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "32b8f4ba-0159-4147-8acd-6a5abf149dfa", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "348e140e-3b78-40a0-88e1-8169320a965e", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "5032172c-20d1-42b1-b5f5-e9e46f07396a", "name": "invoiceProvideTime", "title": "发票原件提供时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ea7c2dc7-a3d1-4ec0-8039-6bcd75ddf644", "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "5271ba93-df08-4a0d-a813-fe5bf902a911", "name": "contracts", "type": "java", "classType": "object", "refEntityId": "c5b069f5-0942-4a77-bbba-a7e71dfe72e2", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "594489b5-d670-4218-836a-4bb037274133", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "62484e4a-4b2a-493a-9061-145ad11a81ff", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "6f2fa4b1-de13-4054-82a7-295c559501b9", "name": "financialContact", "title": "外部财务联系人", "type": "java", "classType": "object", "refEntityId": "4f94abe1-e34f-4a2b-bd6a-3737450d8623", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "73f8843f-2220-4e38-a5ec-ce5a8e4eeb54", "name": "province", "title": "省", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "37f15354-94d1-42de-a5d5-4ba1e5022222", "style": "Input", "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["province"], "properties": {"province": {"$id": "73f8843f-2220-4e38-a5ec-ce5a8e4eeb54", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "省", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7caa00fd-a53f-45f9-be20-babd467c32bb", "name": "homeOrAbroad", "title": "境内外", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c0c5b65b-f682-4311-92df-769a55e5cb1e", "style": "Input", "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["homeOrAbroad"], "properties": {"homeOrAbroad": {"$id": "7caa00fd-a53f-45f9-be20-babd467c32bb", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "境内外", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7f39c661-1fd2-4a26-b90d-e3e1b18f7c17", "name": "claimCost", "title": "是否仅结算理赔责任内费用", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8a4000bf-42f7-47b6-a57c-cc9b9eae9f1f", "style": "Input", "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"claimCost": {"$id": "7f39c661-1fd2-4a26-b90d-e3e1b18f7c17", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否仅结算理赔责任内费用", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "80725eff-c353-4942-b820-67a6d20e7f42", "name": "updatedBy", "title": "更新人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "812a02a2-e202-46bd-a076-20377478d9f5", "name": "bill<PERSON><PERSON><PERSON>cy", "title": "结算币种", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ea7c2dc7-a3d1-4ec0-8039-6bcd75ddf633", "style": "Select", "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["bill<PERSON><PERSON><PERSON>cy"], "properties": {"billCurrency": {"$id": "812a02a2-e202-46bd-a076-20377478d9f5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结算币种", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8ffd56d5-fb35-41ac-af29-9725927d1d7b", "name": "selContracts", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "974a5836-99e0-41fc-aeca-e7596b4d0cd3", "name": "paymentNoticeTime", "title": "支付通知时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "9c9a6660-cc76-4cae-b652-1407b552096e", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "9ff3348f-dd03-421c-a6e3-6bdf414ed539", "name": "updated", "title": "更新日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "a7a1c7b2-c780-4f9b-9459-af71ec717d40", "name": "created<PERSON>y", "title": "创建人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "b0e7686a-4846-4281-8d56-8fd5e5163970", "name": "created", "title": "创建日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "b54048e5-846c-4ff3-b79d-67a8e43429ab", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "bc931c75-6e39-4db0-90e4-368920a66147", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "c19730d9-6338-4318-b708-9785376662af", "name": "financialInfoAttachments", "title": "财务信息变更声明附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"financialInfoAttachments": {"$id": "c19730d9-6338-4318-b708-9785376662af", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "财务信息变更声明附件", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c94b9b0c-8b22-4123-beb1-b97c490470ca", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "d38ab6c4-b7c3-4676-bb0d-63032fb44cd0", "name": "partyRole", "type": "java", "classType": "object", "refEntityId": "7b5e3cee-8de2-4146-836e-54662197ba5b", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "valid": true}, {"id": "dfc11ee3-7470-4594-874e-e3727ba9fa3f", "name": "city", "title": "市", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "37f15354-94d1-42de-a5d5-4ba1e5065921", "style": "Input", "entityId": "9476008e-c239-4b27-b4b0-405862ad83a3", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["city"], "properties": {"city": {"$id": "dfc11ee3-7470-4594-874e-e3727ba9fa3f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "市", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}