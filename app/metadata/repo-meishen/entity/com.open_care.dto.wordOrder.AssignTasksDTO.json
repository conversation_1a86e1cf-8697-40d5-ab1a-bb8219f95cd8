{"id": "58b6e7e2-f6d6-4781-901d-9265a143c5eb", "className": "com.open_care.dto.wordOrder.AssignTasksDTO", "name": "AssignTasksDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "26eed7de-453f-4441-8cf7-a34404101f86", "name": "newOrOldTaskRule", "title": "0: 旧任务进1,1: 新任务进1", "type": "java", "classType": "Byte", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "58b6e7e2-f6d6-4781-901d-9265a143c5eb", "valid": true}, {"id": "38a35ed2-5024-47aa-aac5-94c4e972dd71", "name": "oldTaskRatio", "title": "旧任务比例", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "58b6e7e2-f6d6-4781-901d-9265a143c5eb", "valid": true}, {"id": "95dd85cc-953b-4947-85df-14143498b24a", "name": "newTaskRatio", "title": "新任务比例", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "58b6e7e2-f6d6-4781-901d-9265a143c5eb", "valid": true}, {"id": "b3b2d58d-5724-455e-9e09-b4238e29b762", "name": "filters", "type": "java", "classType": "FieldData", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "58b6e7e2-f6d6-4781-901d-9265a143c5eb", "valid": true}, {"id": "b7167c50-348b-43d1-b169-0e2dc083f3dc", "name": "assignedTaskDTOList", "type": "java", "classType": "AssignedTaskDTO", "refEntityId": "4ee6730d-1641-4026-8756-c646ce280e49", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "58b6e7e2-f6d6-4781-901d-9265a143c5eb", "valid": true}]}