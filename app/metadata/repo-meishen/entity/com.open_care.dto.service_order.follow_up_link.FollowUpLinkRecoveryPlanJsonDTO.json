{"id": "c7e20779-ded4-46af-ada7-5b8be957c970", "className": "com.open_care.dto.service_order.follow_up_link.FollowUpLinkRecoveryPlanJsonDTO", "name": "FollowUpLinkRecoveryPlanJsonDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "09122451-a829-4515-bb6a-22050095a4b4", "name": "expertFollowUpRecords", "title": "专家随访记录", "type": "java", "classType": "object", "refEntityId": "c081ef1e-d3bb-4739-a053-dfafd775fdee", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "3b03d241-0fea-4c70-b5b9-29369e7662d9", "name": "planningDate", "title": "规划日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "47345490-01b4-40f7-87ae-0c38cd6f9520", "name": "needCommit", "title": "是否提交", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "62bc3409-df66-4efc-aa5b-7acbe2260ef0", "name": "recoveryPlanType", "title": "康复方案类型", "type": "java", "classType": "RecoveryPlanTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "9209f4acb1c1c8a50246e47074995819a76a3bb2", "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "776a8228-e1bd-423a-a2aa-4401f8aa1953", "name": "planSendRemark", "title": "康复方案发送备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "8913b954-04e6-401b-af4e-72077785fa88", "name": "planningRemark", "title": "康复方案规划备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "a253acf0-1a35-471d-958d-bc88dc1d280e", "name": "isStartAnnualSummary", "title": "是否直接开启年度汇总", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "a40f59df-871f-4043-9cbb-0af3da7dc3f2", "name": "arrange", "title": "专家随访安排", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "b5c5754b-d8ef-4fc4-a821-8e987a828ce4", "name": "planSendDate", "title": "规划发送日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}, {"id": "fe0fca7d-f64d-4f29-8983-8fa67265d1f7", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c7e20779-ded4-46af-ada7-5b8be957c970", "valid": true}]}