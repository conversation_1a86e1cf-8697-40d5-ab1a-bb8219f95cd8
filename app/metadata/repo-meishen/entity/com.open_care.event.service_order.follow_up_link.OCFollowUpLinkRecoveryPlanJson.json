{"id": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "className": "com.open_care.event.service_order.follow_up_link.OCFollowUpLinkRecoveryPlanJson", "name": "OCFollowUpLinkRecoveryPlanJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "32572173-244c-49b3-8737-466270626225", "name": "planningDate", "title": "规划日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "valid": true}, {"id": "7540f41a-c522-4a08-b6f8-1618998549cc", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "valid": true}, {"id": "812bb562-2d93-4edc-8383-f63ea302d435", "name": "planningRemark", "title": "康复方案规划备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "valid": true}, {"id": "8d9c56cb-942f-433a-9c74-3cf6e6d82a08", "name": "arrange", "title": "专家随访安排", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "valid": true}, {"id": "c37638e1-a9fe-4270-a12a-82d62e2a7661", "name": "planSendRemark", "title": "康复方案发送备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "valid": true}, {"id": "d1c0e3c2-8639-47d8-82a5-4e24c501aae8", "name": "planSendDate", "title": "规划发送日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "valid": true}, {"id": "e8e7e2e6-90f6-420a-8110-33c2bbedd0a1", "name": "expertFollowUpRecords", "title": "专家随访记录", "type": "java", "classType": "object", "refEntityId": "147609df-5271-405e-85d4-64851122afe4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "valid": true}]}