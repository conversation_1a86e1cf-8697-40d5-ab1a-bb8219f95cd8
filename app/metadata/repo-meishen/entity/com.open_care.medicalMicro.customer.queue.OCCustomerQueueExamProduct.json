{"id": "04339745-cc6e-41b7-985c-cb426c53da69", "className": "com.open_care.medicalMicro.customer.queue.OCCustomerQueueExamProduct", "name": "OCCustomerQueueExamProduct", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "00e09432-8acd-41b1-95c3-b2f13ce4ab83", "name": "customerExamProductOcIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "03de85f1-2e7c-487f-83be-eb3edc243210", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "0b525202-1dbf-4b76-af30-070caee38e88", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "140adc40-bc1a-4218-80ba-3da95f3a5853", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "15d19ebe-c5f3-49e7-a3f7-aee399ff9197", "name": "departmentId", "title": "科室信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "26498a43-3aee-4cfc-b719-a584d2c247bb", "name": "skip<PERSON><PERSON>nt", "title": "跳号次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "3cf324ce-b6d8-49f4-bed8-a8335383d4aa", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "4687b1c4-1f27-4e99-a34c-8eba7f4db5ea", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "46f53fac-5ac2-4968-a558-ec21965e837b", "name": "startServiceTime", "title": "开始检查时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "4c8a58d9-0511-44f5-a280-e11a28dce85c", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "4e33e963-3bc9-4139-a863-20284b48d1ce", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "5390bcf0-99e3-4023-b65b-149ef2eee6f7", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "53e65a98-6808-4fce-91b8-6dd91f503f96", "name": "serviceProductIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "56ac1f5a-96f0-43a5-b1dc-d049a0a757c2", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "58b6786a-0ab7-4e53-a03a-58d1ba79eb02", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "5b3c096b-6d89-4072-b3d0-27de1f091e46", "name": "completeReason", "title": "完成原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "609b731d-e205-4b3d-ae42-34aa798d1b36", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "610b57d2-1b79-4ae8-ae73-87b43842da2b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "61669ccd-ab78-4444-93ca-41b88b4efc2d", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "6399bc08-df09-4937-876d-b23188502ec6", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "6553b121-75f1-4dc1-aa5c-5b4e47840bff", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "6d58cc68-bba3-4fd0-924e-beaf0e78d437", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "717341c8-34ba-49b6-aa48-fa6ab357eceb", "name": "completed", "title": "已完成排队", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "73dcd4dd-d057-49a9-87cd-d842ed2a4dbe", "name": "customerName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "748e763c-31c6-4e56-9116-c9faa7acfc7d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "785a401d-cf9c-446b-8f02-cdefe76bb5ee", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "7c80d56e-0f7a-4053-bfa2-e1efaaa87c9c", "name": "customerQueueRoomGroupInfo", "type": "java", "classType": "object", "refEntityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "92eea5dc-07ba-431c-a189-3ed1b606235e", "name": "seqno", "title": "次序", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "99eeca14-1b68-4963-b5c7-e868e668dc9f", "name": "occupied", "title": "占位队列", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "9abcb46d-2c93-40ea-82a0-4258fb647307", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "a10643c1-a702-491d-b46f-6514e30568f8", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "a29af316-e0d8-4f09-876e-ac54846cf479", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "ac9efbd8-9e23-4561-8d58-51f40a4698b2", "name": "queueRoom", "title": "排队诊室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "ae816cdf-015a-4df6-aece-5e99e7d1544b", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "b3b6dffc-1c25-43b1-8960-cbfeed932f03", "name": "previousQueueTaskId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "baf53996-f294-46ed-b17c-ea204f481bb9", "name": "waitingTime", "title": "等待时间", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "ccf017ef-55d0-4fcf-911e-ef5a9dcd876a", "name": "joinQueueTime", "title": "加入队列时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "cefa0b31-d0d9-4437-a6af-213b606c2ae2", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "d232027c-1460-489f-8e17-5cf4d322f4f7", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "d58e6476-6c79-4f7f-a17e-80229822488d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "e1e6c75e-6a7e-427b-8f74-b6ddec8c10c4", "name": "completeQueueTime", "title": "完成排队时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}, {"id": "fbb44c8e-621e-4a53-8a4b-fb5ce909190c", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "04339745-cc6e-41b7-985c-cb426c53da69", "valid": true}]}