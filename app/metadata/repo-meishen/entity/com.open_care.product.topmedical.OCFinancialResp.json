{"id": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "className": "com.open_care.product.topmedical.OCFinancialResp", "name": "OCFinancialResp", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "034b741f-1149-4bd5-9231-a1f0cb67ec59", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "03720747-453b-4274-a88f-bbedd4492a8d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "0cdaef05-e713-451c-a48b-fd8734a5bcf4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "1551a9cb-3c04-4e52-9e89-1cefb76b10f0", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "20af8ae6-54cd-4d80-9566-524aacc82e30", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "36d3cb87-ac31-4201-8a9d-da16f804ca3b", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "3784e70d-58d7-4084-91b5-d7256fdfd119", "name": "ocFinancialOffer", "type": "java", "classType": "object", "refEntityId": "531e420b-5f9d-4298-992e-37b8268a290e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "4e9ae4cd-0bc1-4442-b096-e22e71f2c165", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "5079e73c-b0f4-4634-ae9e-6d76b105c192", "name": "success", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "7df8895d-1ee1-4103-932a-eb7d73d69ba6", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "8608ced3-0f19-419e-8f96-f86c9b74386f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "922f1a44-feb7-4f6d-a869-3b2831f1d1ac", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "a023fedb-63cb-451e-8089-677ed87f94ec", "name": "message", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "b0e60592-1f3d-4a06-9e61-f33471af83bb", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "b59cb29c-5d1d-4738-b51b-3816dcadcbb2", "name": "data", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "b6949626-9341-48d0-b89e-be89e6550984", "name": "code", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "ba3ded0a-f714-4ef0-9ada-d23b37aaacc5", "name": "timestamp", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}, {"id": "d160a347-a73d-49a2-baaa-95379faf4694", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "377d8d1f-d6b2-4a24-9bde-be9d931d3ea3", "valid": true}]}