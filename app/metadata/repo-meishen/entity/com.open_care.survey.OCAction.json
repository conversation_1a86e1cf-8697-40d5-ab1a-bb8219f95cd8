{"id": "3d475b33-31a2-45e0-bf84-36db531f37b6", "className": "com.open_care.survey.OCAction", "name": "OCAction", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "4c5edbab-da05-492d-a042-d8cd8f3421dc", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3d475b33-31a2-45e0-bf84-36db531f37b6", "valid": true}, {"id": "5990784c-62cc-405d-a569-c0e1529a991c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3d475b33-31a2-45e0-bf84-36db531f37b6", "valid": true}, {"id": "64bd17d1-337d-45b7-93af-c4c13e685781", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3d475b33-31a2-45e0-bf84-36db531f37b6", "valid": true}, {"id": "f6219df0-cf3c-492c-bb47-b76cc319ed44", "name": "target_id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3d475b33-31a2-45e0-bf84-36db531f37b6", "valid": true}]}