{"id": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "className": "com.open_care.dto.service_order.precision_medical.PrecisionMedicalServiceRecordDTO", "name": "PrecisionMedicalServiceRecordDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "03aca836-6fb0-4710-85ac-0b2f0099a739", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "11b5f828-a6d5-4133-8e74-8e4c37b7b127", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "2563f201-be03-4213-8561-7ad7bc69cfe8", "name": "customer", "title": "实际使用人信息", "type": "java", "classType": "object", "refEntityId": "716f8e1d-db68-4bba-bef0-b4e132b3fe51", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "2bf57c7a-a08b-42a8-afb6-aab77d1f1120", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "3074741f-ce31-46db-b3a0-f2340f9a0bb0", "name": "description", "title": "病情描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "34a1cd35-43f3-4197-81ca-b7044803b193", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "705bff75-bce0-4635-8797-09bb9ad2e22a", "name": "actualCheckService", "title": "实际检测项", "type": "java", "classType": "ServiceIntentionEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "262003322f993d4e6e8f0e6121fb60c3fe098658", "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "9a686732-105c-4d28-bfed-61f044d3d8ea", "name": "currentDateFollowRecord", "title": "当日回访记录", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "cbc824a1-95df-47d7-86c8-1e920ef3b153", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "e8b750ec-8d9c-45dc-ba37-d729c8e8155d", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "eb149332-ca1e-40f5-acfb-c039c1db93e8", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "ebbe20c3-d172-41cd-bfaa-feb159780968", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "ede0f6cf-fe89-418d-a576-43b30503b660", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}, {"id": "f0473260-b725-4402-8d07-82a150b3100f", "name": "actualCheckDate", "title": "实际检测日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "valid": true}]}