{"id": "ef1ca3f9-667e-4a2d-8300-1475b017a434", "className": "com.open_care.dto.statistics.AppointmentProjectDTO", "name": "AppointmentProjectDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1561a76b-6c81-49b2-b58a-9020743431de", "name": "maleCount", "title": "男性数量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ef1ca3f9-667e-4a2d-8300-1475b017a434", "valid": true}, {"id": "2122f6ae-8005-473d-88f4-ddac3559726d", "name": "allCount", "title": "总数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ef1ca3f9-667e-4a2d-8300-1475b017a434", "valid": true}, {"id": "ea88149a-10df-46b8-8616-ecbd7799759a", "name": "femaleCount", "title": "女性数量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ef1ca3f9-667e-4a2d-8300-1475b017a434", "valid": true}]}