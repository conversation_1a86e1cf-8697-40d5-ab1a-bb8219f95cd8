{"id": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "className": "com.open_care.process.TaskHandleResultJson", "name": "TaskHandleResultJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "3624d468-02ac-445e-825c-ca65bc5d608f", "name": "taskName", "title": "任务名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "36d43978-f8d9-41e6-88b6-aa17bdff3e4c", "name": "operatorName", "title": "处理人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "392f3bcc-efda-4300-8171-19a3bdb7f4a8", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "637605fa-957d-481e-afd5-5f088161c54f", "name": "taskId", "title": "任务id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "7a937e91-00cb-4f49-b721-b9e61444d278", "name": "operatorRole", "title": "处理角色", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "81696fc4-7a15-47a9-83be-558f02f4c041", "name": "auditResult", "title": "处理结果字符串 true false", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "9a68b80e-349b-49b1-9201-9ee16869b2a2", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "b1950d50-cbb3-47bb-96ad-29958f783f37", "name": "operator", "title": "处理人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "b46445c6-c0ce-4139-9362-0cbf1146a620", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "b93502bf-45a2-48cd-941e-7e4799f696c1", "name": "handleStatus", "title": "工单处理状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "bb74a4ac-e6f2-48d4-96aa-0fb3e2524f73", "name": "operatorTime", "title": "处理时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "e4e1a009-4f52-4c34-8172-373db82ba065", "name": "operatorUserNo", "title": "处理人工号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}, {"id": "fd1f73a2-7eb6-4a61-8a2e-5fd95348b556", "name": "attachments", "title": "附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "valid": true}]}