{"id": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "className": "com.open_care.medicalMicro.medical.OCBaseExamPart", "name": "OCBaseExamPart", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "检查部位", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "04678468-16be-4ac1-a98f-60b500f75815", "name": "itemTypes", "title": "所属项目类型", "type": "java", "classType": "object", "refEntityId": "b7d944c9-182b-4017-be00-e4e594e3ff69", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "d92525da-1fbf-405a-a796-a67bec60b9da", "style": "Select", "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["itemTypeList"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"itemTypes": {"$id": "04678468-16be-4ac1-a98f-60b500f75815", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "所属项目类型", "required": [], "entityName": "OCBaseItemType", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}, "itemTypeList": {"$id": "04678468-16be-4ac1-a98f-60b500f75815", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "所属项目类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0c1bdc71-46e1-4927-81e3-8e009e8429bc", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "0d7cb47c-323f-46dc-8f8b-eb3001889756", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "1627f917-a2c3-4985-8483-960184f90ff8", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "1aaea36a-197a-49e8-8811-9c8b829fc8a3", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "2044bf0b-4320-4441-ae51-5ba55bd07980", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "3794ac15-793c-49b5-b737-36b8ecf3958e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "44aa935d-0082-46cf-b276-fd912193940f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "49dbcc60-400e-4ae3-b1a4-b00843d9ec7c", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "49dbcc60-400e-4ae3-b1a4-b00843d9ec7c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "51280b89-2996-4ac5-a8a0-a8bfa8a8425e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "61768eae-c2c6-471e-9edd-6d47d2695733", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "728c20bb-3a4e-4281-b8a4-7baaab8042bd", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "7bcb8ea3-e6eb-4fcf-b97f-c3cebaebe534", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "8a62335f-1c23-4e24-ae0b-3783f2d0efd3", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "a2fab9f4-df05-4aee-948c-0c9b5584732c", "name": "sex", "title": "适用性别", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"sex": {"$id": "a2fab9f4-df05-4aee-948c-0c9b5584732c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "适用性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a587a336-559a-4a71-9e06-23b1b67808c3", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "b3e8ae45-2a88-4ac5-a178-06cca8592a3b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "d4e9b412-09be-4007-a85a-b9f0dfbc9956", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "dff1a83b-6450-4e6a-9df3-3b07486ee3aa", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "dff1a83b-6450-4e6a-9df3-3b07486ee3aa", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e823e2c9-f3d9-431a-b187-ff7890a4e514", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "e9c06aaa-4cc1-407f-b809-5314e18527a2", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}, {"id": "fd7800b5-159c-4485-83b7-f1504d231e3c", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b65c966f-272d-4d50-9d4c-df31c2f56bae", "valid": true}]}