{"id": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "className": "com.open_care.product.topmedical.OCInvestigateAdvice", "name": "OCInvestigateAdvice", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "08a58f27-f56c-4fb5-9f8c-febcd9d72711", "name": "purposesManualEdit", "title": "调查目的手工编辑", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "16061df1-92b5-44e2-ba72-0004745db0d7", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "264436c8-5ed4-4976-b4ef-1d9da4e53b1f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "3cde5fd2-a24d-4d2b-b903-ebc863e21b0c", "name": "purposes", "title": "调查目的", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "59c4f0ac-3cc0-46e8-8259-ef9e3db99872", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "5b93a50e-ad3f-45db-93a8-14373b2d7aa6", "name": "centralSubBranch", "title": "受理中支", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "635c9e19-7225-45cc-9f77-76b849c1bf21", "name": "issueType", "title": "发起调查模式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "66db0e15-1fe9-40d3-99c7-5651fc8c9c98", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "7b4045e9-43d4-46bd-adfe-2cd785224f7f", "name": "investigation", "type": "java", "classType": "object", "refEntityId": "6cd8fe9d-c6ca-4132-8bf3-c9ca879bd7c9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "8f7adfc6-71ac-4a20-8d71-34c0747324e4", "name": "templates", "title": "可选择模板", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "a08572e1-2fb3-4272-8241-c203cf1d0fcd", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "a90e7aa1-ceaf-45e0-9a34-51cc1b9c9809", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "a9e80491-ad80-42dc-8326-4937e73161d9", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "ad22fbf8-4421-4048-967a-b0290bcad519", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "b62a8c2b-710b-4350-8c85-68d40b366a96", "name": "branchOffice", "title": "受理分公司", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "c222b450-d21d-4dd3-aa3a-aa589d76b02d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "c9e6759c-5334-471a-978f-6a12e0dc7e0f", "name": "methodContents", "title": "手段内容及地点", "type": "java", "classType": "object", "refEntityId": "8142d390-3f94-4cfa-a4a7-7a9787687123", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "d6aea21b-7cbc-44b8-9801-1c2ed6807fa5", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "dc54052a-ac08-4532-b572-a4cccec857b3", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}, {"id": "ee9ccbf1-9c0e-4565-8d66-60647083e825", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0de9e1d-93b4-4a11-941b-c4c0face857f", "valid": true}]}