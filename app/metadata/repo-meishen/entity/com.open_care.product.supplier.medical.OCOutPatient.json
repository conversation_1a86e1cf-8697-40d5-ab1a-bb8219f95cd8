{"id": "dd8163c0-548d-400a-be09-4a027b8d51ca", "className": "com.open_care.product.supplier.medical.OCOutPatient", "name": "OCOutPatient", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00f5fc93-5813-4f6a-9045-32bf580d2fe7", "name": "workHours", "type": "java", "classType": "object", "refEntityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "06db1979-96c3-48c5-baaa-4580c47530ab", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "106458ca-71d2-4d2b-b3bb-79768a7f1acf", "name": "receptionDesk", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "19f5f87e-25b7-4d28-82af-1439e29dd729", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "1e283893-04b9-4370-951f-bdf8bb2127e5", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "244def64-a38f-48b3-a51e-2760fd4afea1", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "2887899e-8e96-488a-be87-addb7ea67598", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "36d366da-ebf2-4e12-b26b-c29eeeb7749f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "4bcdb8d0-d0df-4a9c-a51f-8ce850952e5e", "name": "advanceBookingTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "51defc3c-1cca-44ba-a74d-c70b46f10615", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "59b6156c-0ec1-46a6-a7d3-d78fe2da274a", "name": "medicalRecordProvision", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "5ee0b266-90b3-4bc8-a83b-7b6f874aa212", "name": "address", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "68e2e1bc-e5c4-44a8-aac6-a7b3034001ca", "name": "medicalRecordTransferLocation", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "72883ebe-c706-46e5-8c54-c91e5a452144", "name": "medicalAdvice", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "7d883f00-eb65-4207-821c-080bca9aa5ec", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "86222923-3754-44b3-9b86-8140560f6539", "name": "outpatientAppointment", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "c7e7c024-51fe-48d4-b279-f223a64edd96", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "cfca6d05-67cd-49c8-b114-1cc601e52f21", "name": "appointmentTips", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "d933c021-4528-4bc3-ba3f-af6278e91835", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "ea938e2b-a89b-446c-b14e-95ead569550f", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "eaa11faa-e10e-4100-bcb9-25324c9d98bb", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}, {"id": "feff1f8e-ebe4-4ee8-acb0-c8963627db40", "name": "appointmentPhone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd8163c0-548d-400a-be09-4a027b8d51ca", "valid": true}]}