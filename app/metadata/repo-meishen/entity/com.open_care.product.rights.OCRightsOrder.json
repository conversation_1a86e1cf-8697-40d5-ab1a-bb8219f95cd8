{"id": "ebea57bf-9a78-4efd-a458-70c06859aef6", "className": "com.open_care.product.rights.OCRightsOrder", "name": "OCRightsOrder", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "000ffd31-5e46-49b3-96ac-7ed5959fd13c", "name": "payInfo", "type": "java", "classType": "object", "refEntityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "0bac3443-8dfb-4b6e-8472-e2d5c2337cef", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "12cf7fc1-8a0e-4da4-917f-390f21d677db", "name": "note", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "146b1b9f-64cd-4583-93a5-55c05002f45f", "name": "righsFromInfo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "1ae97538-0903-487f-a02a-6470405be323", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "1b1349be-10fa-4dec-8be9-22049d0f7eaa", "name": "category", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "1d89461d-322c-41ea-8c82-f3030d820aa0", "name": "project", "type": "java", "classType": "object", "refEntityId": "562237b9-2940-4f99-9f77-eef50035cf02", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "2658a0eb-fc91-4167-a19e-e52ec7033bc9", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "283ae41f-de40-40fb-b947-49f373b9ef57", "name": "ifMaster", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "50802f3c-f7ec-4609-a028-0918e024adde", "name": "orgClient", "type": "java", "classType": "object", "refEntityId": "6b3bca44-b285-42a6-a8f2-e8670e722b7a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "50f93bc1-32e8-4670-ada4-3de52d8a18c6", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "53ad5078-30c6-446a-9df2-827baeb8b357", "name": "submissionTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "5f848066-e3ad-40e7-a555-cebd9fed114e", "name": "effectTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "6a225515-b81f-4029-be6b-23f6b4fafdd4", "name": "orderFee", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "737d3634-103a-4bca-9efc-b6fb5164ab46", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "80a00e81-9861-4b69-801f-666140477ce9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "8442e46c-2afe-45b4-ac5e-89a4c1d2b6ee", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "a9ef8a08-68ee-477a-ba35-800cde823350", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "c3205d54-c8ce-49c1-8c89-61a31d7e8716", "name": "orderNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "c39b246f-ec42-458f-b04f-e6d49f0706a9", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "c8793d78-18d0-4e49-894e-92374b690aa6", "name": "isOffLineSignUp", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "d0263e10-7563-46f0-b755-6392883cd4aa", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "d09f8c4c-242e-48f2-a1d3-e3d839dc4547", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "d32a1e86-ea91-419f-9124-df751ec10b9a", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "dcf821e3-2a30-4afc-b71d-2ed353ee7d5c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "dfb8ece7-268d-4808-9b48-f01af7053620", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "e6104f17-fd18-4397-9f1f-c2f958afee62", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}, {"id": "fd993e39-40a1-426c-ab16-cf908368e45f", "name": "contracts", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ebea57bf-9a78-4efd-a458-70c06859aef6", "valid": true}]}