{"id": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "className": "com.open_care.product.topmedical.OCTopMedicalSettle", "name": "OCTopMedicalSettle", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "123f143a-4974-446a-9d81-6c0a7a93debd", "name": "outPatientBusinessTimeAM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "157e0fee-997c-44db-a4ea-c52475cd2ba2", "name": "depositBackWay", "title": "住院押金退还方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["depositBackWay"], "properties": {"depositBackWay": {"$id": "157e0fee-997c-44db-a4ea-c52475cd2ba2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "住院押金退还方式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "16efa4ce-60f7-44e8-be14-560d45f16be1", "name": "inPatientStatus", "title": "住院周末营业时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a51cee4f-13f6-4708-8cb3-c568cfb8d83b", "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"inPatientStatus": {"$id": "16efa4ce-60f7-44e8-be14-560d45f16be1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "住院周末营业时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "17932066-2782-4ce1-848e-4706b412e166", "name": "directPayProcedures", "title": "直付服务手续", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "63946c89-2475-49dc-b1f9-ffbbb1cdcb1d", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["directPayProcedures"], "properties": {"directPayProcedures": {"$id": "17932066-2782-4ce1-848e-4706b412e166", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "直付服务手续", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "1806af6d-7688-4722-9cdd-dd93f9f7fdb2", "name": "subscribePhone", "title": "预约电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["subscribePhone"], "properties": {"subscribePhone": {"$id": "1806af6d-7688-4722-9cdd-dd93f9f7fdb2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "预约电话", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "220bb77a-5b21-4536-8678-3bac6b24f8a2", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "223dcb2c-8eec-4add-b294-df98e0ec07f8", "name": "ifProvoideMedicalRecs", "title": "门诊病历是否提供", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifProvoideMedicalRecs"], "properties": {"ifProvoideMedicalRecs": {"$id": "223dcb2c-8eec-4add-b294-df98e0ec07f8", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门诊病历是否提供", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2300e9ab-d470-4a01-b1c3-48c44d5eb90e", "name": "emailForGarantee", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "2a01d15d-4d8c-47cf-8b75-af356f5df7c1", "name": "preOutpatientDays", "title": "门诊预约提前时间", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"preOutpatientDays": {"$id": "2a01d15d-4d8c-47cf-8b75-af356f5df7c1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门诊预约提前时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2ac9bc32-14a7-444c-aefd-f5b9a8d3420a", "name": "ifHaveStag", "title": "是否有驻点", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifHaveStag"], "properties": {"ifHaveStag": {"$id": "2ac9bc32-14a7-444c-aefd-f5b9a8d3420a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否有驻点", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2cc8cc5b-50e2-4d35-9a30-df13a9e52725", "name": "supportProducts", "title": "支持保险类产品", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "e06ddd2f-c75f-4a36-8b57-bd6ec6a1f123", "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["supportProducts"], "properties": {"supportProducts": {"$id": "2cc8cc5b-50e2-4d35-9a30-df13a9e52725", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支持保险类产品", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2f3dead6-22cc-48fc-a78a-d13a4667f4d3", "name": "discountExcludes", "title": "除外情况", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "41cfadf8-5a37-49ce-9c2f-8f867bf1981f", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"discountExcludes": {"$id": "2f3dead6-22cc-48fc-a78a-d13a4667f4d3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "除外情况", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "34fbfd5c-7119-4b07-af13-069ad7b38f91", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "37afa8b4-061a-40df-a8f6-4be752a8b2b4", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "3ba8c24f-c003-4bbf-89f9-79b489297437", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "3c6aa51c-e040-4d49-8899-2dfffa824981", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "3f6bc3dd-9936-47ca-8694-6815ade3a673", "name": "subsribePhoneVisiblers", "title": "预约电话可见对象", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "41b3ca1e-21cb-47fa-af19-692b830f1817", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["subsribePhoneVisiblers"], "properties": {"subsribePhoneVisiblers": {"$id": "3f6bc3dd-9936-47ca-8694-6815ade3a673", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "预约电话可见对象", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "408a3bba-2704-4582-bd23-a9ce82abf947", "name": "ifCanPayByGov", "title": "是否支持医保支付", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifCanPayByGov"], "properties": {"ifCanPayByGov": {"$id": "408a3bba-2704-4582-bd23-a9ce82abf947", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否支持医保支付", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "41fb8924-1094-4fb7-9106-ea886e400bc5", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "4293844d-0e32-4e91-a80b-5c41bdda20a6", "name": "hospitalEmail", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "46e74d64-7a5b-4455-b0b9-63ab138b0275", "name": "inPatientWeekendTimeAM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "479cfe76-23ca-4106-bb81-5ed6bf0302b6", "name": "hospitalPublicEmail", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "49af4843-a131-4fe1-8efe-2eb0a92ceab7", "name": "netContactor", "title": "网络服务联系人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["netContactor"], "properties": {"netContactor": {"$id": "49af4843-a131-4fe1-8efe-2eb0a92ceab7", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "网络服务联系人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4d3c629a-1eaa-43c4-afef-1bd213c886a7", "name": "stager<PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "4e8b3760-2d22-4e95-a4d3-c9d47f4c3814", "name": "outPatientWeekendTimeAM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "547e65f3-835e-467d-967e-42bb394369aa", "name": "hospitalizeDiscount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "5af838c5-638f-422c-a265-9bb6804c85a6", "name": "inPatientLegalTimeAM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "5b4eae2d-f449-41e0-8180-439b141f66b5", "name": "whetherSameAsOutPatient", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "6a530174-9856-49d3-8d60-135c69fa0ca6", "name": "netEmail", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "6ae7dbad-869d-4b04-a05f-1156164d1fc2", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "72664f5a-4de1-4c50-9055-486d17f70c5c", "name": "note", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "7577654b-08f5-46fd-8377-278abc7fd248", "name": "inPatientBusinessDay", "title": "住院工作日", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "158c4f4c-28f7-4a70-8bc8-90b203326412", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"inPatientBusinessDay": {"$id": "7577654b-08f5-46fd-8377-278abc7fd248", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "住院工作日", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "75cbea6e-1d94-4097-aa18-d66839fac0bf", "name": "ifCanHospitalize", "title": "是否支持住院", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifCanHospitalize"], "properties": {"ifCanHospitalize": {"$id": "75cbea6e-1d94-4097-aa18-d66839fac0bf", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否支持住院", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "79117307-dbf2-42b4-b04a-ac204852e0f6", "name": "otherDepartments", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "796504b8-c803-4e98-8bb8-3f5a1eba4ff4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "7aaf950d-5628-47b7-8d4b-11680939ae51", "name": "outPatientBusinessDay", "title": "门诊工作日", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "158c4f4c-28f7-4a70-8bc8-90b203326412", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"outPatientBusinessDay": {"$id": "7aaf950d-5628-47b7-8d4b-11680939ae51", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门诊工作日", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "80e3b2c8-eab3-4dc9-8d7f-bf38902f06e7", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "8147ea1c-a277-43cc-8ef2-ba90d64d697f", "name": "otherDiscount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "82b47640-874f-4933-84db-62c9f800e297", "name": "outPatient", "type": "java", "classType": "object", "refEntityId": "8e54de6f-a160-4eda-926b-4c9e19d085b9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "846a3d25-ad18-4452-83ba-7a9170f8fa60", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "85481eb2-e054-41eb-a41c-bfcf4f07abb5", "name": "departments", "title": "科室信息", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "b906c2b5-461d-44a7-8d53-624d9bc8143a", "style": "Select", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["departments"], "properties": {"departments": {"$id": "85481eb2-e054-41eb-a41c-bfcf4f07abb5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "科室信息", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8677933a-5baf-421a-a02f-aaafdaa443aa", "name": "coStatus", "title": "服务合作状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "fd46d7ac-33a0-4c39-95cf-65965e16b746", "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["coStatus"], "properties": {"coStatus": {"$id": "8677933a-5baf-421a-a02f-aaafdaa443aa", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "服务合作状态", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8abcb072-f789-4127-8a3e-ad2f1e6a467a", "name": "drugFee", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "8afdedbf-b0e9-429b-9b8b-cf7e6dd5480a", "name": "checkFeeItems", "title": "检查项目", "type": "java", "classType": "object", "refEntityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "256b75b0-3f72-463a-b5d9-91932e26ce54", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"checkFeeItems": {"$id": "8afdedbf-b0e9-429b-9b8b-cf7e6dd5480a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "检查项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8b9e7eeb-de5e-49bf-bf82-c176b44ff5d1", "name": "ifPayByAdvance", "title": "是否预付款支付", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifPayByAdvance"], "properties": {"ifPayByAdvance": {"$id": "8b9e7eeb-de5e-49bf-bf82-c176b44ff5d1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否预付款支付", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "90dd66e1-8094-4b38-b455-b02b83e84f52", "name": "emgDaytimeStart", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "914fc150-7ab5-495c-8f22-13f34789d62b", "name": "emgDaytimeEnd", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "969b8833-ad93-423f-962a-0e688b65edb8", "name": "ifHaveEmergency", "title": "是否有急诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifHaveEmergency"], "properties": {"ifHaveEmergency": {"$id": "969b8833-ad93-423f-962a-0e688b65edb8", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否有急诊", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9a2b80e9-5dc4-4557-afb5-2f79373ccd34", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "副主任医师", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "properties": {"chiefRegFee": {"$id": "9a2b80e9-5dc4-4557-afb5-2f79373ccd34", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "副主任医师", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a408f6ed-b574-4f8b-b20f-8f924596d87f", "name": "inPatientWeekendTimePM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "a49fed9c-fdb1-4487-b42d-40c26f0ddd76", "name": "betterDepartments", "title": "优势科室", "type": "java", "classType": "object", "refEntityId": "52700569-49b2-4f3a-9f1d-73698d3cdcc8", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "37469f94-7622-40cd-a38d-fef6f98c128f", "style": "Select", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"betterDepartments": {"$id": "a49fed9c-fdb1-4487-b42d-40c26f0ddd76", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "优势科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a5260524-e2be-44d8-b34f-40b9e1353027", "name": "preAuthorizeNote", "title": "预授权备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["preAuthorizeNote"], "properties": {"preAuthorizeNote": {"$id": "a5260524-e2be-44d8-b34f-40b9e1353027", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "预授权备注", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a8a15f60-c183-4c92-b795-24778eeba871", "name": "cooperator", "title": "合作实体", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "819ea169-be66-49d7-9da5-56f552863c78", "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["cooperator"], "properties": {"cooperator": {"$id": "a8a15f60-c183-4c92-b795-24778eeba871", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "合作实体", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "abc5ef33-91e7-47a7-a352-72b380684337", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "ad0e93df-2cde-48b9-bdfb-befe40abf61e", "name": "inPatientLegalHoliday", "title": "住院法定假日营业日期", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"inPatientLegalHoliday": {"$id": "ad0e93df-2cde-48b9-bdfb-befe40abf61e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "住院法定假日营业日期", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "adbfeea6-65de-4f89-aa77-8f241b87a623", "name": "consultPhone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "b26de740-0aee-48b6-ac99-72731e967f3a", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "bd594981-f63f-40ca-8090-26c053185826", "name": "outPatientWeekendTimePM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "bf07aefd-8ddd-42ee-b90d-e6ef00ceab61", "name": "outPatientLegalHoliday", "title": "门诊法定假日营业日期", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"outPatientLegalHoliday": {"$id": "bf07aefd-8ddd-42ee-b90d-e6ef00ceab61", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门诊法定假日营业日期", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c1a33aa5-fb1b-4c3b-9951-d37a1d9a2d42", "name": "hospitalContactor", "title": "医院服务联系人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["hospitalContactor"], "properties": {"hospitalContactor": {"$id": "c1a33aa5-fb1b-4c3b-9951-d37a1d9a2d42", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "医院服务联系人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c337f3d0-be79-433d-ba7b-c7573454b293", "name": "preHospitalizeDays", "title": "住院预约提前时间", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"preHospitalizeDays": {"$id": "c337f3d0-be79-433d-ba7b-c7573454b293", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "住院预约提前时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c3c8b590-e8c3-4f85-9ff3-d4cdebaaae99", "name": "stagerPhone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "c8c20896-b397-4162-a713-ea8d71dcdd05", "name": "servDaytimeNote", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "caa8938c-03eb-4cb7-958e-1d3ea74e8dee", "name": "medicalRecBy", "title": "住院病历提供方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3d77e67b-56f9-4367-8c11-c5c3cd45323f", "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"medicalRecBy": {"$id": "caa8938c-03eb-4cb7-958e-1d3ea74e8dee", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "住院病历提供方式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "caf1a96f-e184-4f59-adbe-e87a9178cac4", "name": "inPatientLegalTimePM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "d0c909a7-90f6-4a0d-81c8-9da29b8d4c7a", "name": "directSettleTypes", "title": "医院直结类型", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "d4269b18-5de6-43b3-aec9-24010087bac5", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"directSettleTypes": {"$id": "d0c909a7-90f6-4a0d-81c8-9da29b8d4c7a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "医院直结类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d6f3fe3f-cb8c-4db6-8528-d9ab5dfb40b1", "name": "outPatientBusinessTimePM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "d8027ab3-92f8-4115-8769-667cbd36d672", "name": "inPatient", "type": "java", "classType": "object", "refEntityId": "8e54de6f-a160-4eda-926b-4c9e19d085b9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "da5cd436-67c7-4ed1-bacf-b7808e45b401", "name": "outPatientLegalTimeAM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "daa354f2-a5b3-4185-9340-a735c70ba0b4", "name": "inPatientBusinessTimePM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "e043dca7-1a78-413d-bda4-420ad62d5034", "name": "outPatientLegalTimePM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "e0c5a9b1-0571-485c-9a97-430a2964c0a8", "name": "outpatientDiscount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "e1df5e12-952c-41b0-97f3-1e1e6d2220e3", "name": "netPhone", "title": "联系电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["netPhone"], "properties": {"netPhone": {"$id": "e1df5e12-952c-41b0-97f3-1e1e6d2220e3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "联系电话", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e63d3035-509d-4703-a905-bcce48d29081", "name": "cooperator<PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "e6c754fb-501d-4835-83b3-15fd19958ef2", "name": "languages", "title": "特殊语种服务", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "8c0a61b5-844d-4c84-ab9a-a5df6c9ff15a", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"languages": {"$id": "e6c754fb-501d-4835-83b3-15fd19958ef2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "特殊语种服务", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e85589e1-b5f8-498a-ab30-096508c25e2c", "name": "deputy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "主任医师", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["deputy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "properties": {"deputyChiefRegFee": {"$id": "e85589e1-b5f8-498a-ab30-096508c25e2c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "主任医师", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ecdf036c-72b4-4cb7-ad3e-06af75bef012", "name": "inPatientBusinessTimeAM", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "valid": true}, {"id": "ece7b944-4f89-4069-a6de-d37f65d3fdee", "name": "ifSubscribeDirectPay", "title": "预约是否仅支持直付", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifSubscribeDirectPay"], "properties": {"ifSubscribeDirectPay": {"$id": "ece7b944-4f89-4069-a6de-d37f65d3fdee", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "预约是否仅支持直付", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ef0f2b1e-ff0d-4b9e-879a-b68bbd78dd82", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "主治医师", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "properties": {"doctorRegFee": {"$id": "ef0f2b1e-ff0d-4b9e-879a-b68bbd78dd82", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "主治医师", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "efe5121f-fe19-45d4-b13f-9a3cb66cec4c", "name": "outPatientWeekendDay", "title": "门诊周末", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "7c88e058-0ab9-4e28-ae28-1bc38238c593", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"outPatientWeekendDay": {"$id": "efe5121f-fe19-45d4-b13f-9a3cb66cec4c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门诊周末", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f4f8abc3-65a6-4a5a-97f0-b14f6fd71905", "name": "inPatientWeekendDay", "title": "住院周末", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "7c88e058-0ab9-4e28-ae28-1bc38238c593", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"inPatientWeekendDay": {"$id": "f4f8abc3-65a6-4a5a-97f0-b14f6fd71905", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "住院周末", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "fa50d99e-d293-47c0-866a-e67efba6287c", "name": "hospitalPhone", "title": "联系电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["hospitalPhone"], "properties": {"hospitalPhone": {"$id": "fa50d99e-d293-47c0-866a-e67efba6287c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "联系电话", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "fd1b8c52-3627-4fbf-a826-527d73336f44", "name": "outPatientStatus", "title": "门诊周末营业时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a51cee4f-13f6-4708-8cb3-c568cfb8d83b", "style": "Input", "entityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"outPatientStatus": {"$id": "fd1b8c52-3627-4fbf-a826-527d73336f44", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门诊周末营业时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}