{"id": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "className": "com.open_care.order.entity.OCBill", "name": "OCBill", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00680cf6-2542-41df-aa90-abfafb378ad6", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "03cbe38e-ca7a-4f7e-82d0-9c80750d05b8", "name": "payType", "title": "缴费类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "0d8c9898-91ce-4a82-8691-5c1ca07b912d", "name": "brandGroup", "title": "售后服务团队", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "2472be98-40a9-4501-8287-3e4c2d3b39f9", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "3742835b-414d-4f75-a4f8-c7c67292b194", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "54fbefc7-dc93-408d-8679-db39106993d5", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "573f2d9b-243a-48f1-ac72-9a01ec58b673", "name": "area", "title": "售后服务地区", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "58423554-0d42-41af-8a5f-ab00eeb4566f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "5a89262c-bf7c-41b9-825b-4976266245de", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "703eed4d-401f-425d-9b94-abb575c27bea", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "749d0e46-5745-4931-88f9-1514f17e5f31", "name": "baseOrder", "title": "订单", "type": "java", "classType": "object", "refEntityId": "9f0e25c1-7bd6-4782-9edd-cae9e7c121c2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "790abc7d-6952-4772-b601-18b4f9329b78", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "850ce547-d14b-4ff9-afbb-d1bb394292f3", "name": "customerName", "title": "会员名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "9700f952-4135-4476-833f-0f832285a2ea", "name": "status", "title": "状态（正常、作废）", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "9cb49fef-a0ec-4d30-b1a5-7683a898fb10", "name": "branchName", "title": "服务中心 对应区域服务中心", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "a91df08b-969e-4898-8ffb-60d2e200311a", "name": "paymentBank", "title": "收款银行", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "ab92d223-f727-409d-ab90-28e3c0d69ac8", "name": "salesExpert", "title": "专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "b04d0b6c-6a21-4ec3-97ac-ae79caa574a3", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "baa20d37-8f61-4a06-867c-6a2e02b695d1", "name": "reviewer", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "bacc5be6-939d-4e2d-94d1-79275fa53019", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "be5e7b9e-7a79-4890-a726-f5ef75332203", "name": "payeeOrgName", "title": "收款单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "bea211f0-95c7-4262-935a-9f7e7dc10afa", "name": "bill<PERSON><PERSON><PERSON>", "title": "编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "c514166e-c247-42b1-b136-6a7b3851d07a", "name": "billDate", "title": "开票日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "c699c179-2415-4766-b35e-bf4f96f42e65", "name": "payments", "title": "支付记录", "type": "java", "classType": "object", "refEntityId": "1e4adb73-21cb-4e49-bdd9-abf12b2844ce", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "c9b10e5c-843f-429e-ba71-39cbc1f9b1e5", "name": "billAmount", "title": "开票金额", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "d94a48eb-1bb7-4f61-b993-392cda6dfa56", "name": "payScene", "title": "缴费场景", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "da959c24-5b0d-41ed-8cd5-69b6d4719a8c", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "dde9ac5f-8858-48eb-864d-b84240a3e44b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "e65fd366-5df2-41a8-8e2a-01fad2b2bdaa", "name": "customerId", "title": "会员id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "e94434bd-b60f-4d3e-9c9f-4bbd575b841c", "name": "payerName", "title": "交款人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "eb49a6fc-0515-4572-bfda-f48ea0f279b3", "name": "pdfFile", "title": "票据PDF文件在文件服务器中的存储路径", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "edd0b685-2e4b-4d65-b71d-e4755bacb6b5", "name": "payeeId", "title": "收款单位", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}, {"id": "efe47c76-eca0-4d02-a514-4d679aea4697", "name": "payeeName", "title": "收款人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8e3a58b9-96fb-4843-b82f-a49de42247c7", "valid": true}]}