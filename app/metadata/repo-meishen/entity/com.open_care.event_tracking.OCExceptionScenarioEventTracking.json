{"id": "441c09c3-560c-48e2-9317-09147e25f9dc", "className": "com.open_care.event_tracking.OCExceptionScenarioEventTracking", "name": "OCExceptionScenarioEventTracking", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "151a3d04-ecec-4fdd-a5ae-d9a04e2cf86f", "name": "resultType", "title": "错误信息类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "1adde86f-0666-43bd-83e2-d7b22d12d6d4", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "22f75110-9f31-4174-8719-affa3e878e64", "name": "makeDate", "title": "操作日期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "249dd102-ea5c-43b7-9726-0718989fffd6", "name": "createPlatform", "title": "创建平台", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "26fc19b2-1084-4eda-a804-95b7ff045f19", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "33b59066-d27b-41af-a2a4-c4c5a4b5f62e", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "4a87e091-6fbd-4d39-988a-ab31597d2a38", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "4ae33190-b727-4e73-b945-6a7a4ec6c707", "name": "sysNo", "title": "系统编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "4b61772f-c97b-4efe-b92b-a73ba5ec7280", "name": "currentDealPlatform", "title": "当前处理平台", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "644a3020-a56d-45f3-ba16-0c8f8c093e6e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "6df62590-d1d2-4c6b-86e6-680fd41a8b55", "name": "businessNo", "title": "业务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "7005d03a-f4f5-4282-b6c0-df4ae151b070", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "82dbb377-9951-432d-b1ab-c2bcea96cb3d", "name": "traceNo", "title": "轨迹号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "8900bfd8-3981-45ac-bd59-adfa817783f3", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "9dc77a33-adb0-42ab-ab2e-f9ebb6bd9b41", "name": "nextStep", "title": "下一步骤", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "a08930b0-2333-429d-95a9-9417c3766aa0", "name": "makeTime", "title": "操作时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "acf62b80-ee81-4738-91d9-16cbc3bb7a5e", "name": "sysPwd", "title": "系统密码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "b3db04fe-3eb1-4716-9da3-fd021fec190f", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "ba0564cf-faef-4229-8ee1-86799367d093", "name": "url", "title": "页面地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "bafe7cf2-c498-4a3f-9d7b-ba413987f4f3", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "bee916c1-9466-492e-a72a-019a60b5d3b2", "name": "<PERSON><PERSON><PERSON>", "title": "步骤号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "ca197803-fc0b-4747-95d5-035726d9e94a", "name": "templateVersion", "title": "模板版本号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "d048b894-5be4-46b1-a855-7561ff15b197", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "d4758f1f-4e49-4458-9659-ef77cd455ea3", "name": "businessParam", "title": "业务数据标准报文", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "df67dd76-8bc1-4a05-a08a-90656cb84ffe", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "e0d018f9-d71f-4f61-89e0-60b6f1a280d8", "name": "resultMassage", "title": "错误信息详细内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "e31d519c-7776-40b5-91a3-28557da8d1f5", "name": "sendPlatform", "title": "送达平台", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "e78c0445-c2ca-49d6-a9a4-c4b41a59ef9a", "name": "templateCode", "title": "模板代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "eef78df3-6eae-4932-a507-d7358d16d31e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "efc43d7c-4bf4-4e06-b88b-3aeb06c493f0", "name": "errorLog", "title": "错误日志", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}, {"id": "f87f482a-fcc8-4d66-a4ac-39f37f6a3713", "name": "businessNoParent", "title": "父业务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "441c09c3-560c-48e2-9317-09147e25f9dc", "valid": true}]}