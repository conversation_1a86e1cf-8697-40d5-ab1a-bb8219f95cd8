{"id": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "className": "com.open_care.event.service_order.precision_medical.OCAppointmentServiceOrderPrecisionMedical", "name": "OCAppointmentServiceOrderPrecisionMedical", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "15f16a96-ea81-4934-bf99-0ed91bbec226", "name": "currentServiceCount", "title": "当前服务次数是第几次", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "1ad47d80-f0ad-4973-832e-42f02c94f872", "name": "precisionMedicalServiceRecord", "title": "实际使用人信息", "type": "java", "classType": "object", "refEntityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "347a38ae-9460-4f8f-95f3-a6dc8ad1047a", "name": "recipient", "title": "实际被服务人", "type": "java", "classType": "object", "refEntityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "3a4afb09-cd29-438c-9af8-0488d8e4749a", "name": "processInstId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "3c18c439-9502-45ef-b21b-2b1bf3a7d462", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "59045d2e-8eb2-4d84-955d-d1914ede741c", "name": "appointment", "title": "预约单", "type": "java", "classType": "object", "refEntityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "5a7863c3-8c47-443b-8de2-987a00c4e4c3", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "5c88ab5e-44b8-4609-af39-9ca97e8d0b3e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "62807ee7-5545-476b-a9af-dbf843264e41", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "6f2d7805-7e56-4150-9fcb-39410ac50be6", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "79686a85-6ad2-459d-a8bb-873af94bb83c", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "81591a21-430e-4a9f-a327-5785fecde60d", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "9dce3d50-09a4-481c-a03d-8778a4d94b10", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "9ddb1d78-c986-4bc4-aa60-6d8de3e3238a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "a45585db-1cc2-42ba-873e-1d602448b4d1", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "a733df7c-fe20-4a59-8035-8efd9d2c49b2", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "c4fad636-cf44-4c4e-98b7-a8874044187b", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}, {"id": "dab643e3-59ac-4b43-8af3-9a0a4b30aa5a", "name": "isServiceEnded", "title": "当前服务是否已结束", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "valid": true}]}