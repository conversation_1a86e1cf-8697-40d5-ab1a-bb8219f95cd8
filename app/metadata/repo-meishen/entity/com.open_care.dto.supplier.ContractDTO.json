{"id": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "className": "com.open_care.dto.supplier.ContractDTO", "name": "ContractDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05bba216-1482-4856-b934-12f7ea624c73", "name": "auditStatusJson", "title": "审核状态", "type": "java", "classType": "object", "refEntityId": "9a815837-52c2-4449-9fdb-c0271fd3c337", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "0e1bc636-7895-4abb-9437-df38208fa92e", "name": "contractContent", "title": "合同正文", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "10913cd6-739e-4ac0-b5ba-4fbf66df9ece", "name": "flag", "type": "java", "classType": "ContractFlag", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "217f6093-df2d-4513-97ab-0c6c83cfb9af", "name": "created", "title": "创建日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "23302e32-1350-4eb8-a4ce-8cf12e5f1fcb", "name": "contractRelationShip", "title": "供应商账户", "type": "java", "classType": "ContractRelationShipDTO", "refEntityId": "5a79ed74-4a16-427e-b37d-542abddbcd58", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "278054b0-ab94-41e3-8fb0-65deed4c6afe", "name": "operator", "title": "经办人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "3970dac5-c016-4ff9-87ea-1303c6fed93c", "name": "depositAmount", "title": "合同押金", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "4f30e2c1-808b-411c-a59c-2c6cb0feee72", "name": "signerOrg<PERSON>d", "title": "签约机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "532e8c0f-efa1-4e6f-bd0d-abb51621bc21", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "5b56743a-d559-4788-9b37-f7484a03d9fb", "name": "updatedBy", "title": "更新人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "5ebac52c-fecd-4508-8157-45d2bac1416e", "name": "contractPersonInformation", "title": "合同人员信息", "type": "java", "classType": "object", "refEntityId": "1e391bd0-26c0-4da3-9056-12c8a6f6ef7f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "6861c9c9-2221-4d0b-858d-b063c90185e5", "name": "contractStatus", "title": "合同状态，通过有效期计算 大概有三种，未生效，生效中，已生效", "type": "java", "classType": "ContractStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "99dc3c100ee1d423b9c8dc66b6c77ab9f6c7477b", "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "6d313192-eeb1-48ab-bfa1-e43a4d69c73f", "name": "endDate", "title": "截止日期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["endDate"], "properties": {"endDate": {"$id": "6d313192-eeb1-48ab-bfa1-e43a4d69c73f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "截止日期", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6e821ed7-93b9-413f-8280-233be6fa46be", "name": "contractOrgId", "title": "合同主体", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "7b529081-6d75-4a27-addc-be3f90ffcb84", "name": "deposit", "title": "合同押金", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "7fbd806f-287b-43e4-8bec-d141f62b1108", "name": "contractName", "title": "合同名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["contractName"], "properties": {"contractName": {"$id": "7fbd806f-287b-43e4-8bec-d141f62b1108", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "合同名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8423a1ed-f675-43f4-bcf2-b3e7b2c4d041", "name": "otherAttachment", "title": "其他附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "86d53937-46de-4a6d-9413-53342cb6cf6c", "name": "supplier", "title": "供应商", "type": "java", "classType": "SupplierDTO", "refEntityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "8a067f2c-65eb-4272-885b-3514c796c9fe", "name": "contractType", "title": "合同类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "bf0c1b19-4388-480b-a41e-88e7d149b19e", "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "8db92625-fc04-4ade-90c1-89ac385ed38d", "name": "signer<PERSON>rg<PERSON>ame", "title": "签约机构名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "909fdee2-4401-454d-b0a1-9e91428127fe", "name": "advancePayment", "title": "合同预付款", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "96d2a2d1-e72b-4284-be8c-f8848c6b5c41", "name": "contractOrgName", "title": "主体机构名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "9b1dcba3-6f7c-461c-af53-069da3af82a4", "name": "comments", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "9da48c4b-9e37-416f-bf57-29b16c43f48a", "name": "startProcessType", "title": "启动流程的方式", "type": "java", "classType": "StartProcessTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "aa681925-b22a-483c-8138-50116b22bf24", "name": "advancePaymentAmount", "title": "合同预付款", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "ac849463-07a6-48b2-aa91-e3cb1938d4b0", "name": "contractCode", "title": "合同编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["contractCode"], "properties": {"contractCode": {"$id": "ac849463-07a6-48b2-aa91-e3cb1938d4b0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "合同编号", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b2620367-6a35-4823-b709-924846e23c2c", "name": "contractSettlerOrders", "title": "合同结算信息", "type": "java", "classType": "ContractSettlerOrderDTO", "refEntityId": "9f0e039a-75c6-4041-827f-aad385f04768", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "b5f3b0a1-0027-4d45-9ae8-9edd1cbb5edb", "name": "renew", "title": "是否续约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["renew"], "properties": {"renew": {"$id": "b5f3b0a1-0027-4d45-9ae8-9edd1cbb5edb", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否续约", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b645d1f7-9c12-4441-8efe-0ed1fd561ac0", "name": "masterAgreement", "title": "主协议", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "b7b9dcff-59e2-4c78-ba5d-139b72def9a4", "name": "startDate", "title": "生效日期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["startDate"], "properties": {"startDate": {"$id": "b7b9dcff-59e2-4c78-ba5d-139b72def9a4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "生效日期", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "be9e781b-47f6-4261-83fd-13c5816d02b1", "name": "signDate", "title": "签约日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["signDate"], "properties": {"signDate": {"$id": "be9e781b-47f6-4261-83fd-13c5816d02b1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "签约日期", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c365e544-c23d-4aa0-9b05-fc295fc237ea", "name": "sideAgreement", "title": "补充协议", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "c435698f-6b9b-4be1-9db1-c44318ba268e", "name": "updated", "title": "更新日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "c516822f-c509-4a1f-a699-d0b13fa5fa9d", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "cba0e14f-bfb6-41af-bc24-d4a020f9e592", "name": "operatorMobile", "title": "经办人电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "cce32eef-4ab5-4a6a-a363-20ee4823990a", "name": "amount", "title": "报价金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "dc93bb25-b0f0-4630-9302-ad15a7c33a3b", "name": "supplierAccount", "title": "供应商账户", "type": "java", "classType": "SupplierAccountDTO", "refEntityId": "659e04f7-47de-4d51-855b-e81e1bde5f9f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "ea403a36-ff80-4d10-8373-56c558653309", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "f040158c-e294-4f77-9a5b-d9fda4b2911b", "name": "created<PERSON>y", "title": "创建人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}, {"id": "fab8579e-a859-49b1-8da3-2cbb087fcaa6", "name": "payMode", "title": "付款方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15fb1dcf-c5ad-48cf-a33d-d63a5e981094", "valid": true}]}