{"id": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "className": "com.open_care.dto.work_order.WorkOrderRelationshipViewDTO", "name": "WorkOrderRelationshipViewDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0626676f-7205-4fc6-a1c1-fd0e9ad9b17b", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "1cb930f9-2b12-458a-99b3-478ed3245a2d", "name": "bindSessionFlag", "title": "是否绑定会话", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "2233e783-2cf4-4cb0-af02-50886dd64f16", "name": "appointmentId", "title": "预约单id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "224465f4-dca8-442b-bec4-a06e9e7ea7cc", "name": "orderType", "title": "工单类型", "type": "java", "classType": "WorkOrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c2bc1be9f0cdd9d34698f2a19bceddae863a5d64", "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "25723d92-c87c-42cc-9b1d-f00cb387e792", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "2eebe26a-5e5c-473c-997a-2367e16a088a", "name": "orderNo", "title": "工单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "2f655f9c-ee22-423b-b4c8-a40314af707e", "name": "sessionNoList", "title": "会话编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "330f61f1-b950-4609-a489-5b4dfa21e599", "name": "appointmentStatus", "title": "预约单状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "42f21cc6-d148-4d55-9b86-07e649b3506a", "name": "customerNo", "title": "客户流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "4f3f173c-1d00-457f-a6c0-1f40ab0e6cb5", "name": "workOrderSource", "title": "工单来源", "type": "java", "classType": "WorkOrderSourceEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1c4dfd922dae2d24e5c7384940f61def2795f507", "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "511b7c83-b681-4c96-b04d-c512826945d0", "name": "personalCustomerMobile", "title": "客户手机号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "5ba3f7f1-00d6-4790-87e4-22ba022faeb6", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "6812334a-aa5d-4973-99bd-2e32e369fb79", "name": "userNo", "title": "工单创建人员工号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "791f9bba-dcec-47b3-b174-3f562f85cf4e", "name": "workOrderStatus", "title": "工单状态", "type": "java", "classType": "WorkOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "6ed78f0b2ead95a3f74447ef29f94f25411bd8b5", "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "81c06fa3-ab6c-4afd-8782-4f52f6225884", "name": "priority", "title": "优先级", "type": "java", "classType": "ProcessPriorityEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ebd12d3e01d79d6377c2ebd9af6b5ae07f81138c", "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "a0a134a0-9745-491a-b471-8c9d5d5ed5a1", "name": "personalCustomerName", "title": "客户姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "b9a9bf65-d53e-419d-9f01-6744aad14e3f", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "bc550f6c-0e15-4e12-af90-1da287f6ccb8", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "c51efcbf-f1a2-48ee-ad65-c866cc3ca6d7", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "dd41a29b-0026-420f-9e4d-fc6eff5a7fe8", "name": "personalCustomerId", "title": "客户id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "f50bd25b-76af-49fb-b819-06795595dfb4", "name": "appointment", "title": "预约单", "type": "java", "classType": "ServiceProductAppointmentDTO", "refEntityId": "4182111c-37a0-4723-8fe9-2032f3ac3da7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}, {"id": "f79ddb3a-9e28-492a-b433-3dfc8e79f1c4", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3dddee3-28d5-425b-8beb-755fb3729ee8", "valid": true}]}