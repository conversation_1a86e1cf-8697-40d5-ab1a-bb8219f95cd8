{"id": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "className": "com.open_care.medicalMicro.customer.finalExam.OCCustomerFinalExamConclusionSource", "name": "OCCustomerFinalExamConclusionSource", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0a00c548-4063-4676-b544-55f10e883ab1", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "0ce8dddb-37e9-483e-9f47-804d5a91d390", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "201c629e-ee13-478c-af64-0bc0e94521d9", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "5abdc748-15d4-48d5-a9d5-0048895ee79a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "5c7c863e-9b80-484e-98da-59a8be860557", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "88c18bb8-5f95-42ec-a1de-585dec3f8b99", "name": "customerFinalExamConclusionInfo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "8f4a6f4a-d43d-460c-9f84-7c7f56792a86", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "941ea4d7-2001-4ab6-9b15-77fadc46be5a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "95556aeb-c0a0-496a-baf5-c0062f6f1104", "name": "customerExamConclusion", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "977762d8-2e0b-43f8-86b4-5c46abb3ba2b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "a6b9334f-f5e0-45d8-b913-7415ea889474", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "ac9da425-5ffb-47ef-9303-745c4361eb5f", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "c5fe2a81-19e3-4027-b2af-3cb30969557e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}, {"id": "efd08780-d75c-4001-b29b-50456c5cdffb", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "valid": true}]}