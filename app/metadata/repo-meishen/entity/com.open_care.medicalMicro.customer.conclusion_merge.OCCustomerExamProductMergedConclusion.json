{"id": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "className": "com.open_care.medicalMicro.customer.conclusion_merge.OCCustomerExamProductMergedConclusion", "name": "OCCustomerExamProductMergedConclusion", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "052408d8-c515-4b79-8fc8-f70fbd09db44", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "09c67717-a79a-465c-b4e7-e5a16d65561d", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "1d1d063f-f231-47d1-a138-7234c504398f", "name": "mergedConclusionOcIds", "title": "被合并的结论词", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "20d1f55c-cdee-43e4-b710-e1a4a3918c50", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "24964983-3ddb-40e7-83c4-91f0858d6008", "name": "postMergeConclusion", "title": "合并后的结论词", "type": "java", "classType": "object", "refEntityId": "33adf15e-809a-42c8-bfb7-667c1c086445", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "28398205-3076-4c94-814d-2fb2669f1270", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "34afb61f-cb09-45a2-bd1a-87a46e4d5453", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "37b2a8f0-0c05-42b5-aab4-924568af79cf", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "42d877a7-eb63-4525-a14f-fe3548ba7909", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "4e4a1ae5-d374-464d-b4fd-0e6ebc230c91", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "4e8da6b7-6328-4868-b856-585caf45531f", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "550401e5-ac33-4d61-a424-e6d1669af9e6", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "696a7e18-7d24-4bc8-bcd8-125f369e936e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "6f1d0140-c6c6-4bc6-9714-ba31ad51bf52", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "717cf92e-7ae1-4ad9-80c1-ac6b75563196", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "7f8cf2fd-2a39-47b7-9736-5c66504127a1", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "8a15cb42-43d9-437b-9e48-006deb411902", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "8cba7288-00ce-4cc0-adfd-2bb37f57dfd7", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "96570cbe-a27c-433c-8c27-a5ba6886f0ea", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "a6255cec-df98-460e-93ed-c07468c95a73", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "ab734aa7-9530-4669-aaba-594d4f9eeacc", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "b42a4885-2ad2-4e8d-ac8e-7d9499e40cc3", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "b4969b97-90b9-46f6-9812-cd46e5e6f03e", "name": "customerExamProductOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "b4d11396-540a-49dd-9f33-fb7a2af8488d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "cff561e1-5fca-487d-aac0-077897cbfb84", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "d5cfc5c0-6fd2-4fe9-a365-236d0c672e85", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "e88e6760-ab96-4e84-8c36-c2f0a5ee631b", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "ecc3e49c-2e1d-4a75-b447-2da3f71968b0", "name": "needMergedCustomerConclusionOcIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}, {"id": "edf69471-bedd-41c7-b78e-19e32687e420", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9655a17f-148c-4ec5-8aee-017b5b5fde6a", "valid": true}]}