{"id": "9d555168-7a0d-490c-bc32-a69ab01ec993", "className": "com.open_care.sys.OCFlowNodeConnect", "name": "OCFlowNodeConnect", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01bbc74a-ecff-45c5-a97e-0d43021cf58e", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9d555168-7a0d-490c-bc32-a69ab01ec993", "valid": true}, {"id": "8d162417-4d46-466e-a634-4e9e02fe0119", "name": "flowNodeKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9d555168-7a0d-490c-bc32-a69ab01ec993", "valid": true}, {"id": "afe7797a-9bb8-41b2-b451-ce14a153304c", "name": "sequenceFlow<PERSON>ey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9d555168-7a0d-490c-bc32-a69ab01ec993", "valid": true}, {"id": "c5833cbc-e4c5-47e2-922e-616104c54d51", "name": "processDefKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9d555168-7a0d-490c-bc32-a69ab01ec993", "valid": true}, {"id": "e5ea8ae8-43c7-4f99-8e7c-633d37063097", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9d555168-7a0d-490c-bc32-a69ab01ec993", "valid": true}]}