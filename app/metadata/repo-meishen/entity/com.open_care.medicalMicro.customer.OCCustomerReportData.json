{"id": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "className": "com.open_care.medicalMicro.customer.OCCustomerReportData", "name": "OCCustomerReportData", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "01c51dbc-7d57-4832-975a-9688ba33e39b", "name": "signTime", "title": "签收时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "0c424561-2ec5-461c-9024-d651e414ff9b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "0cca71d9-1925-4ca4-8fc5-3cbb6ca35249", "name": "transferUser", "title": "报告交接人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "0da3db18-121b-4051-81df-b2961027dc1d", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "16bd8d8f-56cf-4fcc-af81-fe01f9452709", "name": "printData", "title": "打印数据", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "1ec1e82a-60bb-4a97-bc8a-df1413837526", "name": "customerFinalExamInfoOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "21e91d43-eb2c-48eb-8770-288eda41861f", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "2bec7fd8-215d-4c7d-bdf8-ee547fe6ebcb", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "2c2e2b8d-c670-4e60-8832-fec81705d726", "name": "reportRecipient", "title": "报告领取人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "2d2ba15e-81f0-4db0-b368-73beeba99cb6", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "3154d4e2-9042-47c5-9bf7-dc0789f473c6", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "36b91116-f9d4-45c9-a308-5acd6fe8b945", "name": "isTransferred", "title": "已交接", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "3ed501a2-0da9-4ed9-a7e1-333f4301a0c2", "name": "<PERSON><PERSON><PERSON>", "title": "报告接收人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "3f4bdeb1-66dc-4ad1-bbf8-d661f542ad70", "name": "isSend", "title": "报告发送状态", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "43c564b8-7f17-44e8-86aa-8e51e2812fb0", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "45ca19ae-40a7-4716-8750-e95e3172564b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "47584067-0159-49b1-9e7d-d9e46a46f07b", "name": "transferUserName", "title": "报告交接人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "50371f3a-07f1-4d6a-a2f0-f55e8fbee60b", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "55189419-40f6-4e1f-87e7-1d20c6dfbb37", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "571333aa-90e4-4194-88ba-b3a72f6352cd", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "5962a07c-82b9-4cab-aa8a-435473c115f6", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "60451d82-c9fd-44cc-aced-363798a8b3da", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "6392709d-6f7c-422c-97ac-9c140296d0d6", "name": "printTime", "title": "打印时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "67a1ac6f-e394-4b64-bdd8-605c53a6183f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "6a5716d5-86be-49ac-87b2-1ed96fc099be", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "6beef6b9-a01c-4560-826d-d54369868ed1", "name": "signer", "title": "报告签收人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "6d2b7231-6637-4914-8fb6-aecda49661b6", "name": "sender<PERSON>ame", "title": "报告发送人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "6fee3275-837c-4c78-ab43-1b22a60878c7", "name": "signer<PERSON><PERSON>", "title": "报告签收人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "756338f5-6212-46be-8718-8456a08f81ba", "name": "printType", "title": "类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "78ccfed8-b02c-412b-8574-282e9a014c3c", "name": "filePath", "title": "打印文件路径", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "7f20b492-a0bd-4218-8262-b612af967395", "name": "printerOcId", "title": "报告打印人ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "877f4c84-60e3-4f71-9a23-dc752c56adc1", "name": "isReceived", "title": "已领取", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "97a239df-b081-4675-b23c-424520810186", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "9e763c01-7372-4939-bd42-12e9c721a4e3", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "a820e96b-26fa-461c-9947-d1bbf1a16c6f", "name": "sender", "title": "报告发送人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "abbb9682-ebd2-4778-a76e-d52cb68ac8d6", "name": "pid", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "ae7fb4e1-69f9-46de-9544-23e9f829b59f", "name": "isPrinted", "title": "已打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "b027b939-807d-45ec-8dfc-b71e5e942055", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "bc4f1bbe-fe3c-4e1f-98d4-d847bfaaced0", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "be555f93-2041-4657-87a8-311b3d40d6d4", "name": "printTimes", "title": "打印次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "c2254eef-cc91-48af-b687-de76e3b04504", "name": "examOperationInfos", "title": "操作信息", "type": "java", "classType": "object", "refEntityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "ca79a156-c58a-4657-9908-69806d11a490", "name": "isSigned", "title": "已签收", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "d00b72fa-5ff8-40f1-8aa0-c0857e5d8ac5", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "d783a7aa-b62d-4657-9534-aa22e0143da0", "name": "transferTime", "title": "交接时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "d7a4350a-a6d2-4d9f-8803-91426db8698e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "d7e0dceb-e98a-4e7b-9096-68102fc1ee0f", "name": "printerName", "title": "报告打印人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "e0bb57f1-c9dd-4a41-a08b-0ed107be8229", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "e2d6e3d4-0fa0-49c1-8e65-0d0e3efe63be", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "eb71253d-bb14-4011-8b1b-219f014633b9", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "f28f5d84-8113-482b-b4f6-790c075b91d4", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "f60471ed-9a72-4e5a-8e7b-a7fd8107a8d3", "name": "sendTime", "title": "报告发送时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}, {"id": "faf8fc37-65ea-4b36-b2b6-f227fed0e991", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd74a2ad-4b05-496c-8273-0a5f81aca695", "valid": true}]}