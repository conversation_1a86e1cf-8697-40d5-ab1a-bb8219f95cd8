{"id": "cd86dc68-5dc3-47d4-a9db-9b3f8e4ed606", "className": "com.open_care.dto.sys.user.QualificationDTO", "name": "QualificationDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "09ffc4cc-b1dc-4061-ade3-4cb35c8e3b7c", "name": "qualificationStatus", "title": "资质状态", "type": "java", "classType": "OCQualificationStateEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "e6529c7698cb95827049a02677bcffd536243947", "entityId": "cd86dc68-5dc3-47d4-a9db-9b3f8e4ed606", "valid": true}, {"id": "20960152-6786-4547-8969-c0650c5c67a0", "name": "qualificationType", "title": "资质状态", "type": "java", "classType": "OCQualificationTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "056e7946e1bb1fe0c014695fb51304657c153e2f", "entityId": "cd86dc68-5dc3-47d4-a9db-9b3f8e4ed606", "valid": true}, {"id": "5805829e-fa61-4230-a8ac-dd011040a844", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "cd86dc68-5dc3-47d4-a9db-9b3f8e4ed606", "valid": true}, {"id": "8743da4b-fc3b-4385-9db4-113a73cd1d32", "name": "qualificationPhoto", "title": "资质照片", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cd86dc68-5dc3-47d4-a9db-9b3f8e4ed606", "valid": true}]}