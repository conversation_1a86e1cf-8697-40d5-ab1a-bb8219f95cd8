{"id": "124b47ec-4db0-43f5-bd37-745281c284b6", "className": "com.open_care.medicalMicro.medical.OCBaseNumberDataTypeExamItemSubmitInfo", "name": "OCBaseNumberDataTypeExamItemSubmitInfo", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "数字型检查项目送检信息", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "085650be-c96b-4f28-9b48-f312a2fb062b", "name": "interfaceName", "title": "对应接口项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "0de95859-75a5-4617-8aec-4802bf224125", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "1208c139-5fcc-41ea-9279-f26fe8a83ec8", "name": "baseQualitativeDataTypeExamItemAttributes", "type": "java", "classType": "object", "refEntityId": "84f50ddf-d60f-4277-bc1a-e6544788b038", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "3e37981f-b57b-4c33-98e0-be3529e63417", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "419060db-5d47-46c3-af4a-8921ae8a5304", "name": "submitOrg", "title": "送检机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c69a5bbf-fac7-4a47-940a-1d841add43gg", "style": "Select", "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"submitOrg": {"$id": "419060db-5d47-46c3-af4a-8921ae8a5304", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "送检机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4366707c-809d-41e0-8e85-e98e4dae1d4a", "name": "uint", "title": "单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "54464525-fc44-4465-b8d2-29854b5bcb4d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "59583646-7507-4ffa-a88a-2e2d9db1ba43", "name": "roundingOfDecimalResults", "title": "小数结果四舍五入方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "02840623-ef5e-4cb7-8e91-8d4da6e07896", "style": "Select", "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"roundingOfDecimalResults": {"$id": "59583646-7507-4ffa-a88a-2e2d9db1ba43", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "小数结果四舍五入方式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "67dde9ba-09b4-4f81-a6c9-32172bfa2d94", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "6938cf0c-2289-40f4-b1c0-574ecb83c3c4", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "6e9240d0-be1f-4668-a43e-5d6e0b6e7f95", "name": "containsMaxValue", "title": "包含大值", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "702e6e02-a099-4eb9-a521-a6b394379411", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "7f6108b9-5b07-4fcd-bcf6-33aff4d87fbb", "name": "appliedReferenceValue", "title": "应用参考值范围", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "80a8ad64-a48a-44ee-8648-46dea49621c1", "name": "submitOrgList", "title": "送检机构List", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "83deb0a8-6d19-4f86-bb89-9f533e94d07e", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "85347702-30cf-4dc8-b567-21928c43c872", "name": "defaultSubmitInfo", "title": "默认", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "856d8ca9-8a58-4629-a17b-112bec788dcf", "name": "baseNumberDataTypeExamItemValueConfigs", "title": "性别参考值维护", "type": "java", "classType": "object", "refEntityId": "e3f9917b-5db6-42fb-9ed5-88f94ac8944a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "8eb02f2e-24a8-41c9-8431-2348f2801e42", "name": "appliedMajorReferenceValue", "title": "应用重大阳性参考值范围", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "930e7ef3-67d7-4f6f-b606-8e593919af07", "name": "containsMinValue", "title": "包含小值", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "970f0724-5858-428e-9831-16711f05a57d", "name": "lowValueFlag", "title": "低值标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "9790dd50-5f52-43c3-813d-9009d3f15b82", "name": "interfaceCode", "title": "对应接口项目编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "a07608ff-8c5d-4ccb-9196-4b60a7812a47", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "a596ab12-8a16-42b8-b1a3-329866d8b464", "name": "highValueFlag", "title": "高值标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "afcd9ece-c11f-470f-ae7b-07b98adc4abb", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "b866efd5-f595-43e3-891d-ec707f60dd7d", "name": "baseNumberDataTypeExamItemAttributes", "type": "java", "classType": "object", "refEntityId": "f4d7d121-b8b4-4751-a1e1-859d5dec7846", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "b9606ad6-9fde-4bb8-bc2d-eea501cca8fe", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "bed49801-fc99-4b81-814f-09ffcdf71fd1", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "c7d06265-ceea-4902-bc07-9210e9ee2e6a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "d656b839-bc0f-4f9c-a125-0d9abfcdd11e", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "e02079af-5c40-4ae5-906b-8cbd99b69bb5", "name": "decimalPrecision", "title": "数值精度", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "fd09df09-9c56-4aac-aa32-07433e65305a", "name": "referenceStitchingStr", "title": "参考值拼接字符串", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "ff26dc47-d877-4954-ae63-b6d05b38f4f4", "name": "discardLastDecimalZero", "title": "舍弃末位小数0", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}, {"id": "ff9619fc-e83d-45ed-9039-ba7bd526d80f", "name": "submitEquipment", "title": "送检设备", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "124b47ec-4db0-43f5-bd37-745281c284b6", "valid": true}]}