{"id": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "className": "com.open_care.dto.productCategory.ProductCategoryRoleDTO", "name": "ProductCategoryRoleDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "157c072c-3de2-4cb8-a3a5-3aa46d18ea23", "name": "internalService", "title": "内部服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "49a99855-005a-4e82-8eda-90144710765a", "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"internalService": {"$id": "157c072c-3de2-4cb8-a3a5-3aa46d18ea23", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "内部服务联系人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "16e51570-c66c-4e05-b17e-e1c5c324ec3e", "name": "productCategory", "title": "服务类型", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f657a2ec-e3aa-4b1d-a621-3dcceea15325", "style": "Select", "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"productCategory": {"$id": "16e51570-c66c-4e05-b17e-e1c5c324ec3e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "服务类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "251747cb-6733-4dd4-a8d7-69e3d943c481", "name": "productCategoryName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "32b210e5-92fc-4a8e-9eff-59f4fb6e0818", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "387ecb4e-d36b-412d-bd33-420c272c9514", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "5668fab4-774b-4faf-8e24-b3da57aba1f8", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "71411e8e-cd75-4b51-8a26-4f4e48dd1b91", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "71491774-3115-41f3-9c6c-55f5061d5bdd", "name": "contract", "title": "合同ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "7ee8ee2a-69d3-4c91-b905-e14ac597d7bf", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "841518cb-c82f-4c11-9d29-82f028c8d7ed", "name": "productCategoryId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f657a2ec-e3aa-4b1d-a621-3dcceea15325", "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "8e6832cf-2902-4e75-b870-85a7a89142fc", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "8ec83e71-790a-4099-9e40-56cb60bd8430", "name": "hospitalService", "title": "医院服务联系人", "type": "java", "classType": "object", "refEntityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "e1b7bf38-84f0-4202-859d-e3e4f22c9734", "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"hospitalService": {"$id": "8ec83e71-790a-4099-9e40-56cb60bd8430", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "医院服务联系人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "aa6de3de-a2f8-415c-a90f-0593ce25752b", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "ab2237a3-98fa-4db5-ae3a-0d62bc50e404", "name": "partyRole", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "b7f37b77-2cf0-4d7a-bf94-e464f7145feb", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "c40912c2-bece-486f-8096-70585f124632", "name": "party", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "d33e6e73-307a-4b5d-910b-d7fbcc036db9", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "e5beae31-cfe8-4a74-a805-9677eabc559e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "e89f9a1e-8a3b-4ab1-80bf-582eef23f2a2", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "ee7a370e-473c-491b-8834-302c512593f2", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}, {"id": "eeb7017b-cb66-4576-a3f8-08c3dd512e5f", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3a51a217-3bc2-4c65-9f2e-07255f955f19", "valid": true}]}