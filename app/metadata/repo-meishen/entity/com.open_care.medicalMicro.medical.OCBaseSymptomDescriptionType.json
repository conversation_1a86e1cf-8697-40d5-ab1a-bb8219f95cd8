{"id": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "className": "com.open_care.medicalMicro.medical.OCBaseSymptomDescriptionType", "name": "OCBaseSymptomDescriptionType", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "症状描述分类", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "267fd4fb-85d8-4f7a-a08f-e0da411e2c5b", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "2dd2f79b-3ad6-4f98-adc9-c2aca62c880c", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "2dd2f79b-3ad6-4f98-adc9-c2aca62c880c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4057ac43-f78e-40bd-bd5b-0a3fc9ce7378", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "452ca9f3-d007-4275-bf23-771b81152ed4", "name": "parentType", "title": "父级分类", "type": "java", "classType": "object", "refEntityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3fcdd494-ce35-40ba-a85c-2d3baa0fc9dd", "style": "Select", "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"parentType": {"$id": "452ca9f3-d007-4275-bf23-771b81152ed4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "父级分类", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "47626c59-772d-40da-8b45-fb92386c780b", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "4fb245f7-a04c-4daa-8e93-8ade779fde0b", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "52a80600-f153-4293-9054-32f1ebe8e92a", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "52e1d5c5-dce0-4184-a5f2-adefc05c81fb", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "58f79658-aa80-4d87-aff6-a7c35de5169a", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "64235c2c-8006-4564-b321-2631e2e3d81d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "6d21a806-7dff-4c3a-a5ca-6e1ce578e5c4", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "78e325ce-0ba1-4f6f-bec1-7ee17f5d99a8", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "7ffab835-4abb-4b2d-b80e-8ef866d1dff5", "name": "examServiceTypes", "title": "适用服务类别", "type": "java", "classType": "object", "refEntityId": "e2793e20-5280-403c-8003-28cb1bc29135", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "cffeaf80-0563-4b6d-822b-64eaeed76838", "style": "Select", "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"examServiceTypeList": {"$id": "7ffab835-4abb-4b2d-b80e-8ef866d1dff5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "适用服务类别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a4d6994e-44ed-474e-9a00-d0a5a99c2fd1", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "aceb7eaa-be97-4162-8204-c1835cead955", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "b429ff7f-7265-4580-86f4-ef6c8b76b50c", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "bd850b63-effb-4717-9531-7fa42172a949", "name": "name", "title": "分类名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "bd850b63-effb-4717-9531-7fa42172a949", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "分类名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d1236055-d66e-4ce6-8a5d-30433f55bb5d", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "df53ba68-8a46-432a-bf47-da8c5331d23c", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "e8ab4293-f21f-43db-82f7-1a7e2323fd03", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "f8da61d4-9759-4cc7-aceb-3d1d992280af", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}, {"id": "f947272a-4e50-48bb-9201-f4b24c1f5f11", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "798b00b4-7e9e-4d54-86cb-b277b0d698bf", "valid": true}]}