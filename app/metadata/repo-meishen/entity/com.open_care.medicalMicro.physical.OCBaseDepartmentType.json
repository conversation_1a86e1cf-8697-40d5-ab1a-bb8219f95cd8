{"id": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "className": "com.open_care.medicalMicro.physical.OCBaseDepartmentType", "name": "OCBaseDepartmentType", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "科室类型", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "00469d55-bd58-4ab5-ad4b-3125ee5cab00", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "01dcaefd-bcd9-4f83-9cfd-852f53b2d01f", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "043a7045-9901-42cd-95bc-4ff5694a8d1b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "1bc0e6c7-9aa0-47b7-9b2a-4779a3775997", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "2b6c521b-a75e-4c5b-ba07-dc5f0335c6d7", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "2ccb7ecd-eb72-4791-8369-35b8171ac5d8", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "2ccb7ecd-eb72-4791-8369-35b8171ac5d8", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "47e20ed5-7a37-4390-a352-3b56ace026a8", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "47e20ed5-7a37-4390-a352-3b56ace026a8", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "51b16346-bc1b-4991-bbe8-d14e8c5dc718", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "617dec8f-0ccc-448c-ba79-5bb510eae8c5", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "63f6cfdc-bc6e-4a68-925e-5f341bffd994", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "67379ead-d8d6-41a9-a9d5-48c5e3830892", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "6ec2a26b-1102-4808-a8a0-ed61e600623c", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "7bf09a81-6313-46ea-8aad-e5700a34ecf2", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "8117d2c5-bc42-4b5b-ba48-22c5c9dd958f", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "a50b4870-d640-48e9-a786-6910fbae0dd0", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "a85a08ac-be9d-4cc2-b3fe-960f958172c3", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "ae9e05a4-1373-4818-8adb-3ec34165187d", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "b8aa7c8d-9bf1-4428-ae6a-c2d8451df94a", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "b960f911-d9fb-47e5-a1cb-3e5039e8ec64", "name": "parentDepartmentType", "title": "父级类型", "type": "java", "classType": "object", "refEntityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "33e447eb-9299-43de-998e-5506387c4187", "style": "Select", "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"parentDepartmentType": {"$id": "b960f911-d9fb-47e5-a1cb-3e5039e8ec64", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "父级类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c257aa4e-2e52-484c-b460-67f980261c87", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "c88b522b-4c15-4134-ad32-d69ac4e3a282", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "valid": true}, {"id": "ddaec3b5-d8a6-4a4e-84dd-b7ec6e82c488", "name": "outPatient", "title": "门诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "84a61bcf-ba29-45e5-a7e4-b71de7ad423e", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"outPatient": {"$id": "ddaec3b5-d8a6-4a4e-84dd-b7ec6e82c488", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门诊", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}