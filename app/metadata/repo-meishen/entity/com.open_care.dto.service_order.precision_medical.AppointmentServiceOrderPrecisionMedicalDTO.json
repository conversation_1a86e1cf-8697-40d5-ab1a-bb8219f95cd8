{"id": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "className": "com.open_care.dto.service_order.precision_medical.AppointmentServiceOrderPrecisionMedicalDTO", "name": "AppointmentServiceOrderPrecisionMedicalDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1076700f-919e-4e05-9288-643c2a493b03", "name": "appointment", "title": "预约单", "type": "java", "classType": "ServiceProductAppointmentDTO", "refEntityId": "4182111c-37a0-4723-8fe9-2032f3ac3da7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "1f78ff42-5bc9-4e7e-a4c5-f059eaf3a438", "name": "recipient", "type": "java", "classType": "AppointmentServiceOrderRecipientDTO", "refEntityId": "f2213066-01ef-49d2-9cd2-8d238291e24b", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "32118992-a00d-40e0-a868-f113a9f29442", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "4abf49f9-fb4e-4929-8d31-dfbab5370f84", "name": "currentServiceCount", "title": "当前服务次数是第几次", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "66afa4b0-38e2-45be-ae1a-f4503a6d3997", "name": "precisionMedicalServiceRecord", "title": "实际使用人信息", "type": "java", "classType": "PrecisionMedicalServiceRecordDTO", "refEntityId": "3a3d5b98-7f37-4f86-b10c-13bb2c1cc9c2", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "76edb8c4-e613-4d1e-81fc-68a8d676da82", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "7e9189b9-46fc-461b-9266-0bbcc87c98c0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "9383b44a-b508-4237-84ba-118be6c785a2", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "95debd1e-d747-44b0-85b6-3425db2d66ab", "name": "isCanEdit", "title": "是否可编辑", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "b707cc0d-f25d-42e2-a027-d00a27412d85", "name": "taskInfo", "title": "任务信息", "type": "java", "classType": "ProcessTaskInfoDTO", "refEntityId": "492f5bd0-1e8c-4ef4-8ac2-9e00f486fadc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "c07230f0-1c67-47ee-b194-ea8e864d6d31", "name": "processInstId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "c25dd2e5-dc84-4f4f-ae0a-6019c984f9c4", "name": "communicationRecords", "title": "沟通记录", "type": "java", "classType": "CommunicationRecordDTO", "refEntityId": "a65c8bd2-5983-4ad9-af5a-ed9bcdcf1742", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "cef50f73-76ae-45f9-b109-28ef421cc4d5", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "d5d21500-3e60-4d66-9321-021558b0fbfc", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "eaf1189d-77b0-4e9b-a416-6e73a4321bba", "name": "isServiceEnded", "title": "当前服务是否已结束", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}, {"id": "fed481c7-7858-4a06-b4f8-dc7c450d5f3f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0c8fc7f0-6130-4604-b963-6931ca401bb8", "valid": true}]}