{"id": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "className": "com.open_care.dto.service_order.follow_up_link.FollowUpLinkAppointmentRecordDTO", "name": "FollowUpLinkAppointmentRecordDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "20841ec6-9b72-44a1-a18c-1c0647d0edc0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "29867fa9-ebb5-489f-b2a0-368230176eab", "name": "needAccompany", "title": "门诊就医是否需要陪同", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "3f82cf19-3ef7-42c5-92cd-13d39720d5af", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "44152a4b-d96a-4f74-91a9-d5a7f5c15db5", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "57798a41-0827-4c1c-a721-d406d9b05752", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "9f020241-9c37-421c-84bc-01eecef1b74e", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "a0df7e58-8db3-4d98-afb8-a238398a61ed", "name": "needAccompaniment", "title": "是否需要门诊就医陪同", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "a78fab7a-c46f-4877-a20e-19e354005bf0", "name": "serviceCharge", "title": "医事服务费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "a9631f20-3404-4e01-bed4-8af62bb6a45b", "name": "outpatientTreatmentDate", "title": "门诊就医日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "ab7f91b4-a6dd-4791-ad4a-3eeb315a9581", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "ac43fd77-c104-4d0d-981f-01c2769140b4", "name": "department", "title": "实际就诊科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "b957206a-016b-43e4-bd53-8d81da34d21c", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "c00ae316-4c02-4f11-9cdb-b30800845c84", "name": "hospital", "title": "实际就诊医院", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "c574e682-c0b9-47e9-86fa-6c46fadd57cb", "name": "outpatientTreatmentRemark", "title": "就医备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "cafb9cfb-a079-4abe-a34a-3646a265b8b2", "name": "needOutpatientTreatment", "title": "是否门诊就医", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "dc0becda-645c-405f-8fc0-515662e434a4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "e003319b-b3c6-4738-857b-e67e0a981dfd", "name": "doctor", "title": "实际就诊医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "e2bd8b4a-24c8-44ae-a5a0-3fe57228a0fc", "name": "accompanyRemark", "title": "陪同备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "e6001c7d-aff9-41bb-9b32-904295011063", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "就诊医生职称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "e7fb8ddd-bd41-4a50-bf68-bbd2a74aa102", "name": "registrationNumber", "title": "挂号单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}, {"id": "f869186d-8fce-4293-95a4-59d89d07b6a7", "name": "accompanyDate", "title": "陪同日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "33417fb2-534a-43e4-b7ef-f1f998c988ff", "valid": true}]}