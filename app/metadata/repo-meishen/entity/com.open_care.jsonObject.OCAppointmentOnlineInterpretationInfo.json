{"id": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "className": "com.open_care.jsonObject.OCAppointmentOnlineInterpretationInfo", "name": "OCAppointmentOnlineInterpretationInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "30965b7f-db36-46a2-b37f-6f2b586e893b", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "valid": true}, {"id": "37edb72a-9965-485e-8706-b10833d3f9f4", "name": "interpretationTime", "title": "预约解读时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "valid": true}, {"id": "4ca800c5-08b9-493e-9e7a-71b267cd5623", "name": "examDate", "title": "体检日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "valid": true}, {"id": "6612cdc2-ad04-4a2c-ab70-8a4b387781aa", "name": "isIntegrated", "title": "是否在融合体检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "valid": true}, {"id": "67c74343-9c9f-4f2b-863e-03314e617de2", "name": "isFiled", "title": "客户已建档", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "valid": true}, {"id": "b7e35ba2-c0f6-4fe1-b426-5adb5717f2ff", "name": "isCollected", "title": "是否已完成体检收集", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "valid": true}, {"id": "f85dde22-c27c-49a1-8ba8-d43090ca193c", "name": "fileNumber", "title": "客户建档号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "65ce4006-0c6d-4294-9f9f-d8f8e4ff137a", "valid": true}]}