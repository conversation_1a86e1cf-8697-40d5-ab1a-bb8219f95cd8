{"id": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "className": "com.open_care.event.time.OCAppointmentDateRange", "name": "OCAppointmentDateRange", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "062eda6b-5751-4c74-a877-932f0e6ea914", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "1073df69-42d0-473c-a06a-95fa43a9bc34", "name": "endTime", "title": "结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "15e5d04e-9fea-42dd-a728-29455c030f2e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "16b165b6-dd7a-4ecf-89ca-b6545eecc4f9", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "1b785d9f-83ac-421a-be12-b895f32f07d0", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "55efa30e-02cd-44da-a214-1ca1b2fef1d0", "name": "appointmentDate", "title": "预约日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "5b73f202-9b83-410d-aa1a-5478ddb31209", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "83dadc97-17d2-4828-9283-2fb116758421", "name": "beginTime", "title": "开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "869c48cd-1b38-420f-95ad-1ad85fc9f215", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "8ca5d775-a571-44b8-abbe-e8f8b01fd39c", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "a26bacdc-61a3-4cec-bcef-74800cd05be4", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "a5b9d558-2d1b-4981-bc3b-1eb6855a7a70", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "bee14b65-2738-4f23-9306-1b2a07f7c0ed", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "f598c55f-d471-4870-9878-2e755801d675", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}, {"id": "ffe7f404-049a-462a-af7a-9ab9856e1b6f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ac512cd5-8869-4d2e-85e5-e42db8d00d10", "valid": true}]}