{"id": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "className": "com.open_care.jsonObject.OCServiceProductAppointmentConfigJson", "name": "OCServiceProductAppointmentConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1953da4d-f407-4308-890b-bf321416b63d", "name": "ifHasWaitPeriod", "title": "是否有等待期", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}, {"id": "3827f479-a423-425d-892e-07e40ac5e456", "name": "isChangeable", "title": "支持改期", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}, {"id": "5829d197-a5d9-4c30-9213-938d8e243b10", "name": "rules", "title": "预约规则", "type": "java", "classType": "object", "refEntityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}, {"id": "59c0d0af-8e4f-4df5-9622-b0fc1076af28", "name": "isNeedSubscribe", "title": "需要预约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}, {"id": "61309ade-2fca-4f54-9f19-7bf2adb63194", "name": "waitPeriodUnit", "title": "等待期时长单位", "type": "java", "classType": "DateTimeUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8a1136265010b28b2895fece827b84ef0cfe9d70", "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}, {"id": "65bc3207-0718-421f-9373-0a73bde6a1fd", "name": "isCancelable", "title": "支持爽约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}, {"id": "94b94c6d-64e3-4c9c-a1a6-26978c8b48a4", "name": "wait<PERSON><PERSON>od<PERSON>ount", "title": "等待期时长", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}, {"id": "f3acdf20-6fe6-4278-942b-ae10cdc59300", "name": "verificationMethod", "title": "核销方式", "type": "java", "classType": "VerificationMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "01877fabf6c1911109f6bb2780219bdb88bc0447", "entityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "valid": true}]}