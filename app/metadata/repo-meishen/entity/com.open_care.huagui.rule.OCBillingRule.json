{"id": "7d5c2500-415a-418a-b9e9-412456d09c80", "className": "com.open_care.huagui.rule.OCBillingRule", "name": "OCBillingRule", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "113262c2-ebd5-4d66-85bc-4f8b8ed13b83", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "1cd68bb3-14ab-49c1-b8b8-4114f0d607ca", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "1f999c7d-48f8-4f16-8408-cdc3a87ebb55", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "2bc5e387-5860-48b1-b453-57f2048cf235", "name": "ruleType", "title": "规则类型", "type": "java", "classType": "RuleTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8d22f76ebdf5256c56b8bb2ae1cff8eb2eebb91a", "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "32885dd2-547c-4945-a78f-5306bc931ffc", "name": "executeAction", "title": "执行动作", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "44c15455-13fa-455e-b8d0-c2682a4536d9", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "49f17fb8-3183-4f74-aed7-44b0df14e135", "name": "matchRule", "title": "匹配规则", "type": "java", "classType": "object", "refEntityId": "3f9a876f-088f-441a-8bbd-0a8bfd69ba08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "70aaf0cd-bbc5-4e76-822b-6a7f65843696", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "9b0be63a-3f47-4e5b-805c-4ed4c2f4c49b", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "a6474de2-9ce5-4235-bede-738b017d5841", "name": "ruleDescription", "title": "规则描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "ae15549c-6bc9-4898-8308-129937916efc", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "ba8d3b82-4d7c-42b3-89a7-01fd23e38a21", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "bfc2dd8a-8c06-47a1-9461-b73bfbda7fd2", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "cef3a311-d740-4d78-8c85-a69217114d2a", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "d1194cbf-2f79-4de2-bbfa-cef2131dc100", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "d3a94f66-a7c7-4082-8c45-f6a2e36fa2a0", "name": "expiryTime", "title": "失效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "dc776b5b-b19e-4f09-998c-54ceeea36756", "name": "effectiveTime", "title": "生效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "ed88ada8-e106-41db-8117-dc5686c989b9", "name": "ruleName", "title": "规则名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}, {"id": "f6912ec3-4fcc-4010-928c-cc05a7d94fa2", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7d5c2500-415a-418a-b9e9-412456d09c80", "valid": true}]}