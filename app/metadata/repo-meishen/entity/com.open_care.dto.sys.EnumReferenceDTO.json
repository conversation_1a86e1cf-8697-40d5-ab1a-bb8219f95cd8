{"id": "796891b0-df59-4d42-afe3-e1351d2ec5cc", "className": "com.open_care.dto.sys.EnumReferenceDTO", "name": "EnumReferenceDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "4ae3cde8-0517-4ad2-aaa6-953f5fc4e4b1", "name": "staticFunc", "title": "静态无参数方法", "type": "java", "classType": "EnumProperty", "refEntityId": "5c109253-d819-479b-848b-75cbb843f21d", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "796891b0-df59-4d42-afe3-e1351d2ec5cc", "valid": true}, {"id": "4c7d0018-356c-468b-8152-6eddb87339f8", "name": "staticProperties", "title": "静态属性", "type": "java", "classType": "EnumProperty", "refEntityId": "5c109253-d819-479b-848b-75cbb843f21d", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "796891b0-df59-4d42-afe3-e1351d2ec5cc", "valid": true}, {"id": "66c03234-bc37-4c13-8640-a7db4d824f2b", "name": "refList", "title": "参考列表", "type": "java", "classType": "EnumReferenceItemDTO", "refEntityId": "018203f8-7b58-4d95-b372-d31b5ba17a51", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "796891b0-df59-4d42-afe3-e1351d2ec5cc", "valid": true}, {"id": "75ed6bed-29ce-4826-81e5-5ba7e9a68558", "name": "className", "title": "className", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "796891b0-df59-4d42-afe3-e1351d2ec5cc", "valid": true}]}