{"id": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "className": "com.open_care.event.service_order.OCAppointmentServiceOrderRecipient", "name": "OCAppointmentServiceOrderRecipient", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0056414e-0739-42dc-8585-15f97e8eddb8", "name": "contraindications", "title": "已告知禁忌项", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "0d2ba71c-8839-47a8-8369-91202b67de65", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "1a4411c4-815e-42cf-8a40-390623bab7a5", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "2b22d658-ba95-4000-a1fa-f04c063ecda0", "name": "amount", "title": "付费金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "3386880f-54bb-4ecf-863a-685de118c384", "name": "appeals", "title": "客户诉求", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "449ca31b-0d96-4683-bca7-14a0286e14d5", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "48d896a2-3b97-40bc-a2ca-4976ec42232d", "name": "actualDateForHospitalArrival", "title": "实际到院日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "4a692823-7ec0-4391-8b70-d0ec1d3b730c", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "4ab868a5-ba2e-40fb-b146-b89579c09a51", "name": "customer", "title": "实际被服务人", "type": "java", "classType": "object", "refEntityId": "b011f8ee-475f-46b1-b3a5-e18c99ca01b7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "505d039c-c0e8-4efb-b963-3bff3bf63db8", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "65a1ddcd-285c-4412-8e55-fdd8c44658ed", "name": "appointmentTimeRangeForHospitalArrival", "title": "预约到院时段", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "6be2514b-df62-4d9a-a4d8-34e0fbec040e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "7014c335-cb1e-475c-ac9f-45610851a88c", "name": "description", "title": "病情描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "7ea39831-59f7-4052-a5a8-a49f5962d1d3", "name": "serviceIntention", "title": "服务意向", "type": "java", "classType": "ServiceIntentionEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "262003322f993d4e6e8f0e6121fb60c3fe098658", "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "90543f0d-9831-41a4-8455-4bc02d45e37c", "name": "appointmentDateForHospitalArrival", "title": "预约到院日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "a300fe18-ed3a-4a59-b083-47b23ca0494d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "a4f7591d-ba5a-4e93-836f-2fcab4d0bb29", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "b28cec36-9d6e-4b32-8aaa-d95ac92038c6", "name": "weiFangLocal", "title": "潍坊本地客户", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "b5f3ac31-d141-4651-a302-35de4fd0bedc", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "bd655cb7-01fb-4f79-85d3-1bbb2d6f62a1", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "c29b6e59-fc4c-49f1-9a8f-b456eeee1287", "name": "age", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "d5592be6-bd22-4202-bcbb-60fd5acd6138", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "de443ae0-2f5d-4d5e-b017-54b684a2b0ea", "name": "isPayCompleted", "title": "已经完成付费", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "edefe6a7-a86a-431c-b2be-82a6c6e44326", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "f1e3d368-a917-4735-b250-53d1f49a67f5", "name": "date", "title": "付费日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}, {"id": "fdfa4bfb-0af2-481b-9274-6cea781a0ae8", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "valid": true}]}