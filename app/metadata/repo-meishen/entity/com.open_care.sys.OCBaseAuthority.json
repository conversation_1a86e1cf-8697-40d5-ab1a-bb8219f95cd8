{"id": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "className": "com.open_care.sys.OCBaseAuthority", "name": "OCBaseAuthority", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0d98956d-d33e-466b-bbb5-0f7cda5cd3f8", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "23420f03-6e6e-491d-8666-e32f5d981fc0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "5e8c55da-a960-4930-9c11-e0ecc179d6db", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "7e0d4f0b-7cdc-4dbe-bda4-e23a65fc1f9d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "7e61a69d-8d64-47cf-bee1-66761acdc3ba", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "97cb3d8f-1193-423b-8d60-00c1b118a52a", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "bcf09b9b-85e3-49a5-8a28-3a7ef493846e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "c5ef71b8-1380-43ed-8578-74e2b8da4cd4", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "d0085d3e-44fe-48af-8043-db3fe4c81cfd", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "d1a7f853-6033-4614-8016-af1d953f9726", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "d4061574-d8ba-4f0a-bcd1-3bf18a24e74d", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}, {"id": "e2ec6383-f0c4-4cc6-b762-7d32ee8ab0c6", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d67197b-aeb3-4a7b-b88a-b0e0a6546702", "valid": true}]}