{"id": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "className": "com.open_care.medicalMicro.medical.OCBaseMedicalDeviceType", "name": "OCBaseMedicalDeviceType", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "医疗器械分类", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "04566d22-c1e4-417b-ab1c-bedf24ed2b6b", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "137e4c55-c932-4481-8703-fb5ea7ced01a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "3187bb85-d12d-41bb-8a17-bdd51f209160", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "3b91e181-a8d6-4d06-8b73-9c53632fbf1e", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "4256776a-ecf4-41b3-a137-76f15b362a4c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "569d8ec7-b81b-40a4-96ff-6b4bcab7883a", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "5e297388-40d4-4634-b4f8-91b3ab8627dd", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "622bb3a1-dae8-428d-a960-08df6d50b527", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "69f85ac4-f926-4de9-8efe-538d5ce6a30a", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "73968c91-d5df-4fa6-b7f1-4387308659c9", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "73d04ddc-55cc-498e-9a76-ee14946b8ae6", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "87d4b983-cd82-46e4-b440-c6c9fcd70b67", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "958412f1-ca75-46b8-bf86-c58649f0e39b", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "b25bc047-632b-4008-be69-8dd16f92fcac", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "b7bc456d-737c-4df6-a05f-840a0e2e5ea4", "name": "parentMedicalDeviceType", "title": "父级分类", "type": "java", "classType": "object", "refEntityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "cdfe7576-2080-4d73-ae9e-a1fea79059d8", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "d6d68ecb-967e-40ed-b72b-74a3344e0e42", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "dccaaf6e-9818-4127-b1da-e730b3e868e6", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "ecf211c2-63fc-480a-93fc-f676710bd4ea", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "fb8028a9-1f4b-4b9c-ac8e-b771f4631e35", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}, {"id": "febe25dd-467b-4896-9f50-58966f45f14c", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "valid": true}]}