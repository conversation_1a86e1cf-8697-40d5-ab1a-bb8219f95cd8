{"id": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "className": "com.open_care.product.entity.OCSportProduct", "name": "OCSportProduct", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00e1d925-277a-4b05-a5fb-2744e6d037f4", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "0dcdf675-28a4-4056-add6-80f8630583e3", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "1316464a-cf58-4b24-b699-08fcf21fb83b", "name": "productTypeEnum", "title": "产品类别", "type": "java", "classType": "ProductTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a412e1fa5e9b5c0522b9b505403365f9953fdd16", "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "1551fdd4-7a39-467f-89e8-e19f7225ad35", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "1cbd4a43-5d14-4e0f-b1b9-0d1449058718", "name": "phonetic", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "1f3dcc09-594f-4c80-98b3-cc72d33609cb", "name": "serviceDuration", "title": "服务时长", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "216f6d5d-3ad7-4394-8ca8-d8fdd16741c7", "name": "appointmentPageInfo", "title": "预约页面信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "240fe4b0-5622-4be5-9cb7-fe41ded07ebd", "name": "invoicePrintCategory", "type": "java", "classType": "object", "refEntityId": "dcc64fe7-6a12-44a3-b254-7add8cbc9e55", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "2aacbf19-19ef-481d-a2d9-d64b6724bdaf", "name": "departments", "title": "检查科室", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "368d82bf-9462-4268-94df-c9a5bc47aecc", "name": "englishName", "title": "英文名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "378e609e-8364-43ae-930c-67f89a4d603a", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCProductAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b46b46e30ec540dffb8a1d2910673c202c96e0a2", "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "3b2ae8aa-0cda-45f3-9861-ca7b8cb93a8e", "name": "tags", "title": "标签", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "3ea071c7-34dc-4c13-a7f1-96e40d2795fb", "name": "productReportUnit", "title": "所属报告单元", "type": "java", "classType": "object", "refEntityId": "1d0847f3-9bf4-43eb-a8b9-17f2ae85c6cd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "41d2b239-739b-4173-b8ef-e04bd7844a66", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "4547d8af-a75b-400b-8ec0-40bc92602961", "name": "processTemplateKey", "title": "流程模板", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "473b061e-41b2-4840-9901-aa30868e3991", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "510de2bf-7386-4c87-8364-95ea47b11bf9", "name": "showOnGuide", "title": "在导检单上显示", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "514b3400-9a7f-41f8-9b83-5bbf8b066938", "name": "seqno", "title": "产品行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "5b046ee8-e297-4cba-989c-1674102ad2e3", "name": "serviceResources", "title": "服务资源", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "60868cdf-f0dd-410a-aa3c-40027d89d6b8", "name": "productSop", "title": "产品SOP", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "656ef8b0-abb1-4998-b4b5-e0ccd16a129b", "name": "description", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "6ede8894-ec8f-428b-bd7c-853fd77689d6", "name": "serviceInfo", "title": "服务信息", "type": "java", "classType": "object", "refEntityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "72a61862-3568-468a-86b2-0ebbe8a66d0b", "name": "showWorkbench", "title": "管家工作台显示", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "772e7f62-12e5-4985-8441-9b0c7b5f932c", "name": "productIndicatorCategory", "title": "所属健康指示灯类别", "type": "java", "classType": "object", "refEntityId": "d5e1113b-eb2e-479a-af67-1f111379b258", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "7940a6d1-cdde-44de-a7c5-2b52a5e3d51f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "7aac2a9f-93fb-46e2-a27d-ac827c1e4da8", "name": "otherSysCode", "title": "其他系统编码", "type": "java", "classType": "object", "refEntityId": "84b0cdeb-49ef-4965-afa0-3825b0a947b6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "7b3fb198-1209-4af3-ba73-ee35ce83d31c", "name": "appointmentInfo", "title": "预约信息", "type": "java", "classType": "object", "refEntityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "7c825557-3c07-44db-b9a9-b32f474afdc5", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "81cf5849-89e9-403c-a852-ddd638c805f7", "name": "brand", "type": "java", "classType": "object", "refEntityId": "f9317ca8-02a7-4f69-8069-f4f0e61e6ef2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "8312ae46-9d03-4f6e-ad5e-f16bbb94b6a2", "name": "<PERSON><PERSON><PERSON>", "title": "产品负责人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "83979c78-9182-4139-bfcc-16e3f67cdc6e", "name": "fromDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "933de89b-1e6a-4c56-8587-eed45bb0a5a5", "name": "mallInfo", "title": "商城信息", "type": "java", "classType": "object", "refEntityId": "ac78171b-b49e-4a2a-9739-a7deb58152d8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "9475a4d1-be69-4aa0-851b-520d8861f515", "name": "meal", "title": "就餐项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "96630ce4-476a-43f1-9518-e71a2c48538f", "name": "productNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "99af4de0-2525-495f-84ed-0ca5a467c15b", "name": "quantity", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "9d6708c3-e4f9-4d1d-ad1a-a14319bdd813", "name": "separateReport", "title": "单独出具报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "a69dfcb5-bb30-4968-b4e2-d4ae39ddc3fe", "name": "manager", "title": "产品负责人id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "b122783c-b7b4-440f-aa73-b101015707aa", "name": "attributeSets", "type": "java", "classType": "object", "refEntityId": "41ca4d69-933e-416d-b1ec-5f500a801a7a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "b1298a2e-228e-4fb6-86e8-ab0cb3c2a1d4", "name": "productType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "b2fd20d2-0b10-4409-919d-500971f23bcc", "name": "preStageId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "b376b12c-209b-4943-8c52-33678e78bd46", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "bb9b5a5a-892a-44ab-b74b-75116c3de349", "name": "productCategory", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "bde5bd5d-4596-42d2-85ff-8cc64ccf71aa", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "bec91141-ad40-4fe9-a731-d2b8ccb4e7da", "name": "productGuideGroup", "title": "产品导检单分组", "type": "java", "classType": "object", "refEntityId": "cdcc0a88-0f8c-dda6-dd86-081ebaa2cece", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "bf8a2fcc-dec2-4f85-bc1a-cad3a7901720", "name": "comments", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "c1093b34-9dd1-4b7b-9971-3ddf97ddf599", "name": "printCategoryFlag", "title": "打印时类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f9fc0b3b-aaec-4602-808a-03c6dced4688", "style": "Input", "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"printCategoryFlag": {"$id": "c1093b34-9dd1-4b7b-9971-3ddf97ddf599", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "打印时类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c484eafc-80b0-4a84-96be-9bd290f3beb9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "c830f354-b7e1-46fd-aa61-35a0296c603a", "name": "processDefinitionKey", "title": "流程定义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "ca9e8651-b90e-4f2e-bed5-6cf6d7d24213", "name": "abbreviation", "title": "英文简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "caab71b7-8055-4c08-bae1-8a709db27b58", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "cb1f02db-78a7-4852-b1d8-701a296bfca0", "name": "timeoutInfo", "title": "超时提醒", "type": "java", "classType": "object", "refEntityId": "bcdf5a27-ff91-4b72-8618-506ab447bfb8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "cdffcb1b-a859-4bb9-acbb-939f8c6a8b34", "name": "mealType", "title": "餐前餐后项目", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "cea8b233-25da-4d89-a84d-955e39b8142f", "name": "thruDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "d853af67-6ae2-4b09-8d27-89ce476c1429", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "dae9ab03-eca8-4834-bfb3-45f914c4df23", "name": "dispatchAndAcceptanceRule", "title": "供应商接派单规则", "type": "java", "classType": "object", "refEntityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "daf1a814-0f7c-47f2-9118-9339ef69bd3d", "name": "productAttributes", "type": "java", "classType": "object", "refEntityId": "05ad013f-9028-48e7-b064-e72934c69b35", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "e60605df-22aa-4ae4-bae3-8d6af96ef754", "name": "canSell", "title": "是否可销售", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "e9931f39-99c9-46d7-b96f-fca807981800", "name": "barcode", "title": "是否配置条码", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "ede8f74d-9e34-41e5-a746-ce48da7a207b", "name": "saleInfo", "title": "销售信息", "type": "java", "classType": "object", "refEntityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "ef0c3090-75fd-4ec2-a84e-9dc92e4258de", "name": "status", "type": "java", "classType": "OCProductStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "7f4e41d9bf4d856872e61acab81b030d08583b05", "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "f59556ec-de90-4ffb-a41a-d1ced9c92d15", "name": "productComposition", "title": "产品组合", "type": "java", "classType": "OCProductCompositionEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "fb77728c82b4976215813d01658d30647905b917", "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "f6cb1824-173a-4cb4-9abf-ef74f19e8f4d", "name": "productUpOrDownEnum", "title": "上下架状态", "type": "java", "classType": "OCProductUpOrDownEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "5783903a3025d203419af1adb17c5b58e2779555", "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "fe53e250-a6ee-4e6f-b4e6-96fd84b73447", "name": "disabled", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}, {"id": "fff56c18-8cab-4926-b477-02ff5a7ce6ac", "name": "productName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad7059b2-626b-465f-a947-0b0ba7b5e61b", "valid": true}]}