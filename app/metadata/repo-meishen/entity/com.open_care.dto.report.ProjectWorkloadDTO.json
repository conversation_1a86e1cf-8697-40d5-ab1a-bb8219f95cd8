{"id": "b69c7988-431d-465b-b29a-442668ac5d07", "className": "com.open_care.dto.report.ProjectWorkloadDTO", "name": "ProjectWorkloadDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "13e7231b-e24c-433c-9652-e9332a3facd2", "name": "entryStartTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "1fb74b24-b868-45c8-bcd8-3639a662a6c9", "name": "entryName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "2e351a58-73e2-4a89-b11f-48e9d4a222d3", "name": "departmentName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "3a8bd617-2210-46ed-995a-23804493872a", "name": "productId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "7abb6ebf-62f4-46ec-95b4-1c2d39a7142d", "name": "entryEndTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "8b43bc8c-7dba-442a-a634-fd63c78b7ee6", "name": "countNum", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "9f86658c-8287-48a2-a2f0-c9bfb8260341", "name": "entryDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "a7641440-ea90-4b7a-9f8c-480ce0cb1ad1", "name": "examProductName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "d5e384c0-e5e9-4220-9c81-58fae491fe51", "name": "departmentId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}, {"id": "ebd26f2c-9814-4aaa-b2e8-b7e572225e37", "name": "entryId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b69c7988-431d-465b-b29a-442668ac5d07", "valid": true}]}