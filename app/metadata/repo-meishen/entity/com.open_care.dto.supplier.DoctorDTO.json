{"id": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "className": "com.open_care.dto.supplier.DoctorDTO", "name": "DoctorDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "14cbfdee-4ad4-44a7-ac2b-7520c4ea8585", "name": "expertIn", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "27245d85-ea1d-4c01-af13-285684ae891c", "name": "hospitalLevel", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "27f88f07-af12-4811-abb6-87313aa6e4cc", "name": "productId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "2d50ca88-9ce3-4216-97f4-af759cbf156e", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "30d1f999-3374-43e7-ac29-65e9533922c2", "name": "<PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "3e42ffef-485a-4b16-bf12-21c003a4a424", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "52a72741-087c-4126-85ec-bfb4c21f53ba", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "58ed1daa-d102-459d-8b23-76046f100694", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "5b5c246e-276e-469d-ae93-5a083499fa18", "name": "department", "title": "科室", "type": "java", "classType": "object", "refEntityId": "4c1aa0a3-dbe3-4d77-9ce3-48749bae6109", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "63d29f13-5572-4431-8bb2-5c5f1dd86cd5", "name": "supplierProductId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "7b194977-731c-45d4-8caa-23e61f45c55a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "8f153a40-3bbf-419b-b2ee-8bceb1f94a6d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "90f6ea56-b735-4b01-958a-7bfd9ac72279", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "947ba22f-46f7-4aef-8752-598a463d3385", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "95f62c8f-c496-469e-8237-e5eb91be6ccd", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "9b079522-7ae9-46b0-83e8-27fee80e05a7", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "9bd0360d-2a84-4007-8ce2-8e5e5c9fcde4", "name": "supplierId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "a32a7899-451c-49a6-9d4a-4ed42ef6bbee", "name": "departments", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "b1f53312-7924-4f05-bf09-000bdedfd673", "name": "province", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "c17487aa-92d2-4a07-b986-07085367408d", "name": "contractId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "c3d084be-c0e8-4918-a087-e61133ba043a", "name": "surgeryTime", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "cb2a5cf1-a08b-4dfe-b8d4-40161fdac5e5", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "d544bad2-f092-4cb5-a5f0-976f37723aec", "name": "city", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "d965ce30-c70c-45b4-955c-8e566ac76dfa", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "e1ee8d32-7365-4a80-bf1a-4934af2c66b2", "name": "hospital", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "e436d73d-d608-4147-9115-500b78efcc1c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}, {"id": "f6bdab4c-183b-44bf-a1b3-d40d2b9f0444", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba9fb9e4-2ee4-425d-9836-d5507eb9f7c0", "valid": true}]}