{"id": "e4f7c416-1351-458d-bfa0-68db243078f7", "className": "com.open_care.dto.service_order.follow_up_link.FollowUpPlanDTO", "name": "FollowUpPlanDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0d74ec83-f655-4ece-bebb-672214ae60d3", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "17d890f2-d1ac-4e33-b701-79404a601e0a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "4be680cf-aa4f-4bc6-a46c-1c0f78b27089", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "5ce9423a-90ea-4a6b-ba21-b4a72c011b35", "name": "followUpDate", "title": "专家随访日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "7c421ba4-5954-446e-b58d-6af3cfe2c694", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "a45b632d-2441-4327-a374-b73f8bd4d6b3", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "a6281e0e-34a4-4c54-9f1a-db985d7db3ae", "name": "expertName", "title": "专家名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "b4f854fe-512f-4d5c-a8eb-75c3547357b5", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "e3af195f-7632-4207-9fc5-b45b01c4ac80", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "f898ad3b-0424-424f-9f66-75cec315f65e", "name": "jobTitle", "title": "专家职称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}, {"id": "f8b69e94-ec61-4e04-9c76-da0fbc809293", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e4f7c416-1351-458d-bfa0-68db243078f7", "valid": true}]}