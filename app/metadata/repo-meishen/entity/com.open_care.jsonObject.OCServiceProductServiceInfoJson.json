{"id": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "className": "com.open_care.jsonObject.OCServiceProductServiceInfoJson", "name": "OCServiceProductServiceInfoJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0ea42e4c-d15d-4c50-bc9a-f3c8a30c42dc", "name": "userRange", "title": "使用者范围", "type": "java", "classType": "ServiceProductUseRangeEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "3b0b6927f2dd84abd81f77c11edef70d09551d2f", "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "41ef3c62-eeb9-4066-8cfa-aa7095e37dd0", "name": "url", "title": "url 地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "620ecd52-06b7-4642-a117-49a250bafd04", "name": "serviceRange", "title": "服务范围", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "62a88db2-cf86-48d9-9b50-f60108a98cab", "name": "ifOwnUseOnly", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "62be29cf-e8d5-40b3-a4dd-a7842640fca7", "name": "serviceProcess", "title": "服务流程", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "67d26d6c-3780-4302-a3c9-8ccea8441999", "name": "serviceValidityCount", "title": "服务有效期", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "a08dda93-6980-4b37-95a1-0be468ac2c3b", "name": "urlDescription", "title": "URL 功能说明", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "ad32b10b-9637-45ba-8bb6-e5b092b4a9e3", "name": "intendedFor", "title": "适用人群", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "ba30716e-c924-4c84-8330-f82ee795f2e5", "name": "activeWaitForCount", "title": "激活等待期", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "be9f1889-058b-490d-af6e-f0586173f8ad", "name": "serviceValidityUnit", "title": "服务有效期单位", "type": "java", "classType": "DateTimeUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8a1136265010b28b2895fece827b84ef0cfe9d70", "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "bf3a650e-8998-434f-b6ad-6e58c6936d58", "name": "qrCode", "title": "服务人员二维码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "c088bd62-6260-4929-a4de-28135d98e4ed", "name": "activationMethod", "title": "激活方式", "type": "java", "classType": "ActivationMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f675ae4684b752d2dcc8204273b6c7cf010d979f", "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "cc67a63f-2b8c-481c-b099-153953519ff3", "name": "needFollowUp", "title": "是否需要回访", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "ccc176e9-1fb0-4943-8d8e-86ea9697d075", "name": "billingUnit", "title": "计费单位", "type": "java", "classType": "BillingUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "14c1896abf052f7fd8fe2bf54d0bd2e1b7523ff5", "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "f8602f91-5308-4fc6-8702-ea4cc570c03d", "name": "qrCodeIsValid", "title": "服务人员二维码是否有效", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}, {"id": "f91cb0d1-e5eb-4eb1-b23b-48c6e44f4ee4", "name": "activeWaitForUnit", "title": "激活等待期单位", "type": "java", "classType": "DateTimeUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8a1136265010b28b2895fece827b84ef0cfe9d70", "entityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "valid": true}]}