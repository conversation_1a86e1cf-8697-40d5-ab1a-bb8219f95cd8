{"id": "af4932cb-1480-4eba-9c81-8e8ad808a5ce", "className": "com.open_care.jsonObject.OCSamplingRecord", "name": "OCSamplingRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "13355929-4c92-43b0-a170-9bb061c92621", "name": "followUpInfo", "title": "回访信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "af4932cb-1480-4eba-9c81-8e8ad808a5ce", "valid": true}, {"id": "46c36ed1-b12e-4e2b-944d-9807ba88fc2b", "name": "paymentDate", "title": "付费日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "af4932cb-1480-4eba-9c81-8e8ad808a5ce", "valid": true}, {"id": "69b7df8a-8f73-48e4-8e62-911119af15b4", "name": "paymentAmount", "title": "付费金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "af4932cb-1480-4eba-9c81-8e8ad808a5ce", "valid": true}, {"id": "9e01a7c5-e2f3-4aae-9831-46d0ef04a715", "name": "samplingCompletionDate", "title": "采样完成日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "af4932cb-1480-4eba-9c81-8e8ad808a5ce", "valid": true}]}