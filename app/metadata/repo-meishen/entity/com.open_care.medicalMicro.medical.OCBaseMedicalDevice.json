{"id": "6d8cec80-d8fa-4272-b204-a4077a939133", "className": "com.open_care.medicalMicro.medical.OCBaseMedicalDevice", "name": "OCBaseMedicalDevice", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "医疗器械", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "25a3130f-b26e-4914-89b9-36f6811603ce", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "31352f20-183c-4622-a4f1-30f769a85d1e", "name": "medicalDeviceType", "title": "医疗器械分类", "type": "java", "classType": "object", "refEntityId": "e403f7a5-1484-4bb1-9832-93b1dac49bbe", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "32c97c08-800a-4931-993d-bd4cae05eb44", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "4c94c958-9e22-4a01-bfaf-6219b888b08f", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "4ccee047-8cb7-4a52-9eac-7562c3616cda", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "561e81ac-a7a4-444e-9ad0-ec4530ad1d45", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "5c875479-2ec1-455e-8ccb-7cd1ecce5efe", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "69215b59-0cf3-43ac-98b0-0b49b407ea65", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "7b8c20fc-54f0-4772-a918-0c2f5dffe594", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "839480b1-9d84-486d-9010-965464011d3d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "8f0a4a6f-80f4-4286-bfbb-35d340f13fc1", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "9af8cddb-fce0-4aff-94f9-77391ae89294", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "a310a7e5-8cde-43f4-8a75-29a718d6b334", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "a4e9e549-49bd-4438-8ec7-7d7f4c49deb6", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "b0f38a55-134e-408a-a2da-c5de90def99c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "b5a2fcd1-a0c1-4084-997f-2616c75f90b0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "c1da873f-3d61-4503-9b11-0529da98bc02", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "e6bb649b-e228-4d4b-8324-ff26b19f1f10", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "e9898810-3c94-4cc7-95d7-d01da5484a2c", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "e9cc9a97-3204-4eff-a01d-e715f850b8a5", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}, {"id": "f9fc48df-8106-4f98-8721-a38d0915bd37", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6d8cec80-d8fa-4272-b204-a4077a939133", "valid": true}]}