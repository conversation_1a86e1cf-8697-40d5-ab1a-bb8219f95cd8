{"id": "f4e781db-447c-4c77-a718-f82296385de2", "className": "com.open_care.sys.OCDiscountSetting", "name": "OCDiscountSetting", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "123bdc64-8474-45d1-b97f-93b4c7d4449d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "14c4f151-80d3-4d15-9206-a0879e380ca2", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "213fa920-08e4-4cfe-87dc-99e640d3c082", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "55f9b15a-85d0-4491-80fb-c6f07d9a0bbc", "name": "userNames", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "83809e5d-8bf8-42a3-bc78-3b40706ab4f3", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "83be1e5e-dcd1-43bd-86be-eb0788ace861", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "930da0bf-d9ea-46ab-adad-0115a120650e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "a87b0b05-83a0-4541-a45a-a1164ddb68fb", "name": "userIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "683d38a2-fee3-4d3f-8af5-2d7912aa1585", "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "be8d70b6-07e4-4f59-9a34-c13fb4b83198", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "c7c5c711-d376-4835-9ec8-06259a3914a7", "name": "roleIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "3c7e0003-4d5e-4e73-a79d-abd40d00249b", "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "c968e1b2-52d2-4171-9ce4-3b5b944a9c5f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "cb4a8462-410a-4d58-bcd6-91aaf95d478b", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "d524c598-368f-48f4-9336-d0d85f472211", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "e1245d5c-e190-4992-a674-6dd4064c10c5", "name": "upperDiscountRate", "title": "折扣上限", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["upperDiscountRate"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"upperDiscountRate": {"$id": "e1245d5c-e190-4992-a674-6dd4064c10c5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "折扣上限", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e8a8df26-f0ed-41a6-bbc6-96296a4d9502", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}, {"id": "fe11a3e2-2ff6-4780-9654-8c31334a4fc6", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f4e781db-447c-4c77-a718-f82296385de2", "valid": true}]}