{"id": "da1d4d52-38b7-4dbc-a58c-89b69c8086eb", "className": "com.open_care.dto.product.work_bench.WorkBenchProductDTO", "name": "WorkBenchProductDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "006ca185-851d-4da6-bd4d-3fadac6856fb", "name": "_rowid", "title": "_rowid 前端需要", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "da1d4d52-38b7-4dbc-a58c-89b69c8086eb", "valid": true}, {"id": "66c285fb-7082-4e3d-b33c-0bbf0adc9117", "name": "productId", "title": "产品Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "da1d4d52-38b7-4dbc-a58c-89b69c8086eb", "valid": true}, {"id": "ea303c13-e1ba-436a-a6fb-2bb86f360f48", "name": "productName", "title": "产品名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "da1d4d52-38b7-4dbc-a58c-89b69c8086eb", "valid": true}]}