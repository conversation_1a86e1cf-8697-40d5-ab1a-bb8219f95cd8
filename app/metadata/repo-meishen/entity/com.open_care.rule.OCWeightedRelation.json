{"id": "08498e29-6be1-4203-af22-95690901f6fc", "className": "com.open_care.rule.OCWeightedRelation", "name": "OCWeightedRelation", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1230a1d0-acce-433c-9a12-c4b7da3137bc", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "08498e29-6be1-4203-af22-95690901f6fc", "valid": true}, {"id": "2998b8a8-7778-454b-8b70-9740037f3510", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "08498e29-6be1-4203-af22-95690901f6fc", "valid": true}, {"id": "55049d9d-3096-4beb-a944-1bdd19ab8436", "name": "weight", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "08498e29-6be1-4203-af22-95690901f6fc", "valid": true}, {"id": "df0b1cea-0c46-4282-aa0b-a1824734706a", "name": "questionId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "08498e29-6be1-4203-af22-95690901f6fc", "valid": true}]}