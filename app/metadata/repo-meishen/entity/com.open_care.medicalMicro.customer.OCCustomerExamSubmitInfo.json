{"id": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "className": "com.open_care.medicalMicro.customer.OCCustomerExamSubmitInfo", "name": "OCCustomerExamSubmitInfo", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户医疗基础项目送检信息", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1c77ea23-eae1-4a28-b2fd-f5fadf78b12c", "name": "examMethod", "title": "检测方法", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "23cfaa11-63a0-409e-a232-0d821e8d17c0", "name": "submitOrg", "title": "送检机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "df7798ca-4c88-4b0f-9fb8-f4092ccff1d4", "style": "Select", "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"submitOrg": {"$id": "23cfaa11-63a0-409e-a232-0d821e8d17c0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "送检机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2a1d460b-f5f7-45d5-aaa5-76496eac39d9", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "30db5f48-d138-4df3-b4ad-247a9f148398", "name": "auditor", "title": "检测机构审核者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "3b3ad987-3910-4ea6-ad3a-dad34971656e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "3c54ee61-b848-4d4a-bf08-ebb3d05c29ed", "name": "submitOrgPrintDataTime", "title": "检测机构打印时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "439ba41f-d076-4b88-b1a0-eaa7e8031551", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "46993fb0-cf18-4cf7-aaad-938439872d0d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "52e8ca81-48f4-4aa0-b46c-82675cc897b2", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "547915ff-1a88-4f9f-a46b-fbed1cffcd61", "name": "receiveDataOperator", "title": "接收结果操作者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "59cb124c-a72f-4e50-aebe-3093e79ff607", "name": "entry", "title": "检测机构录入者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "5e60db4b-688e-41cc-a42d-3c320d890be6", "name": "entryName", "title": "检测机构录入者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "6004857e-455b-42f3-a174-19b5eb2f7cbb", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "6bde4e72-26b3-450a-94ed-a267bf18fb32", "name": "submitOrgAuditDataTime", "title": "检测机构审核时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "733aa2f1-ec1a-435f-8634-a2201a4f1533", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "747d6d95-ed97-4c9a-8d7e-1d95d5a3e2f3", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "7bec576d-1f59-4df7-a144-2484703b277f", "name": "barCode", "title": "条码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "7c57efbc-9250-437a-b3cd-dd2c1b8779cf", "name": "operationType", "title": "操作类型", "type": "java", "classType": "object", "refEntityId": "63597756-e3a4-402c-9523-4759b8eb9cf9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "85b5cb9c-e796-4cf1-a6c1-d903dbb3cf85", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "997dd217-1731-47cb-afea-8111bfd0a36f", "name": "examEquipment", "title": "检测设备", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "a06111e5-402b-4aa9-b156-a15552085a65", "name": "operatorName", "title": "操作者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "a665a400-af2e-415d-9795-6827225b5e73", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "b0760a45-2814-4dde-9a68-7c50dfe687be", "name": "customerExamItem", "type": "java", "classType": "object", "refEntityId": "12f02542-0c2a-4b9c-8ec4-d26beb2f4b79", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "b6365437-6884-4fc3-83a0-a297f22f4a25", "name": "receiveDataOperatorName", "title": "接收结果操作者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "b8999257-f8ca-4966-a0ab-3f054726588c", "name": "operated", "title": "已操作", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "bb097890-b4c6-44ef-a377-8ba643c4313d", "name": "receiveDataTime", "title": "接收结果时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "ccfb2229-f043-4c9b-9ebf-0683da3752d6", "name": "auditorName", "title": "检测机构审核者姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "ce748910-463b-41c8-b70a-04b05a8b654f", "name": "operator", "title": "操作者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "d38758dd-c41d-49b5-88e1-36079e063136", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "d5dc452d-91d7-4642-9122-aaea28f9bed1", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "e961009a-fd9c-4492-9569-5324e0112b4a", "name": "submitOrgEntryDataTime", "title": "检测机构录入结果时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}, {"id": "edb3c9ab-bc46-480b-b0e0-1315325cb81a", "name": "operatingTime", "title": "操作时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0ab7f3e-d7c3-4f30-91e5-6413db5ab83e", "valid": true}]}