{"id": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "className": "com.open_care.stock.OCStockOut", "name": "OCStockOut", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "出库单", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "025c0c38-20fc-496c-a803-34f300f6ee0f", "name": "auditDate", "title": "审核日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "0b516f46-d257-42d8-816b-3c4d7986a461", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "12dd79ee-30a7-4e55-94dc-dcb9e7100c29", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "152926b7-dc67-45ac-8f30-d8a9f919b019", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "2c8a195d-1527-482b-8973-c6c2b6681026", "name": "stockOutNo", "title": "出库单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "3b5cbb84-6994-4f63-815e-184334206bd9", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "3f3aaba6-4309-4347-9b6b-d5c1b9afaf5b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "4b446296-c8ce-4647-aaee-b1e348b4825d", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "6189fed7-4853-485e-822e-44def1da3d80", "name": "wareHouse", "title": "申请药房/药库", "type": "java", "classType": "object", "refEntityId": "7793905f-066a-4cd8-85e8-0059dc7c9b10", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4af1cc5e-1968-46ed-ba9c-798b4a98fa5f", "style": "Select", "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"wareHouse": {"$id": "6189fed7-4853-485e-822e-44def1da3d80", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "申请药房/药库", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "68848b9d-0b3b-4052-b18e-1a4a8d025784", "name": "method", "title": "出库方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "66ded70f-1fa0-47b8-b62a-f79d1b3d483a", "style": "Select", "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["method"], "properties": {"method": {"$id": "68848b9d-0b3b-4052-b18e-1a4a8d025784", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "出库方式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "69258a44-a43f-4391-b24a-ec8a3b2cd17f", "name": "operator", "title": "操作人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "style": "Input", "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"operator": {"$id": "69258a44-a43f-4391-b24a-ec8a3b2cd17f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "操作人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "71c52e0e-c50c-465e-97de-45ba9cb9c77d", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "791872f9-3d2d-43c4-ac05-d3e411d3c054", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "5dcb2167-dddb-4a9e-a96d-d8011171d27d", "style": "Select", "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"status": {"$id": "791872f9-3d2d-43c4-ac05-d3e411d3c054", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "状态", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "863e7f3f-734f-48b3-a0b1-a3a775b19121", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "86f12a93-4c86-4e70-81d8-82fbfc28b733", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "9738fa5d-3ed5-4596-8f36-8a1cb895493b", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "9b824e2f-5ef7-411d-b4a8-47323368e2b0", "name": "operateTime", "title": "操作日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "a0f60c19-102e-46e8-8431-da6867072e6d", "name": "receiptDepart", "title": "领用科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "a0f77e80-d458-462a-aa23-ead0a7607246", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "a543d21e-f614-49d6-b7ab-a0bef8a4b733", "name": "receiptDate", "title": "领取日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "c785581f-0ab4-456a-84b7-ab4ac87f9434", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "c96031a1-4d3d-463f-a374-d21107a53707", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "d7300d03-1af5-4278-957b-329753c6532c", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "de6b2631-81da-4e21-96aa-172267c8b269", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "de7073fc-8d77-452c-974f-c4d0b39de6c9", "name": "stockOutItems", "type": "java", "classType": "object", "refEntityId": "f4fbc4e7-5cb0-4c5e-9abd-b1a5731291d4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "e294008f-6e32-4048-b6c7-9250da832ea4", "name": "prescriptionId", "title": "处方Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "ee1d4a81-9791-4717-abb0-13531ab25ab5", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "f52f884a-3c73-4453-825e-f9bdff9d44ef", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "f66a7aaf-ce8f-405f-9a3f-0752fe048a14", "name": "receipt", "title": "领取人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "valid": true}, {"id": "f923c8dd-873e-40f4-87f2-d12527e4ee37", "name": "auditor", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "style": "Input", "entityId": "7159653e-d6ec-4d00-9e0f-1376c1b3239a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"auditor": {"$id": "f923c8dd-873e-40f4-87f2-d12527e4ee37", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "审核人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}