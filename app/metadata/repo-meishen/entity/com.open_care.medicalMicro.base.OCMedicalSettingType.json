{"id": "326999f7-ee8b-43e5-a996-2283f928f76b", "className": "com.open_care.medicalMicro.base.OCMedicalSettingType", "name": "OCMedicalSettingType", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0fe59d0c-1834-4990-9948-d15be3c3912f", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "1340a807-f05f-4b09-a392-7492b7d15adb", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "4cacdde9-7f07-4940-a80b-506370a539dd", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "4d1053e3-bbd6-4a04-be5c-4ed7ce3b75b9", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "51d9c87d-6c19-46fa-b372-12422db0cd48", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "5b215485-9751-474a-a80f-f700dc863120", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "5eeb2873-25e5-4bbf-a210-b8d4e828bd5f", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "703b6397-7a4b-48ba-bb77-6e37cf180859", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "71aaf244-c040-4b74-b358-9e1d24243eda", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "8b731fd1-6fa1-4fc9-8aea-0a1a50b4abcd", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "8b731fd1-6fa1-4fc9-8aea-0a1a50b4abcd", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "99a25fe8-c5fb-4e31-a49d-53e1302b2f86", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "9c18fb3c-70e7-48c7-be14-e68f311ad827", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "9ed22fd1-7b4e-488e-b436-e06f1d07a68c", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "b89a4ba8-ac93-4992-8ae3-4758acdc66dc", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "b89a4ba8-ac93-4992-8ae3-4758acdc66dc", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d764c595-0cc8-44eb-80e1-820b112800e9", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "dadb635e-b909-4411-a512-40b5cbbbc274", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "e3f62fd7-dee9-4164-87f6-9bd41cedf5b9", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "e9f91307-f12b-46a8-ac17-508eea48ee03", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}, {"id": "fd501603-63d9-49d5-b622-a92867c4acea", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "326999f7-ee8b-43e5-a996-2283f928f76b", "valid": true}]}