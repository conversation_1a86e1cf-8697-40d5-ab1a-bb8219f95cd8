{"id": "15883863-523f-4454-8657-1bf67f5c546f", "className": "com.open_care.jsonObject.OCAbstractProductSaleInfoJson", "name": "OCAbstractProductSaleInfoJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "a31d0e58-04df-4a1a-9872-3db4b58d3a4a", "name": "floorPrice", "title": "底价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15883863-523f-4454-8657-1bf67f5c546f", "valid": true}, {"id": "c1c0f7b6-8370-495b-84b0-5d579be2f08e", "name": "internalCostPrice", "title": "内部成本价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15883863-523f-4454-8657-1bf67f5c546f", "valid": true}, {"id": "e971c3a9-d4fb-4a20-b199-16e1d774bf89", "name": "unitPrice", "title": "销售指导价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "15883863-523f-4454-8657-1bf67f5c546f", "valid": true}]}