{"id": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "className": "com.open_care.product.rights.OCPayInfo", "name": "OCPayInfo", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0641e639-b771-4fcc-92cb-3925653d2d20", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "28e15b20-d1f1-47ca-aadd-5ad409b76818", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "2c9671a7-1795-478e-aa3d-ba749bbe0be0", "name": "payWay", "title": "支付方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ccef6218-5f39-482b-9995-4d3c6c023a05", "style": "Select", "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"payWay": {"$id": "2c9671a7-1795-478e-aa3d-ba749bbe0be0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付方式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "2f75b286-a702-4595-a7c4-037f1856dfd5", "name": "payAmount", "title": "支付金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"payAmount": {"$id": "2f75b286-a702-4595-a7c4-037f1856dfd5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付金额", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3efcd01c-5690-4f52-9e56-c7f171b7df19", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "6218d51f-cef1-4b8d-83fd-8a63ed9ebfbe", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "6a92d96f-a985-40a9-9d1d-f0955e5d63cc", "name": "payAccount", "title": "支付账号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"payAccount": {"$id": "6a92d96f-a985-40a9-9d1d-f0955e5d63cc", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付账号", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6db6b2fc-b33f-4f2c-a978-9da5701765c7", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "7c37f5c3-cbb6-44b8-a514-5dc39b3c9f72", "name": "payCertNo", "title": "支付凭证号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"payCertNo": {"$id": "7c37f5c3-cbb6-44b8-a514-5dc39b3c9f72", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付凭证号", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8a91f238-053f-4314-b59d-787e111fad84", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "8e2fc5b3-fb1c-4975-a758-a31e5ae790fd", "name": "payTime", "title": "支付时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"payTime": {"$id": "8e2fc5b3-fb1c-4975-a758-a31e5ae790fd", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a0da692a-9296-4232-a369-bb0bd81163ee", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "d9df6cef-66e2-4403-94d7-be9aaf7b6be3", "name": "payChannel", "title": "支付渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2d22fc99-01b7-4813-8023-f5c6e36507db", "style": "Select", "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"payChannel": {"$id": "d9df6cef-66e2-4403-94d7-be9aaf7b6be3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付渠道", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e0446f0b-81b7-4790-be87-9ce2ed9767db", "name": "clientRights", "type": "java", "classType": "object", "refEntityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "e0bf36ef-78bf-4c7e-93cc-2faf4f6b84ba", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "eb0a4364-3750-49da-bf4f-e85a5e126a20", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "f12f5b06-c6dc-4dca-a096-b1ed87bb6da9", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "f292b5fa-9d30-49ff-8a16-df96720a41dc", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}, {"id": "fe7d4f47-c5ba-4f30-9006-62c1653e50cf", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "valid": true}]}