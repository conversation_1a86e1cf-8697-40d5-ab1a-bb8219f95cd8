{"id": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "className": "com.open_care.product.topmedical.OCPreAuthorizeAudit", "name": "OCPreAuthorizeAudit", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0747c4c9-9092-4eba-97e6-194133d68714", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "0b87e140-b08d-4945-94d1-80e4f4378ab1", "name": "ifBindCase", "title": "是否关联", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "15f3dc57-3e75-4acb-bf4e-aebeedac76dc", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "19d56a41-dad6-4034-a471-d8d95230daf1", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "1d27380c-4443-4b8c-93da-64c9a67fcbaa", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "2142f6bd-2b0b-4655-baa2-4892f2fae9c5", "name": "auditNoteDate", "title": "审核备注日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "2c966775-a188-423c-a408-ef7ca643af10", "name": "authorizeStartDate", "title": "授权开始日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "3fed56c4-b708-4571-8b79-f296d1782313", "name": "quarntity", "title": "数量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "4bd77488-2ec2-4173-b0b6-1d2aaabd3c2d", "name": "caseNo", "title": "关联案件号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "50f5b723-9b78-46cb-835a-68421be4e345", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "52b1bf12-7b65-4657-9c4b-ecbf4ec17a72", "name": "channelForGuarantee", "title": "担保函发送渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "562c742e-c0d5-457e-b56e-fe1d952cb90b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "6c12e937-18cb-4b82-8b0c-1dafcc8460c2", "name": "authorizationDate", "title": "授权日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "7c4e3e28-e493-4ad7-818e-cd9822298745", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "85c1dc9a-91dc-4896-af06-fb883158817b", "name": "reason", "title": "决定原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "8714f87b-2cd7-4cc0-ac3d-414ccb032b2e", "name": "plan", "title": "方案", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "91901740-db04-4a77-ae2c-82aefca2e733", "name": "planNote", "title": "方案说明", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "9bb5fcc8-1ae0-4eff-8cfe-b2e7a8b240e5", "name": "attachReadyDate", "title": "材料齐全日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "ab9ba293-7a61-4a87-9e10-33260007f2b1", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "b19aa67d-0c89-4910-ab68-e356f64bec8f", "name": "<PERSON><PERSON><PERSON>", "title": "授权人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "b5608792-3369-47a5-8cf0-78719dc0e51d", "name": "conclusionNote", "title": "预授权决定备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "b5ac8458-bd1b-4964-8f7c-21df61b6734c", "name": "ifApplyDirectPay", "title": "是否申请直结", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "b87e6927-811e-4a26-b2ce-39f4de3c0860", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "ba215fd1-d1d5-45cf-a9fa-726205a88944", "name": "authorizeEndDate", "title": "授权结束日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "be9f49a3-b5e1-4886-b529-24ab6f220ec8", "name": "directPayQualifi", "title": "直结资格", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "c69f051f-2f16-404b-8365-ba22b15dc150", "name": "bindType", "title": "绑定类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "d5465157-89f8-4f2e-8388-c5a95a06764c", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "d934e819-c95a-4aa6-a826-05c5fbc4722d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "db972f13-0988-4f87-aa69-f2bb3af2d945", "name": "auditQuota", "title": "预授权金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "e4433dd2-3929-4628-a624-9c510cd5d862", "name": "auditNote", "title": "审核备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "e4a9c1d4-e970-43ec-87e3-cfce155a7e82", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}, {"id": "e4bcf296-3caf-4820-858f-de10fd786ab1", "name": "conclusion", "title": "审核决定", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "db16bbc9-dd1d-441c-ba2c-d7c8f9918032", "valid": true}]}