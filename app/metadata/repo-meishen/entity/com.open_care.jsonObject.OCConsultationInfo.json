{"id": "d06e5d12-fa02-417b-b5a2-b8863d9ad512", "className": "com.open_care.jsonObject.OCConsultationInfo", "name": "OCConsultationInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1f91d667-b6d8-4d51-9748-a85ec2c311c2", "name": "consultationContent", "title": "咨询信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d06e5d12-fa02-417b-b5a2-b8863d9ad512", "valid": true}, {"id": "cc0713eb-646e-4bb6-b811-e7dcfcb24fe6", "name": "imageList", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d06e5d12-fa02-417b-b5a2-b8863d9ad512", "valid": true}, {"id": "e85a5868-5e97-4dc2-80cd-45a1c6134d76", "name": "age", "title": "实际服务人年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d06e5d12-fa02-417b-b5a2-b8863d9ad512", "valid": true}]}