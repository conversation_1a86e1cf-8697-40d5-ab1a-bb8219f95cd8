{"id": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "className": "com.open_care.stock.OCStockIn", "name": "OCStockIn", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "入库单", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0769bab7-f133-49fd-99b7-f574b6bae060", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "17e471d6-20a7-4a11-9b4c-6764cdca83e5", "name": "invoiceNo", "title": "发票号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "18fea2f7-5982-481d-a86c-d170ea0bbacf", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "1f8bd1b7-9235-44b3-85ac-39edfce34364", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "320b5dab-6bc1-4015-a0c4-8b11e487d471", "name": "auditor", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "style": "Input", "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"auditor": {"$id": "320b5dab-6bc1-4015-a0c4-8b11e487d471", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "审核人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3705e49f-e78c-443d-a468-1605d5014a6a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "39e13944-9d6e-4ac8-b329-8db8ae52d4f6", "name": "stockInNo", "title": "入库单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "3fc6bf35-d1ee-4d39-8e70-5216d696c346", "name": "auditDate", "title": "审核日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "416fd29d-70fc-43ed-b57b-981406aa2886", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "5c860352-c694-4d54-bec7-3789d84c9058", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "60801d09-ed3f-4a61-934b-7423671ae32d", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ed954506-49ea-410a-87ed-36eef6cd2acc", "style": "Select", "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"status": {"$id": "60801d09-ed3f-4a61-934b-7423671ae32d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "状态", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "71382fc8-9786-4b27-ab4b-ecc7f31565af", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "9231e4c1-90e3-4fd5-81ab-327fd25e63c9", "name": "wareHouse", "title": "申请药房/药库", "type": "java", "classType": "object", "refEntityId": "7793905f-066a-4cd8-85e8-0059dc7c9b10", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "87288b24-ef4f-48b8-b81f-581ccb2c5947", "style": "Select", "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"wareHouse": {"$id": "9231e4c1-90e3-4fd5-81ab-327fd25e63c9", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "申请药房/药库", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "94c6208c-5450-4b25-8ba5-38e7645a9da0", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "9acff087-5511-4c1f-9d81-ff6f426062ba", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "a6cf1293-85b1-40bb-8ab0-28041232cc21", "name": "supplier", "title": "供应商", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "a6d77a8c-993d-45ef-8099-6216574bf5b2", "name": "operator", "title": "入库人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "style": "Input", "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"operator": {"$id": "a6d77a8c-993d-45ef-8099-6216574bf5b2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "入库人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b1235d32-0db9-444c-bb48-afaa00ed9d41", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "b51d6fc5-62b2-4c4e-b4d2-745d8d65e4ee", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "ba1ab72b-559a-45e4-bfac-1ac934507a1d", "name": "stockInItems", "type": "java", "classType": "object", "refEntityId": "2bf2a3cf-e7e5-4d63-9b9f-d77ea449e6ea", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "be5e4b2f-8b8b-4cb9-a26d-325ef9fd4b3f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "c8f385c2-ea02-43eb-be1c-7206648307c5", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "c965cb62-69a1-42de-8c0b-9cc5eef22754", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "e1a61593-9d23-453a-b9ec-891f2ee49c4a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "e31de507-d76b-45fb-a1b6-d5e1b2f9eb99", "name": "operateTime", "title": "入库日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "e5fbf1a3-760f-48f5-a37d-dd82ab40b961", "name": "method", "title": "入库方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "810d2bf6-ffd0-419b-b027-16849b7793cf", "style": "Select", "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"method": {"$id": "e5fbf1a3-760f-48f5-a37d-dd82ab40b961", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "入库方式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f359504f-2c65-4594-b97d-dfdb8a0bbf24", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}, {"id": "fb200880-73ad-434f-bb90-7dc8e5996897", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f74b0b71-b2d3-4c72-a748-09e95a8ccfa4", "valid": true}]}