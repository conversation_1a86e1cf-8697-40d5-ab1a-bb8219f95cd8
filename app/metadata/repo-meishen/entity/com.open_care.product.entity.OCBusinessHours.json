{"id": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "className": "com.open_care.product.entity.OCBusinessHours", "name": "OCBusinessHours", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "330f7e95-bf43-4d74-89af-732046b00258", "name": "wed", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "3e0b1288-a8a2-41ba-bfeb-9c6f4fa14fbb", "name": "thur", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "6c6e37eb-6103-4887-9746-2813defe653c", "name": "_entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "75ace2ff-f337-463d-b09e-181c6bc540bb", "name": "pointTime", "title": "工作时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c98c166a-b23c-40b4-acf1-50e1c5207cc5", "style": "Select", "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"pointTime": {"$id": "75ace2ff-f337-463d-b09e-181c6bc540bb", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "工作时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "8c3992bc-f1d0-4ac3-a0fb-e1520fce70da", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "9cb462d0-6955-4a10-92f5-ce83fee22187", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "a9755536-43cd-4993-aaae-424593eecd7b", "name": "sat", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "ae07d1bc-98ee-43b5-888c-9b0283e308f8", "name": "_rowid", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "bce91501-1b0e-46f3-8ed9-d9ac5b514609", "name": "mon", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "cb27e55f-73bd-417b-875d-6b2ad9a39a86", "name": "tues", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "e33a493d-c16e-484a-9185-eb165e080ab0", "name": "sun", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}, {"id": "f71deb3c-8045-434e-b7be-adb58cc0ac5e", "name": "fri", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "49ce0237-87b9-42b2-bb5c-d70cd3c47d44", "valid": true}]}