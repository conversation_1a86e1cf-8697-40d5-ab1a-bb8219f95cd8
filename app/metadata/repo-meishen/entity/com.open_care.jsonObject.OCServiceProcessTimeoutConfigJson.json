{"id": "6338aeea-705c-443c-8364-e84a1714ba59", "className": "com.open_care.jsonObject.OCServiceProcessTimeoutConfigJson", "name": "OCServiceProcessTimeoutConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "11e68e69-e691-4ac3-bb42-55fad4f21256", "name": "reminderIntervalUnit", "title": "提醒间隔单位", "type": "java", "classType": "TimeUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "250eb7bd0b76bfb1b410c97f063223b36fca19a7", "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "571330d7-491a-41c4-9037-f515211d2ee1", "name": "userIds", "title": "提醒人员", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "c13a3af2-15f1-4a2c-8845-a1df3d5166ad", "name": "reminderInterval", "title": "提醒间隔", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "c2f75494-c148-4bef-be4b-1f5067a0f018", "name": "timeoutPeriodUnit", "title": "时效单位", "type": "java", "classType": "TimeUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "250eb7bd0b76bfb1b410c97f063223b36fca19a7", "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "c7c894ca-c860-48c2-800a-7362665e05bf", "name": "reminder<PERSON>ethod", "title": "提醒方式", "type": "java", "classType": "ReminderMethodEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "6f7c67197cba669e1185851731b6e1fa93c85be3", "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "ce51c15f-0ed4-4d52-93ce-bccffe9de185", "name": "roleIds", "title": "提醒角色", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "d907f5e1-bba2-4f72-a8c7-fda95e2b2ecc", "name": "serviceTaskType", "title": "业务场景", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "91bb6b8080914301b3dc6ee7ef2462c47c7ca266", "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "fa0c7981-34ae-416b-8981-06f414670e47", "name": "timeoutPeriod", "title": "时效", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}, {"id": "fd7ac208-3770-4e01-bb80-470e3e44adcd", "name": "reminderCount", "title": "提醒次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6338aeea-705c-443c-8364-e84a1714ba59", "valid": true}]}