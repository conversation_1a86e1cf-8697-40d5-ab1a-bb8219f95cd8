{"id": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "className": "com.open_care.dto.schedule.WeekScheduleSettingDTO", "name": "WeekScheduleSettingDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "013a3567-1869-45a3-8d48-b6b05df32e71", "name": "resourceName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "016bf1cb-fd84-4faa-9079-d1b6d01ea602", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "0350aa8a-a783-4a05-9c88-8b9d8e75c75d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "0365438a-95de-459f-b09c-17bf7585250e", "name": "startDate", "title": "开始日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "062bde5a-b771-4933-92de-426d6b0bda36", "name": "endDate", "title": "结束日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "1c2b7c5f-f431-43d6-8f68-79b9f38c671c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "29777702-2eb1-4606-bc7a-6913dbfcbb04", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "3171381a-b6f0-4e89-8245-886d35d4c7b8", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "4c7e74d0-6702-4d4e-a227-13ee7d46ae92", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "4daf3fb7-ecb9-416f-a5ff-5974d25c9b73", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "4eeb4d9b-a553-435e-8bf6-66adb1736efc", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "80ead519-4b1e-4ab3-833f-1a5b2dfc76f6", "name": "sunShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "89e381fb-37c1-4b8a-87dc-7b8e54faccd3", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "8a38e1bf-f2ee-4899-a60d-9faf6ef45cf8", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "8b261880-4c8f-413e-a680-db2c5b265845", "name": "resourceGroup", "title": "科室", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "97066f5e-146c-44f3-bfe9-f011020bc512", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "97fbe03b-6944-4ee6-8273-fc16e421b623", "name": "resourceId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "994dc755-73f1-4672-941b-2fe07bb8b64a", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "9d3db362-716e-4c09-9f81-74cfae80169d", "name": "resourceJobTitles", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "a39f4527-28ee-45e1-b6dd-f675e307b96b", "name": "departmentName", "title": "科室名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"departmentName": {"$id": "a39f4527-28ee-45e1-b6dd-f675e307b96b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "科室名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a909b45a-91f9-4140-ad83-dce3bd938b55", "name": "monShift", "title": "周一班次", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "ac5df4da-45aa-4927-ad1f-50a8c8008288", "name": "departmentId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "af0e0912-9646-4e7d-9e54-254073ee22b5", "name": "satShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "baf45930-b142-4654-85ff-c6072420e8ac", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "c2760f67-95cb-4811-b3e1-dba7e18a0b3d", "name": "scheduleMode", "title": "定期模式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3e93fd68-7bae-4714-b558-5b8ab5ac3e4e", "style": "Select", "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["scheduleMode"], "properties": {"scheduleMode": {"$id": "c2760f67-95cb-4811-b3e1-dba7e18a0b3d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "定期模式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c84fe410-76b8-4d4c-861f-37a877544fe1", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "cc3a3010-6113-4a85-adf8-1811db03f50b", "name": "resources", "title": "排班资源", "type": "java", "classType": "object", "refEntityId": "7dd3506b-c0a7-4d8b-8def-73c2cc121012", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "d4ff67e3-92ba-49e0-903a-991ff63581b6", "name": "thuShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "d5e6cef2-d623-4675-93fa-49ed3b55e440", "name": "friShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "d604d37e-5e11-4108-8a77-612f3d3c4ba9", "name": "wedShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "eb6a32ab-c474-4755-98a3-3d98047ac25c", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "effb7678-2873-48a2-992f-3b22c4b4819a", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "f073db9b-c3d8-4db7-9866-bc5ad872f19c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}, {"id": "f5bdba95-1914-4265-a09e-4c7497c67949", "name": "tueShift", "title": "周二班次", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "98dacbdb-fd8c-48dc-a4dc-d70ff039e2f0", "valid": true}]}