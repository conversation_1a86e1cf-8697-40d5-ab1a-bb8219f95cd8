{"id": "018203f8-7b58-4d95-b372-d31b5ba17a51", "className": "com.open_care.dto.sys.EnumReferenceDTO$EnumReferenceItemDTO", "name": "EnumReferenceItemDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "56f4b5b7-6e6e-48e3-8592-25dd79b778ad", "name": "value", "title": "值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "018203f8-7b58-4d95-b372-d31b5ba17a51", "valid": true}, {"id": "6caeb24f-1d3b-40ba-8361-0d3ed75e26e4", "name": "label", "title": "标签", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "018203f8-7b58-4d95-b372-d31b5ba17a51", "valid": true}, {"id": "ebc91580-5429-4f74-9a10-2322b93e5db7", "name": "properties", "title": "属性", "type": "java", "classType": "EnumProperty", "refEntityId": "5c109253-d819-479b-848b-75cbb843f21d", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "018203f8-7b58-4d95-b372-d31b5ba17a51", "valid": true}]}