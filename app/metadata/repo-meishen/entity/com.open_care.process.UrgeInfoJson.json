{"id": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "className": "com.open_care.process.UrgeInfoJson", "name": "UrgeInfoJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "08a0c628-d130-41e1-b0a9-07bcb3d9368b", "name": "operatorRole", "title": "处理角色", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "25e7d960-b94d-4d58-8437-c1cc13b14e3b", "name": "handleStatus", "title": "工单处理状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "3a14fe7b-d78c-45f1-a162-f4e52fa84ac4", "name": "ocTaskHandleResult", "type": "java", "classType": "object", "refEntityId": "4cb1c021-fd82-4136-b1dc-b6bec2d4668e", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "3edbf34a-09b4-4070-9b00-287ca4194d0d", "name": "operatorUserNo", "title": "处理人工号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "4b67c353-e04c-4fe0-899e-d7c93b3e24bf", "name": "operatorName", "title": "处理人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "594c12e9-4134-4108-84d7-51314845cc59", "name": "taskName", "title": "任务名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "665bac57-b45c-4691-9eb7-3e824e9370c4", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "73092a9f-4f74-4208-8b61-7b879f4699a5", "name": "attachments", "title": "附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "88e1c735-e819-4f1b-a903-27be7d117714", "name": "notifyUserId", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "910d7ea5-8ac3-458f-a86d-f834e9b90fc3", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "ac62a783-91db-4818-9e9f-7e17349ea7fe", "name": "operatorTime", "title": "处理时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "e0e076c6-8627-44e1-b454-b7415d77f277", "name": "operator", "title": "处理人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "e2f347e3-be4a-4f1e-a2d1-6eaf8e29dcd5", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}, {"id": "ff2c14f8-f5e2-4c5c-90a6-a8cca2853145", "name": "canReplyUrge", "title": "是否可以催办回复", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "valid": true}]}