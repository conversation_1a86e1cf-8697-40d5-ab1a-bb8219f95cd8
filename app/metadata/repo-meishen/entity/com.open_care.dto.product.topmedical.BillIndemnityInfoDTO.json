{"id": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "className": "com.open_care.dto.product.topmedical.BillIndemnityInfoDTO", "name": "BillIndemnityInfoDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "19e5cce3-52a4-42b4-bec0-57e4c36b3e11", "name": "discountAmount", "title": "折扣金额", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "232205c3-215e-488d-9bf1-93d04cd5e6b2", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "243e0fad-1082-478b-9c98-753e51d23107", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "27949c39-b680-4261-aa11-b12ba456378d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "2c10b127-b349-4c30-b145-f50e2eada756", "name": "diseasesAtPresentation", "title": "就诊疾病", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "34aacb6b-e52c-4e37-b400-807a2c2d9ecc", "name": "hospitalName", "title": "就诊医院", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "373c3e68-8203-47d9-9cdc-06b30633378f", "name": "medicalNo", "title": "账单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "46b0c1d3-acc8-4fa5-a70c-860ac7640761", "name": "personalPayment", "title": "个人自付", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "4b596821-48b5-42d3-99a9-0a0d990a6756", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "4d3cc9d5-560a-4373-94aa-23ad3bfd0ec9", "name": "payFeeSum", "title": "赔付金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "69c42b89-eb8d-40cb-b0bd-f84bc9d54a90", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "6db13b6b-8e5d-4f9a-961a-7aa8f42c94b4", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "874573fd-cf47-456c-b70e-efe1bcf8cbdc", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "9052d7c3-38d8-45a0-b36c-af4a87d64ce9", "name": "feeitemType", "title": "就诊类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "96661008-11fa-4525-8fc6-60b08faab646", "name": "reasonsForNonPayment", "title": "拒付原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "9b7e2003-4fc7-4020-808d-4338ed2e293c", "name": "contNo", "title": "保单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "a76c13a8-3016-42ae-a0a8-54eed4c89e8a", "name": "feeAmount", "title": "账单金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "a9644659-a1e2-498e-bb34-dfe89a6d3178", "name": "inHospitalDate", "title": "就诊日期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "afc4dfd5-e8eb-42ea-a5a0-f94edc6888ce", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "b471e58c-d498-4c05-bd32-1f7f3dc1849a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "bbe1a4c1-75ba-4c11-85fc-41a2fc85c4b4", "name": "advancePayment", "title": "先期给付", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "cc0e58fd-88ae-416f-b2af-844ae30fc9b3", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "d7f0a010-9bfa-4f3f-a96d-0ceff488fb17", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "df3bb95b-995b-41e3-89d9-9e002e2c5bb9", "name": "amountRefused", "title": "拒付金额", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}, {"id": "f8b90b31-0cc6-4161-b1eb-0c91f363f39b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2d803c82-6b5a-4c73-b49f-7543c0b6e3ca", "valid": true}]}