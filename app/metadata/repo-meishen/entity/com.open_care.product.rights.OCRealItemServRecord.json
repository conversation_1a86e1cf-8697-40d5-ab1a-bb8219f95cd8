{"id": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "className": "com.open_care.product.rights.OCRealItemServRecord", "name": "OCRealItemServRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0baef439-a128-4692-8281-b7087647672a", "name": "realServQuota", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "2640226f-5f4b-4ed8-add3-be5dd2ed0bfd", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "32034d91-cf29-4389-9f43-8b01d67786da", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "3ea8ce6d-ec85-4b7e-9fa0-d19d91d5e45b", "name": "realServTimes", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "51879482-ba9f-4351-adb5-18afbf71da9c", "name": "servRecord", "type": "java", "classType": "object", "refEntityId": "d3634751-26a5-48e3-bc34-461056fe8e87", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "51a0c875-cfa1-48a1-baa7-599d8faedb75", "name": "subscriptionItem", "type": "java", "classType": "object", "refEntityId": "886875aa-47a6-42ff-b7f0-29ff3839a2b5", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "5b765513-d29c-47a8-a412-7d94fdae8604", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "7c66eeeb-4f42-4b09-a04b-324fade57ff9", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "8b6bc551-26bf-4123-b7e2-cb0096b7d8b9", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "e0a67a7c-25b1-4444-9126-f7d6b9aa8af2", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "e58267bf-5182-41fe-b667-e4d8e4c4ad22", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "ea6fb96c-c189-4e35-bfda-f8e6568fa9bb", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "ebd4f53a-fe5b-469e-831a-4273f51f35aa", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "ec1c0168-b3c7-4259-b93f-4fa978bf19aa", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "ee61b8f2-9b79-4ae2-9156-d7705e7bba57", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}, {"id": "f5dbc0e2-d408-40d4-a57e-5d6c314c5ef2", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "18a5187e-18d2-44bc-83f2-b6ff8f144cb7", "valid": true}]}