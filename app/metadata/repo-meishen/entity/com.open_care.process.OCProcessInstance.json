{"id": "dd9ca923-48c8-4be9-ac1e-080723343dac", "className": "com.open_care.process.OCProcessInstance", "name": "OCProcessInstance", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01da7bc9-335f-4e0f-98e0-1124bfd06389", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "07dafc0c-16ba-4813-a6fa-f79c43d80137", "name": "processDefinitionKey", "title": "流程定义key", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "41ee98279d8cf882e674510acc859bbd", "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "0e5492e0-0d1b-4d14-a092-63cf96b3ddfe", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "1807bcf2-ced4-4051-9d64-a529f7a9308c", "name": "ended", "title": "流程是否已经结果", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "1c390726-e935-49c2-9af1-e1f69f37bb5a", "name": "processInstanceId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "20fe29ba-d3ef-405f-8a3d-abb79a4193b1", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "2189a6ca-a7dc-49f8-a1a9-5b1f1c6c0d49", "name": "endTime", "title": "结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "2c3b0e9a-ffff-40b4-a8d3-93704ea14ec0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "4b3fc6e3-c6b5-4d69-baf8-bc5abfe20eca", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "54d60f00-c32d-4af5-b54e-3c263b8a13c6", "name": "processDefType", "title": "流程类型", "type": "java", "classType": "ProcessDefTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1b9a61c42c97508d8eb6eee791973e252e69d376", "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "55ef3946-8ba7-4bad-9af1-5bad93c9ae99", "name": "processDefinitionId", "title": "流程定义id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "56872c2e-9a63-4eee-95f8-b022ac931b13", "name": "cancelTime", "title": "取消时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "61a85027-8c8a-435e-8ee0-5ba4fb9a2ec5", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "82912b39-1df7-4d9a-b996-dcd8c10eac73", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "848b4ed0-7ade-4982-997a-7c6675b54d82", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "a41e0950-6762-4d8a-b58e-a7d5713e44e7", "name": "cancelUser", "title": "取消人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "ae229311-eb5c-4eb1-a8d8-7c57bec6fd2e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "c52b4bd0-3acc-46a7-9cec-fddf28c72c46", "name": "canceled", "title": "流程是否已经取消", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "e5e4442d-ff54-46b7-8acf-026ff285c335", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "f4ad32a1-8718-4fc1-bd31-5cdc2ed9c4a6", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}, {"id": "fab00a1c-4e30-4a4d-9379-6461d0f62fee", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "valid": true}]}