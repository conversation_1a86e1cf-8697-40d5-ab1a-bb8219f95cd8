{"id": "b809f489-a298-46c6-9bd5-486904ebad04", "className": "com.open_care.dto.survey.SurveyCategoryDTO", "name": "SurveyCategoryDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "060726a5-a508-422d-8db6-b22ce9ff0072", "name": "autoPublishing", "title": "是否自动发布", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "0ce8c2ba-d69f-4043-a8c9-1670af934201", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "12d71c9c-b6f7-421d-861c-0d06d9c9b356", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "359dec73-2593-4fa1-aece-8f5ce0bd7807", "name": "note", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "36154a0e-c6c2-41d9-aaff-b72819a3fbf2", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "47d819fe-2e58-4f3d-8c13-e98bbbdb7211", "name": "parent", "type": "java", "classType": "SurveyCategoryDTO", "refEntityId": "b809f489-a298-46c6-9bd5-486904ebad04", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "4b264cef-602f-4af2-ae6a-9a4fab8e96b7", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "56f4e607-d4bc-448c-a587-d1bf31401800", "name": "serialNumber", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "5a26bc6d-6d8f-4779-99c7-1262dda13b7f", "name": "title", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "b9c85c6c-fff8-4add-a73a-1d3537b15c16", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "bf0cefbd-6ba3-487a-a512-6859eda74edd", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}, {"id": "f943be6c-52a2-4270-a07f-e5cb09a82e07", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b809f489-a298-46c6-9bd5-486904ebad04", "valid": true}]}