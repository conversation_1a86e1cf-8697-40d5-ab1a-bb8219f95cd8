{"id": "00d19b85-ab30-4999-936b-b9ddbb827227", "className": "com.open_care.product.entity.OCMedicalProductTemplate", "name": "OCMedicalProductTemplate", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "068044b2-a572-46df-95e6-cecb49780543", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "0b931371-743b-4782-bbaf-9747607b2153", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "0c462f74-b88e-43bf-825b-8c2ddeef2e67", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "13cd8d5e-a435-425b-9393-5f89478f9e9b", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "1d967aea-5d7e-4c3e-a311-f58fd1891002", "name": "taxRate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "2290bddd-31bf-4a20-8b1e-de1fb80c8ecf", "name": "tags", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "248aa71a-1496-4d62-b6b0-872001cd6973", "name": "marketPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "24f06e4e-4bc8-43cf-a55b-9c26c91db1a7", "name": "sellPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "280f8b60-349d-4b1e-812d-eb29a8b796fb", "name": "belongOrg", "title": "所属公司", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "28b840fb-6384-4999-ae92-428166a67df9", "name": "status", "title": "产品状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "2da0a42a-7a87-40a6-a6cc-aec6e98abb66", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "2dcec59c-b9e6-4aaf-9a80-2a2796c86f32", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "3f8cf4cb-e1ea-488b-bd5e-b2c99ac8fc44", "name": "healthreportTemplates", "title": "报告模板", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "410a27a0-012c-477c-aa39-d410ee7f1c6b", "name": "medicalType", "title": "项目类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "44f1893c-3aa1-48e1-b47d-a38f369d5d47", "name": "processTemplates", "title": "流程模板", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "470344db-1549-4b27-b753-ff1439653e06", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "4bd41558-ac71-4f71-a640-ac4565cc2028", "name": "suitSex", "title": "适用性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "4f528909-6d68-4bc7-beb1-876131c3e6e7", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "504244ea-f4a0-49d4-a8c7-fe989bc82f15", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "5233b37a-f772-4bac-a5c1-e318a4bf080a", "name": "processTemplateKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "5b13ee24-63e6-46c4-ba73-2224aefba114", "name": "name", "title": "产品模板名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "60108ebb-3c74-42ca-86dd-b3299184bf72", "name": "healthReportTemplate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "71e35e66-4f1b-4ecd-8b54-0c09da1a3416", "name": "suitMarriage", "title": "适用婚姻", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "76fb425c-5226-485b-b618-409dcc7db340", "name": "sex", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "7fc8ab58-840b-4e8a-9624-bb5eacab56d3", "name": "placeHolders", "type": "java", "classType": "object", "refEntityId": "1050e8f8-ea41-4465-972d-329ec46c5065", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "8fe66649-097e-4009-ab05-009272567197", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "91fbd58a-8656-437d-93d0-d6afe19bb4a7", "name": "minAge", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "a303cf2c-d727-40ae-b2e3-194b9d8ff163", "name": "marriage", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "a5d32a7e-917e-4b7c-b495-ac9c2c28a542", "name": "maxAge", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "adc34f7e-a589-416b-9827-84faa5210383", "name": "phonetic", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "b37462cd-0b7b-4ba1-85d6-396be3842280", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "cb9feb4b-a82d-4697-a79a-296ea7f441c0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "e78f8c33-093c-4ce2-9827-a57b11201b08", "name": "productCategory", "title": "产品类别", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}, {"id": "f8775d6a-b7a5-4f02-83e4-1f490e81b987", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "00d19b85-ab30-4999-936b-b9ddbb827227", "valid": true}]}