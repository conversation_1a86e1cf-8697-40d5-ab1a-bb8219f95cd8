{"id": "562237b9-2940-4f99-9f77-eef50035cf02", "className": "com.open_care.product.rights.OCClientProject", "name": "OCClientProject", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "09cce785-266b-4dad-8313-da52cdb47958", "name": "recordNo", "title": "项目编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"recordNo": {"$id": "09cce785-266b-4dad-8313-da52cdb47958", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目编号", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0c478fe8-95c1-46eb-9de9-a2220df44efb", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "12bfc548-8001-4de0-b280-f82d7f6551e6", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "289e5d7e-a74a-4d37-90e6-9c838f0c60e6", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "29cc3bef-081d-4c11-8205-eaa9a2fb32a5", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "29de4c40-1d5d-4eeb-976d-616d2158c9e8", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "48d7cbd7-f6a6-469c-829b-05faddb99cc1", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "5667b577-8e91-463f-b108-2e6f4f19fb35", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "76cd709e-b2fa-40f1-a269-c27262faca44", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "83126cb8-723f-41c1-930a-a204330a3b77", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "8f4252ef-fc8d-40c0-8266-7a56d4ad5960", "name": "orgCode", "title": "机构代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["orgCode"], "properties": {"orgCode": {"$id": "8f4252ef-fc8d-40c0-8266-7a56d4ad5960", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "机构代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a69568c2-2e4b-4621-8b0e-62f13fdc9b28", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "b153d232-d03d-4713-97f4-162964067bd9", "name": "type", "title": "项目类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"type": {"$id": "b153d232-d03d-4713-97f4-162964067bd9", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b4a11f3d-3fd0-4672-8874-8abb93222a09", "name": "note", "title": "项目简介", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"note": {"$id": "b4a11f3d-3fd0-4672-8874-8abb93222a09", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目简介", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d6a970fb-a1e4-4bbe-9573-bed65a6c0ff2", "name": "name", "title": "项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"name": {"$id": "d6a970fb-a1e4-4bbe-9573-bed65a6c0ff2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e616d6bc-cfc0-4e90-8543-bc4392ae7820", "name": "clientAmount", "title": "覆盖人数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"clientAmount": {"$id": "e616d6bc-cfc0-4e90-8543-bc4392ae7820", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "覆盖人数", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "eaf26adb-927a-4f8d-aaa9-ee9bbdfbbb9e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}, {"id": "ec7fe361-210a-43da-87de-69be45820ab6", "name": "manager<PERSON>ode", "title": "项目经理工号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["manager<PERSON>ode"], "properties": {"managerCode": {"$id": "ec7fe361-210a-43da-87de-69be45820ab6", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目经理工号", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "edfdd3f2-074a-45c1-bafb-4012915dd054", "name": "bizChannel", "title": "业务渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "9aa16e60-ccb4-45b4-90f9-b033d2e1ca3d", "style": "Input", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["bizChannel"], "properties": {"bizChannel": {"$id": "edfdd3f2-074a-45c1-bafb-4012915dd054", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "业务渠道", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ee71d8c3-9e43-46bd-909a-5eefee6964ff", "name": "status", "title": "项目状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"status": {"$id": "ee71d8c3-9e43-46bd-909a-5eefee6964ff", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目状态", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f6f6d3b6-ee78-4bd3-9e17-156db00bdf02", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "562237b9-2940-4f99-9f77-eef50035cf02", "valid": true}]}