{"id": "5c109253-d819-479b-848b-75cbb843f21d", "className": "com.open_care.dto.sys.EnumReferenceDTO$EnumProperty", "name": "EnumProperty", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "2b846efd-98c5-4459-967f-2ed980274918", "name": "name", "title": "属性名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5c109253-d819-479b-848b-75cbb843f21d", "valid": true}, {"id": "d27f0dea-c14d-4c94-b255-1e82a3205b8e", "name": "value", "title": "属性值", "type": "java", "classType": "Object", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5c109253-d819-479b-848b-75cbb843f21d", "valid": true}]}