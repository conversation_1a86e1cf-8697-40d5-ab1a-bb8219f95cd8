{"id": "f5af6ae5-29e2-474c-a287-68f258642dc9", "className": "com.open_care.order.entity.OCOrderAssoc", "name": "OCOrderAssoc", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05ee3014-3df2-4eeb-925a-f853524e8295", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "1a1ac19b-ac12-4c06-a11b-6b059fde9cf2", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "35c4f2bf-2c60-4560-94db-f04f06a7e9f7", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "3635ebc5-3859-4226-b69b-73cc01b2c067", "name": "fromDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "3f94673b-d593-4dbf-8b07-f73758dd5950", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "60daa8f7-f637-4794-b4a5-e4a3005433ce", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "7164a61f-fcc0-4008-8111-6799c6844762", "name": "orderAssocType", "type": "java", "classType": "object", "refEntityId": "925f9bdd-c428-425d-b16b-c9ec96c4e50e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "76055802-a608-485c-94ae-cfbf1a9c0a6c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "7d4f51cf-9438-402c-9a4c-53d94640d1b0", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "8d9f95d4-4820-4ca6-8f74-a6c63245be56", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "9098ab26-363c-453b-a19e-97400eb87699", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "af1cc46d-cdf4-489c-a21f-1447a6d1d3b7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "b6f77a24-6f70-4dbe-a5c4-cb65adfd3025", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "b9312f1e-cb6c-483a-aa4a-80b5f3b71c5d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "bc2a5c94-231e-4157-8a54-ea9c87c83b26", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "bfee652c-1aaa-4a40-9f38-ee9274868a8c", "name": "thruDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "c7c3499b-2aee-4cb7-a326-2c9f16332ed2", "name": "target", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "c94db35c-50df-497a-9fb5-88780af7ce7e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "d22ac030-5d26-4025-a827-776462f399d1", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "d4a800cd-b3e2-40e1-8156-806a4b0571a0", "name": "source", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "d7ff08e1-405f-45e5-94bd-259957789ea3", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "ea35cee6-01e5-4161-b665-3cf3a3181147", "name": "remark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}, {"id": "eb5d4b67-d128-4982-a8fd-030a2de99522", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f5af6ae5-29e2-474c-a287-68f258642dc9", "valid": true}]}