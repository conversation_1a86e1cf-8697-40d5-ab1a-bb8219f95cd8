{"id": "329e9d4b-efe3-4217-b201-feed2792c65d", "className": "com.open_care.dto.third_party.UnifiedPlatformServicePackageDetailDTO", "name": "UnifiedPlatformServicePackageDetailDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00896d61-f335-463c-a3cd-c36b30eab991", "name": "svcName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "010008e4-ac3a-4f71-b250-ebd4a940c349", "name": "disId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "40b82982-2640-4cbf-8c4d-53f15fff5be8", "name": "waitDay", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "5f67c287-1d82-4671-bb37-034c814f4046", "name": "appointmentPageInfo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "6cf66f87-0305-4049-b9ac-7cee80969604", "name": "serviceBindId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "85fd96a8-b3a5-463f-94af-e862be73d885", "name": "expDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "8f64e971-859a-4163-8a02-d422574ee6f0", "name": "count", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "ae929fbe-9094-499a-9ca8-ec614fc825b6", "name": "availableCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "f5d93100-f07a-46c8-a857-37a1a818636d", "name": "svcCode", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}, {"id": "ffb81b3b-df78-4610-8035-9590ef348302", "name": "effDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "329e9d4b-efe3-4217-b201-feed2792c65d", "valid": true}]}