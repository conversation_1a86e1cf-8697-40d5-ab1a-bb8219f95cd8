{"id": "94d5f252-5489-42da-8a01-c6226c2<PERSON>ba", "className": "com.open_care.jsonObject.CompatibleWithOtherSystem", "name": "CompatibleWithOtherSystem", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1564802c-9b5a-430f-b5dc-44aa142b079c", "name": "otherSystemClassificationName", "title": "其它系统分类名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94d5f252-5489-42da-8a01-c6226c2<PERSON>ba", "valid": true}, {"id": "6e60c8ff-5eda-434e-bc69-3971db8c9111", "name": "otherSystem", "title": "外部系统", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ff8fd643-68d8-426e-a7a4-94e02f12345d", "style": "Input", "entityId": "94d5f252-5489-42da-8a01-c6226c2<PERSON>ba", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "properties": {"otherSystem": {"$id": "6e60c8ff-5eda-434e-bc69-3971db8c9111", "type": "standard", "title": "外部系统"}}}}, "valid": true}, {"id": "947364e7-2a38-4573-bf09-45eaaadbce1f", "name": "otherSystemClassificationCode", "title": "其它系统分类编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "94d5f252-5489-42da-8a01-c6226c2<PERSON>ba", "valid": true}]}