{"id": "187a27be-5816-4891-8406-19626320027d", "className": "com.open_care.event.service_order.follow_up_link.OCFollowUpLinkAppointmentRecord", "name": "OCFollowUpLinkAppointmentRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01e622d2-7cbe-4958-a4e2-63cdc91b1d60", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "1687d1c8-b990-449c-95ef-bf83f39415e4", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "1bde1e7a-3360-432a-99c3-1b8fe80fe6d0", "name": "needAccompany", "title": "门诊就医是否需要陪同", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "2012ea43-48b5-4492-9171-f790c3b81079", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "就诊医生职称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "37ae5e81-2c8f-45f2-b297-5681dfc413fd", "name": "serviceCharge", "title": "医事服务费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "413ffcc2-0743-48da-a6d7-027ff0f9b1c0", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "530f5358-c4ab-4dd5-9fa1-8e761c6a2d59", "name": "needOutpatientTreatment", "title": "是否门诊就医", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "6361dc0f-a759-4a8f-bed9-23fb8da9f4cf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "797447ed-3b48-4530-8c13-f2ebcff19db7", "name": "outpatientTreatmentRemark", "title": "就医备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "7abdfdb0-352a-4600-a86e-990fa457ea48", "name": "accompanyDate", "title": "陪同日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "81f5b0bd-d18c-4cd9-a635-e0e9bb47a548", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "85ed3aa8-3bed-4d3d-af0b-d3da0f3840aa", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "8736b94e-7368-4ea4-82c8-55a685900ca3", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "87cada59-1f63-456e-bc8a-197475c596a3", "name": "outpatientTreatmentDate", "title": "门诊就医日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "954ed7a6-3bb4-42f1-981e-a9ac25746fbd", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "9c8af20f-3d65-46f4-8961-e88275a67882", "name": "doctor", "title": "实际就诊医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "9f6ef1c6-aed6-4dec-9aec-f06a940c479e", "name": "department", "title": "实际就诊科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "b5bdb3ed-1185-4a77-84fe-e88606570751", "name": "hospital", "title": "实际就诊医院", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "bc8faf56-b66e-40dc-98f4-c9f672718126", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "cf6f887b-c979-4061-93e4-9f368b7b1dca", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "d5958034-c904-4ab0-9f4c-e5da7aa9ecdc", "name": "registrationNumber", "title": "挂号单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "dc6570bb-b691-432a-9b8c-cac7b15b6852", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "e586593e-c700-42a5-ad27-62c712cab732", "name": "accompanyRemark", "title": "陪同备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "e803ebc6-3be0-4469-9c0d-94fa78b9a987", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "ea4635f3-507b-45fd-903d-99d7986c1017", "name": "needAccompaniment", "title": "是否需要门诊就医陪同", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}, {"id": "f1b898d4-5997-4f3e-8b95-449319e1b3d7", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "187a27be-5816-4891-8406-19626320027d", "valid": true}]}