{"id": "efc8c828-9ecf-4426-99aa-144927bb9f08", "className": "com.open_care.product.topmedical.OCChaseTask", "name": "OCChaseTask", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "013873ef-5d02-4747-9f1f-b4e48cd45531", "name": "insured", "type": "java", "classType": "object", "refEntityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "152d96ca-c5a5-4130-8cff-12d683d3361f", "name": "hospitalId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "167a86d5-8b1a-4d01-841a-85e5bd426855", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "1de575b4-38b4-4df6-9a9d-73277a1b95cb", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "20a16ec4-eb37-407c-a9ae-d1c6585c77cc", "name": "claimAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "23a26fc6-7ab2-45bf-97a4-aa3e21ddc210", "name": "purchaserPeople", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "332b8e39-a53c-4346-a8eb-ad97655c166e", "name": "gotUserId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "35b0a2d5-9052-4c4d-8fb4-feaa524eb52c", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "3609e74c-15c8-4eab-aa25-1a725063d0e1", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "36fcd904-0877-48a4-bafc-29ec78cf64f3", "name": "payWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "4d2b8591-56f7-4f22-8cbc-ebbaa831589c", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "57c4698a-177f-4f20-9a89-9a517438ec46", "name": "opinion", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "5e423637-f05b-482e-8dd7-4dc0e67c4c2d", "name": "batchNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "5eb4517f-04f5-4946-a2a5-f861b7e785ac", "name": "gettableTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "606dfa31-daf5-4e91-b340-1d25150538c1", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "6274b90f-ead3-4408-b1ba-efcd529913e2", "name": "caseCloseDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "700b1502-7dd3-47ee-9d89-302df1d49f05", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "733c14ac-171a-4204-b117-1e037063b7a5", "name": "ifSubOrgAgree", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "77d8b4a1-7c13-44b3-aa44-82aa58ec5109", "name": "vipSign", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "8aa96d80-8c68-4440-aa3e-c6417de64093", "name": "billAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "8e4894af-a938-4be9-b14c-60654b405c10", "name": "contactRecordList", "type": "java", "classType": "object", "refEntityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "9263766a-d90e-41fb-9953-9ae3d18d4ef1", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "9ba5224b-b190-421e-96ad-5a10a0f713c6", "name": "ifPbyByBillAmount", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "9e72bca2-a328-4874-9def-7d0b46cf52f5", "name": "finishTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "a7a85ed4-53f9-4425-bd58-2d2898d1318e", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "a801f7f0-f270-4f6b-875b-a1bad1c7709e", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "a8ab32a7-4a5f-4fcb-b605-cc32f0d9b90f", "name": "caseReportNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "aaba17b8-b0ea-4e23-b2a5-eb6ec0b050c6", "name": "taskInitDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "adcbd333-62ea-4f8b-973b-87ab5130d551", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "b8d8a9d8-cb6b-4a7b-9c2b-8494f57bff14", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "bbc4fca8-e102-45aa-9bf9-ccee6e7186b1", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "be861562-93db-4984-a998-fab33b6915cb", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "cd1178ea-a2c2-42d7-a0f2-4f38689de351", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "d139d86f-a553-4e60-a550-cc9e772f141c", "name": "feeitemType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "d271a488-e98a-4cbc-a3cc-43f0706250a3", "name": "clinicDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "d7d010bd-e3e1-419c-8289-5a75ad631ef3", "name": "hospitalName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "de3e39f6-5b65-4994-92c1-91f468f6df86", "name": "payTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "df0f2069-d3f3-42eb-bf64-7bb4952ea4a5", "name": "receiveTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "f23e9c9c-5437-4f1e-af07-49995fe18560", "name": "msgRecordList", "type": "java", "classType": "object", "refEntityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}, {"id": "f3c69856-99b7-4a7f-8d3e-c88535a0a02a", "name": "chaseAmount", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "valid": true}]}