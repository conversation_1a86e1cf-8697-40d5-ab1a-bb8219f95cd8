{"id": "ca4c22cc-ef3f-46b4-8989-bc864bc53d89", "className": "com.open_care.medicalMicro.medical.BaseRawExamItemResult", "name": "BaseRawExamItemResult", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "364fb133-517f-4b0e-86d9-c34551418d08", "name": "detailMsg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ca4c22cc-ef3f-46b4-8989-bc864bc53d89", "valid": true}, {"id": "9f67c6e0-312c-4b08-9636-9bbf1aa93e43", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ca4c22cc-ef3f-46b4-8989-bc864bc53d89", "valid": true}, {"id": "a2778f4d-28a3-4a4d-8485-558d4fd23b3a", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ca4c22cc-ef3f-46b4-8989-bc864bc53d89", "valid": true}, {"id": "ab62d3d4-8f7a-4b75-b2ea-b062e0c9c569", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ca4c22cc-ef3f-46b4-8989-bc864bc53d89", "valid": true}]}