{"id": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "className": "com.open_care.event.time.OCAppointmentDate", "name": "OCAppointmentDate", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "109f1c81-b146-4648-8404-89df576e3300", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "31584d64-c115-4ee6-be36-ab90a29a0654", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "3d73eec5-b33e-4038-ad8c-ccecb3b6799b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "74dbca32-fa6e-4db6-ae9e-1e70e1dbd2c2", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "848f830d-5f81-4adf-9386-1dc13f84abe1", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "97df3edb-c3b3-4f3e-9de3-fa8ad8a383a9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "9b2af649-0734-4b73-a3e6-276a38e149ca", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "b3d6076b-1c2f-4ec5-afe3-31872514a469", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "bcc73da5-b301-4c04-98c3-4ec4c24167fd", "name": "appointmentDate", "title": "预约日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "ced94066-c33b-4ab5-9c2a-25080ce009ba", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "ea5c2688-4c79-4489-8114-b6d2b19739f5", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "f6b5cbb6-85f4-4097-bd9a-187755a99431", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}, {"id": "fb9ef68e-9da8-4d1f-847b-6642b4130807", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e0a38630-5b0b-4d2d-ad08-66fcd001007d", "valid": true}]}