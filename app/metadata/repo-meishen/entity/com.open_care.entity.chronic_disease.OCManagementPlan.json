{"id": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "className": "com.open_care.entity.chronic_disease.OCManagementPlan", "name": "OCManagementPlan", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "07d07ec4-188f-4503-8bf4-50023820af86", "name": "appointment", "type": "java", "classType": "object", "refEntityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "10d65d1c-78b3-4f39-855f-38ce39860ffd", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "226b18bb-2cfc-430a-9079-bd9a3cf47cb8", "name": "exercisePlan", "type": "java", "classType": "object", "refEntityId": "c718bc14-f54c-43f3-bab9-da83a9d8eec7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "281e41ac-dc9d-4e5e-befa-2e0940b50b3d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "3477520f-ff5a-4356-ac01-e63b7ba716b4", "name": "managementGoal", "type": "java", "classType": "object", "refEntityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "3808783e-d320-4b87-8bb5-35361b3ab29d", "name": "lifestyleAdvice", "title": "生活方式建议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "4812ee50-5748-4cfb-9a68-9fca000ff310", "name": "planVersion", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "4dc7a68f-e737-44d5-a04b-f2331879b8c3", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "55e1fc17-a7e5-4bcc-9958-acc5c1ca5cd8", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "56fe9ca6-2340-40b6-bf4d-1fb871f7f801", "name": "educationPlan", "title": "教育培训计划", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "5a7f6d4b-6db5-46b5-b49a-4e46f694813b", "name": "dietPlan", "title": "膳食计划", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "5beb141e-e727-4c78-a869-8b11e1946723", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "5c185317-f761-451b-bead-e429eadae460", "name": "dietaryAdvice", "title": "饮食建议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "5c90f8f1-9a54-4280-b42f-6529ec5709ec", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "6275ab02-4968-414d-baac-7bd32a52b98b", "name": "followUpPlan", "type": "java", "classType": "object", "refEntityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "6e6b7a5e-c2dd-4c04-8071-af794a984115", "name": "notes", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "71846ba9-4d33-40bd-8e15-f242f440ca84", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "806eb202-d983-4919-89e4-40a102558714", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "97b6e308-f18f-4889-9761-d91f3da34af9", "name": "monitoringPlan", "type": "java", "classType": "object", "refEntityId": "78b19a66-202b-43f4-9af8-fbce41f35b0e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "9e8a19e2-b773-45df-84a5-1f05f142bb49", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "a74b815a-7eca-4bb0-abaa-a0f589b556eb", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "c4e630ea-59ef-4007-b15e-ead4e5d71948", "name": "planName", "title": "计划名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "c71df54f-fd96-4989-8476-55329ca68431", "name": "endDate", "title": "计划结束日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "c741ec6f-9246-48c3-a53a-e569a85e7878", "name": "planCreatedBy", "title": "计划制定人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "d8988e02-5848-42c5-bd68-51ab6c3a5beb", "name": "startDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "e3e4599b-1588-4bd9-9ddd-5da5cf52d568", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "ee0fe9dc-2240-43df-a660-ce7e7bcf96b2", "name": "medicalPlan", "type": "java", "classType": "object", "refEntityId": "4b2de9d4-4fe2-47f6-8147-74b97ee61bd0", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "eeed6cba-61ac-4e54-ad72-349c06cb5715", "name": "isActive", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}, {"id": "ef2258ef-4ee1-4d89-9736-5053035e034a", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "valid": true}]}