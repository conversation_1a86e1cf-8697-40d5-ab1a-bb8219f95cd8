{"id": "0dbde618-0709-4a3e-a36c-21b9c633511a", "className": "com.open_care.medicalMicro.physical.OCBaseDepartmentHisType", "name": "OCBaseDepartmentHisType", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "HIS科室类型", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0d3e64b0-0b2b-49f2-a53d-7bdf562afcdf", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "13cead8d-014a-4c9c-91af-4d198c1a7f43", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "2ea8c93c-3d77-4d0e-9a1d-aab06f45292c", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "32efd5dd-6af5-45ce-9a26-bfdc8d818333", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "3306339e-58dc-4f81-8210-4a6d7a02078d", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "3d82ce59-a71f-4993-88e5-c64bcb47ac95", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "3e0de8aa-68e4-4f66-985e-58c5df372002", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "4f3c88aa-ee17-4c05-91fb-91cf6059b4e7", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "63e4c2f3-1319-481c-854e-5f0fafafdcf7", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "6671a5cf-a56f-47d8-8670-b496178485f9", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "7d575026-c267-41dc-b79d-5fd2e585b9fc", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "81e74b5b-af1c-474e-a7eb-210fe351e2ab", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "8f4f7ddd-572d-4b14-91ca-12062be7ee0e", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "9e813aa9-67e1-4339-afbf-3bc2dcbc4f8c", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "aa4825dc-f43e-450a-a5c6-4e24c72e96b3", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "aa4825dc-f43e-450a-a5c6-4e24c72e96b3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "cb59590a-11c1-4d8d-9380-d4ff944d6fcd", "name": "auxiliaryDepartmentType", "title": "辅助科室类型", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "cec88d7a-4889-4f14-97ad-ab25808e893c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "d302696f-09cc-40fc-9e71-b048c8a209c0", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "e1289d20-cb4e-4193-b4be-a097eb8bc12d", "name": "diagnosticDepartmentType", "title": "诊断科室类型", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "e822fd9f-e759-418f-9c01-ea2e2783faf0", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "e822fd9f-e759-418f-9c01-ea2e2783faf0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f07e29da-e61f-4b27-8cea-5b24c7a51524", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "f0909d3b-a175-4ff2-a7cf-23a9f7a27fed", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "valid": true}, {"id": "ff62a869-3f56-4eb8-96f0-b39b3b56027f", "name": "parentHisType", "title": "父级类型", "type": "java", "classType": "object", "refEntityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4698614b-2ed9-4d19-95ec-653cfa07923f", "style": "Select", "entityId": "0dbde618-0709-4a3e-a36c-21b9c633511a", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"parentHisType": {"$id": "ff62a869-3f56-4eb8-96f0-b39b3b56027f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "父级类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}