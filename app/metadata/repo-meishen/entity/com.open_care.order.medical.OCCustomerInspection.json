{"id": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "className": "com.open_care.order.medical.OCCustomerInspection", "name": "OCCustomerInspection", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户检验单", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "011010b7-bc71-423f-b9eb-af314d72d96c", "name": "orderTime", "title": "订单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "096280e2-f8ee-441e-a06e-9a1c68add706", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "0964e9ed-044c-44ab-b311-10dab2b30896", "name": "highAuthorityUserId", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "0c04bc7c-b11a-47d5-bd3d-867892c085b8", "name": "productPriceInfoSnaps", "title": "审核通过后的产品价格快照", "type": "java", "classType": "object", "refEntityId": "d1b2e111-7825-4378-8137-cb6297029364", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "142a6f29-5ae6-4fda-97fb-37c64cd3d50b", "name": "orderItemsJson", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "16382e89-3332-48bf-8edc-6f2628f34ed4", "name": "attachmentList", "title": "附件列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "183ec65d-b275-4ad0-80bd-befc34b37c3b", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "1a8aad09-b5a5-4316-b52d-169ad4913cc5", "name": "customerSex", "title": "客户性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "1afa0809-e202-4a75-a1cd-01869bb91f6d", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "20208253-1921-49c8-be8e-bd99f8bc5754", "name": "paymentStatus", "title": "订单支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "242f7527-253e-4351-8093-4df5b79ec03b", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "25fb8f25-dcc4-4cee-a5fb-0b8e50e7f43b", "name": "meeting<PERSON>ame", "title": "会议名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "268b9dc5-4e14-4b82-9e00-290235739fa5", "name": "needUploadPdfReport", "title": "需上传电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "2b23702b-4cdd-4092-bab5-fd32b23143b8", "name": "advanceSettleDate", "title": "预结日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "2c5b5b84-bc40-4eb2-9104-dd4763a3fa3d", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "2dfdc725-3ada-4303-8db7-883c0eaf5463", "name": "orderNo", "title": "订单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "30bf0e24-9b5c-49ec-8d99-c835a1f10b7f", "name": "channel", "title": "渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "32a0e663-de94-4332-bc91-f726b355e66c", "name": "beneficiaries", "title": "受益人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "331d0100-dc5c-4401-977b-fc0eca13e7de", "name": "orderItems", "type": "java", "classType": "object", "refEntityId": "7b028dd2-f0d3-4b62-bc28-09489c72bd32", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "378047ef-3d7f-4960-a343-581ed9fb7528", "name": "orderItemGroups", "type": "java", "classType": "object", "refEntityId": "12386553-11c9-4e42-a4d2-b7c8014a8a85", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "3b7b933f-5233-4102-b683-0a293cae87a5", "name": "brandGroupName", "title": "品牌组名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "3e1af70a-1a02-4dab-b0c4-47da8ff27a28", "name": "partyRoleName", "title": "客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "3feb2afb-861d-46b0-89ea-3489b983bbc1", "name": "additionServiceOrderId", "title": "加项服务单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "3ff6cbf5-2218-4275-9db7-c192b88951bb", "name": "replaceOrderId", "title": "替换订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "425eee32-f1b5-47d3-ac2d-994c7078490e", "name": "profitLevels", "title": "权益级别", "type": "java", "classType": "object", "refEntityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "4343614c-8d75-4317-b10b-b9e90b960a98", "name": "serviceOrderNo", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "45cb3650-90a5-41eb-b904-19c60a544d9e", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "4a02e09d-b6d0-4048-b0c4-5ca1892844c0", "name": "qrcode", "title": "收费二维码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "4f85117e-edf3-47bf-8570-3fc9f4ab655b", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "5574513f-ca24-4f6f-b78b-6466c1f0b80e", "name": "agreementId", "title": "协议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "5705287e-46d1-4cdb-aba7-eb9bee267617", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "5c413e40-ea22-4e7a-824e-ca3e66b2184f", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "5f2fc587-27ba-48a1-a60c-05495b74517b", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "63471ec0-93b3-4bc2-8af0-aafbf919b997", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "691dc10d-1d72-4af9-ba97-b5cdb67edee6", "name": "sampleType", "title": "样本", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "6a6ce4af-4541-4aa8-87bb-c2a40d968c9c", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "6baf8953-425b-468b-af5b-22f9377be269", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "6e54c0e7-eeb7-47bd-ae2d-81a3a8ccee17", "name": "examServiceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "6f360677-c0fb-4dd8-9ad3-4139b4cd3865", "name": "owner", "title": "所有人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "7c67846e-c64d-4025-9a52-a2548a9cc201", "name": "emergency", "title": "急诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "7d0cb7f9-18e5-433f-ace0-cf84da946183", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "7e23e8cc-df14-45c9-adf5-28580e11c704", "name": "department", "title": "科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"department": {"$id": "7e23e8cc-df14-45c9-adf5-28580e11c704", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "808258fe-00fc-40a2-8eae-0b912d80bd0c", "name": "selfUse", "title": "本人使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "81239231-53e2-4178-a89f-05e9aacdb53f", "name": "cancelReason", "title": "订单作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "85f0725d-49b6-4b95-b476-34afd663d02a", "name": "orderName", "title": "订单名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "877a99c1-be94-40ef-8b7c-bac6fb05d1f5", "name": "serviceCode", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "87c47a90-ae1a-46a7-8630-dc0af34a3667", "name": "salesExpert", "title": "市场专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "87e1e827-0408-49fe-b474-0ea674dc835a", "name": "refundFlag", "title": "退费标识", "type": "java", "classType": "OCOrderRefundFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "87fc558a08e65ed030848e89c3e183a36266bf15", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "8cf4c589-1846-4122-b329-9d291be78d5f", "name": "salesStrategyId", "title": "销售策略", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "9086cd44-5bf2-4a69-97e3-c3f71bfdcf38", "name": "paySource", "title": "付费来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "92dc6e55-88fd-4693-8ba6-7e93b0245515", "name": "saveType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "93aae504-b488-4e11-923d-a5f267deda53", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "94165622-444a-4b39-a5ba-51245c7d6adc", "name": "orderFlag", "title": "订单标识", "type": "java", "classType": "OCOrderFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "06eb12b855e66cabf22c3772eea900e9746ca8e0", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "96268053-c808-4ba7-9552-ecadeb6b1058", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "98fad31b-d597-4d13-8ede-4a72233baca6", "name": "parentOrder", "title": "父订单", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "9ee32f81-0374-456c-bcd9-09c4174f8670", "name": "note", "title": "说明", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "a05fc48e-204d-4af6-9458-47359afea6f7", "name": "enterpriseCustomerId", "title": "企业客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72666", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "a2ddfc8b-aac9-4863-9a73-2574d11b3d54", "name": "orderLocation", "title": "下单地点", "type": "java", "classType": "OrderLocationEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b4354294f6b84222c01e0d2e7f1b13777edb09c7", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "a5173128-3bd4-45a4-a439-c7475c743190", "name": "branchId", "title": "门店ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "a5bf4785-6894-4ecd-808b-09dfc778ead6", "name": "settleTime", "title": "结算日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "a65e16a9-30aa-4fe2-8cdf-8bb41317b030", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "a8e7d234-cf15-4280-86e8-df60e0602ac2", "name": "seller", "title": "销售方", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "ab133ad4-21ed-4d23-be08-4fc9a587e1b4", "name": "orderType", "title": "订单类型", "type": "java", "classType": "OrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ce7f095131e8e2446b2a9a9c184e62dfb78ef8d7", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "b006e031-44de-4c2c-bf3d-b750b65cec81", "name": "children", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "b9690b7f-8b80-4ece-81a6-6fe3e483c6c5", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "bccdca0a-95ca-48bc-a210-37dfc4369d40", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "be2043f8-61ef-4f36-b18a-7c8164ebe058", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "bfb02ce4-a39e-404d-9156-fe99c1e92ad2", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "OCOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c91e35a85e7bca35cb090a6b23a9ad875e31951f", "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "c0c5b98c-9d61-4c3f-aec8-8f52b734be2b", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "c33dc4e4-e0ad-4814-893d-644008cec302", "name": "reasonForApplication", "title": "申请原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "c41f19d0-e63e-41c3-97a0-aedf79117d23", "name": "orderAccount", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "c54c97c9-7e37-44b9-aadc-647f5d2c4def", "name": "cancelInfo", "title": "取消信息", "type": "java", "classType": "object", "refEntityId": "cd2543df-8799-48e5-8a37-a870977a9a19", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "c54cab28-9e3f-4a87-aadb-b34de332a648", "name": "sign", "title": "标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "ceb4d413-f80e-4bff-a450-b10be71ce777", "name": "hospitalRegistrationId", "title": "挂号单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "cf76a4e4-c310-420c-b620-13c02bde9a78", "name": "customerMedicalRecord", "type": "java", "classType": "object", "refEntityId": "f6727162-3cf3-4411-9eda-99e6c5c47157", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "cf7fb197-7a79-4669-83be-1fc370aead26", "name": "refundOrderId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "dc1208bb-848f-46b2-b0f5-3795d06e8fc1", "name": "sourceDepartment", "title": "来源科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "de678ac3-a258-4028-a4df-c6972a640f02", "name": "rcptNo", "title": "预交金单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "e23680e7-d373-440e-bd3a-781c95a527c7", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "e2ad66fd-d793-458a-bb35-143a579e2c8b", "name": "brandGroupId", "title": "品牌组ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "e3bba0e9-e30d-44f3-a579-1e72897249e3", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "e4dd852f-26bb-4dac-b6eb-dd4e925dd099", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "e66df5c3-d0fa-499c-8db2-31f8822a720d", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "f0e8cbab-4141-4baa-9916-31e81b7e6f5f", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "f2887b46-23e8-4869-a562-485b968d4638", "name": "parent", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "f625a13d-77f7-44c4-8726-70c0654d5d0c", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "f97a3fd0-3d6e-41d0-afe9-ea421c6efea0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}, {"id": "fec7fae2-816d-4e74-bb74-9d35e49387bc", "name": "print", "title": "是否已打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "dd2cfeb5-a2ed-49c7-b21a-09f1dc54ecb2", "valid": true}]}