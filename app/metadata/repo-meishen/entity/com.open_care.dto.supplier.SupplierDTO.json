{"id": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "className": "com.open_care.dto.supplier.SupplierDTO", "name": "SupplierDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0945c22e-798c-47bf-89d4-e3f8a315152a", "name": "supplierType", "title": "供应商类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "97d3fbf3-9e79-4bf3-84fd-acc09a8a7644", "style": "Input", "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["supplierType"], "properties": {"supplierType": {"$id": "0945c22e-798c-47bf-89d4-e3f8a315152a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "供应商类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0fb2d5ee-ceae-4bac-a6fa-5bace917c401", "name": "tags", "title": "标签", "type": "java", "classType": "String", "refEntityId": "644b506f-df71-4704-8019-b454cc0b4ade", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "0fe190a6-57ba-4a49-8ae2-71b9edbdbb8d", "name": "addressAreaString", "title": "供应商地址详细地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "11254b0c-7d41-453c-b40e-415c7440d791", "name": "updated", "title": "更新日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "13bfb104-f4eb-4032-9e52-9e42250e9ef3", "name": "officialEmail", "title": "官方邮箱", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "18a0f099-d2dc-4965-a7b6-b634fc89fb63", "name": "flag", "type": "java", "classType": "SupplierFlag", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "266931ab-b5a0-4fc8-af84-5113d197acd1", "name": "created", "title": "创建日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "27fdcfba-4d88-4e3f-9f90-3054bb3d5733", "name": "supplierNo", "title": "供应商编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "28d2ab11-db5e-4b7e-816b-258b5eb49d1f", "name": "created<PERSON>y", "title": "创建人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "2a2dfee1-d449-473b-98a7-ee5d9e515088", "name": "regionNameDTO", "title": "省市县中文名称", "type": "java", "classType": "RegionNameDTO", "refEntityId": "634b3805-e610-4a96-9a1f-ac18652939aa", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "2f213236-8936-4b72-b8d3-f2e773c4703b", "name": "thruDate", "title": "有效止日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "3195f40f-df0c-4bed-9f34-297cbf5b0217", "name": "updatedBy", "title": "更新人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "3a7a5a35-2bfa-42db-8b79-8ae99d087587", "name": "otherInfo", "title": "附加服务说明", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "3b563f14-8787-4dfc-9c4b-2a7f6ab1665d", "name": "startProcessType", "title": "启动流程的方式", "type": "java", "classType": "StartProcessTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "444d8a15-c3bc-4c58-a281-a26a8fb6d65b", "name": "licenses", "title": "营业执照", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "48e38d9f-a82f-4ebd-8308-314eaf54a6ba", "name": "supplierFirstCategory", "title": "供应商一级分类", "type": "java", "classType": "SupplierCategoryDto", "refEntityId": "59e50dee-be73-4755-aebb-017bcb2ba471", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "4c3890a6-f8dd-4e22-a99a-4259ffef075a", "name": "supplierPersonList", "title": "供应商联系人方式", "type": "java", "classType": "object", "refEntityId": "903eb2c3-29a0-4f91-803a-920c663ef980", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "50c06639-db3d-4dcf-8590-48956ee7144e", "name": "fromDate", "title": "有效起日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "525251aa-7c5d-497f-ba4f-2d6969a9d5f6", "name": "chainBool", "title": "是否连锁", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "55fdf3c5-f0c8-48f2-abb9-34baf2653da2", "name": "auditStatusJson", "title": "审核状态", "type": "java", "classType": "object", "refEntityId": "9a815837-52c2-4449-9fdb-c0271fd3c337", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "560641e3-1030-4e4d-877d-d722ee884689", "name": "parent", "title": "上级供应商", "type": "java", "classType": "SupplierDTO", "refEntityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "56ecab29-13ec-4d5d-867b-478f82ac6c02", "name": "officialPhone", "title": "官方电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "605e912c-c0df-48b7-99ba-57f8926ed82b", "name": "attachList", "title": "附件列表", "type": "java", "classType": "object", "refEntityId": "c844322a-52f0-4d7b-ae61-921b5e4b7b7c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "60f3eb4a-ff9a-47ef-a4d2-3b1620630307", "name": "specialSuppler", "title": "是否特定机构", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "6d8f8b7c-9a3d-422c-8399-7d8dfb570de5", "name": "medicalLevel", "title": "医疗机构级别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "867ea461-d054-454c-8174-421357b41575", "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "73a9cb36-5f4b-45f8-8b1a-67817b3dfbef", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "78c462ea-3e45-4483-bbc6-4522ed21ed75", "name": "supplierEnglishName", "title": "供应商英文名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"supplierEnglishName": {"$id": "78c462ea-3e45-4483-bbc6-4522ed21ed75", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "供应商英文名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "********-7a31-4171-97e4-8be46da64acb", "name": "serviceAccounts", "title": "合同关联信息", "type": "java", "classType": "SupplierAccountDTO", "refEntityId": "659e04f7-47de-4d51-855b-e81e1bde5f9f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "8b8b5a7d-d30a-42ad-8884-b51b1b6fe341", "name": "serviceBranches", "title": "供应商服务门店", "type": "java", "classType": "ServiceBranchDTO", "refEntityId": "3ac1959d-2119-4482-9216-099c538aa87a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "aa12de47-561b-44ef-b1c4-0af5e5b2f42a", "name": "medicalCategory", "title": "医疗机构类别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "d0e2facb-c0e5-47cc-a6f2-9ed52d9e04c5", "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "bd2cf50e-fb8d-410b-b2fe-1d9ca8bcc6bc", "name": "supplierStatus", "title": "供应商状态", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "ca9ab2ba-868f-4b82-81c0-8c3ed6c82e93", "name": "addressJson", "title": "供应商地址", "type": "java", "classType": "object", "refEntityId": "e9097e72-d407-4a68-b6ca-94065614e460", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "d8676769-77c1-4ea2-8f7e-3e5d182d7d82", "name": "detailInfo", "title": "详细信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "e3f21ad1-1a50-41bd-9b01-2dfa47f092c6", "name": "specialistCategory", "title": "专科类别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2d0a978a-b3df-413f-a351-adf3314deb43", "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "e6f02232-872f-4f7e-8905-813e99df2fba", "name": "profile", "title": "简介", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "e759bc09-5d0c-4cd3-a869-12eb091201e3", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "eb061520-0fb8-48f3-ab18-5a88fe65d41f", "name": "supplierName", "title": "供应商中文名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["supplierName"], "properties": {"supplierName": {"$id": "eb061520-0fb8-48f3-ab18-5a88fe65d41f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "供应商中文名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "edee08c5-019b-4b59-842a-21c7b2828a8b", "name": "supplierSecondaryLevelCategory", "title": "供应商二级分类", "type": "java", "classType": "SupplierCategoryDto", "refEntityId": "59e50dee-be73-4755-aebb-017bcb2ba471", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "ef3cbbc5-ad81-4a22-9efb-30235289df0a", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "efbe20bd-e664-4831-a01a-320e63743706", "name": "addressList", "title": "供应商地址CodeList", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "fbdfc5f5-1acd-46ee-9724-ad0332ec997a", "name": "supplierOrg", "title": "机构类型", "type": "java", "classType": "SupplierOrgDTO", "refEntityId": "6d48ce2a-300c-41b5-bcc6-c397688f63a1", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}, {"id": "fdcf27ba-4d88-4e3f-9f90-3d5733054bb3", "name": "socialCreditCode", "title": "统一社会信用代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f787d17c-ff7f-4e5e-a2be-c4c5bc95e5db", "valid": true}]}