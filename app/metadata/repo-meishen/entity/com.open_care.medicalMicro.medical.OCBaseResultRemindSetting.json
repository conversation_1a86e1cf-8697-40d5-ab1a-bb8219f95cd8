{"id": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "className": "com.open_care.medicalMicro.medical.OCBaseResultRemindSetting", "name": "OCBaseResultRemindSetting", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "结果提醒", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "089a07e7-fcba-4611-bfed-3050120dd686", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "301a6808-2daa-4949-98f3-58e68a8e9aa1", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "3e51b7b0-3955-4bfb-ba48-6fb583a89179", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "449449e9-8e7c-404f-b510-71c009c56f8c", "name": "remindMsg", "title": "提醒话术", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "4786c8b5-9ebb-49b9-93bc-764d9c147414", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "47a27b7c-9abe-4b49-b44a-e8fb973348bf", "name": "baseDiagnoseICD10s", "title": "ICD10结果提醒", "type": "java", "classType": "object", "refEntityId": "e87de778-8520-4460-860a-cfaa92e4efdc", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "403c8624-eab5-460d-ad26-29f9a0623fd0", "style": "Select", "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseDiagnoseICD10List": {"$id": "47a27b7c-9abe-4b49-b44a-e8fb973348bf", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "ICD10结果提醒", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4d903754-2ce7-49b0-a971-480c02d20960", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "5a928bc6-acb1-4ffc-8c2b-fc311111d2c5", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "6af229dc-dcac-4b80-90da-7c935ec2753f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "6e1caa43-c8fc-47c0-8d86-9c51f6e17eb5", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "7443a060-d19a-4293-851e-9dcdc7f691ae", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "91aeafc5-d1db-4cb0-a532-688e2155bb1a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "95a276f3-07b0-48c6-be10-b8c2d9237500", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "b9441b6d-305f-4c2e-b881-fb57c9962d34", "name": "baseConclusions", "title": "结论词提醒", "type": "java", "classType": "object", "refEntityId": "33adf15e-809a-42c8-bfb7-667c1c086445", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "0d92d857-e3b8-4180-9067-6d4a5999490e", "style": "Select", "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseConclusionList": {"$id": "b9441b6d-305f-4c2e-b881-fb57c9962d34", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结论词提醒", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "bf0198ba-82d1-45d7-878d-130aec32be22", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "c2a72df5-d768-4de2-b5c1-0518f6fb10f5", "name": "otherMedicalMaintenances", "title": "其他医学维护提醒", "type": "java", "classType": "object", "refEntityId": "16b05aa2-0b08-4f32-ab19-bbb179590f64", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "8f813bde-146f-4dc9-a50c-fd9da7901bb4", "style": "Select", "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"otherMedicalMaintenanceList": {"$id": "c2a72df5-d768-4de2-b5c1-0518f6fb10f5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "其他医学维护提醒", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c5eeb264-7f75-48af-bd90-5d36a3b07eae", "name": "baseDiseasesResults", "title": "疾病提醒", "type": "java", "classType": "object", "refEntityId": "09d8191e-229e-4220-8cb1-c37f18b1c970", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "f65f3a7a-507a-4128-ae20-e603cf752920", "style": "Select", "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseDiseasesResultList": {"$id": "c5eeb264-7f75-48af-bd90-5d36a3b07eae", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "疾病提醒", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c9bcd62c-868a-46bc-8c30-23551242977a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "d1036064-fe14-4bde-84be-797ab769ba58", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "d87a56b7-22dd-4df0-8082-435fc01436de", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "d87a56b7-22dd-4df0-8082-435fc01436de", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d8fa2c65-8b9d-4da5-b17a-21d08c45ef54", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "e079be51-5b74-477d-864e-35493713bdc0", "name": "departmentRemindSettings", "title": "提醒科室", "type": "java", "classType": "object", "refEntityId": "e0ba9b89-8557-49ea-82eb-3cfc40be75cf", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "e5d73acb-58cb-4071-b08b-a22628d86758", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "ee6931b0-48bd-437e-a96a-067f904560c9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}, {"id": "f7db6dbc-007e-4264-b7fd-01430ac16939", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "f7db6dbc-007e-4264-b7fd-01430ac16939", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "fedee06b-4343-4fbf-802d-ec1defeeb9aa", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "938daabe-e6a9-47ae-bb5c-7ac0df382ae4", "valid": true}]}