{"id": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "className": "com.open_care.sys.OCVersionable", "name": "OCVersionable", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "03d62a5d-3600-4071-bd22-27c1ddb24ccc", "name": "entityInstanceUid", "title": "版本公用实体ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "0779e1d6-e821-4208-a878-63bffef5a432", "name": "currentFlag", "title": "版本状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "091c0eeb-885d-4e17-ab95-bcf998d7e957", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "18da4463-b930-43d9-acce-771e8e91a8fb", "name": "genPublishVersion", "title": "是否生成发布版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "254fa920-3226-404b-9b78-bade8d831039", "name": "shallowCopyFieldNames", "title": "浅拷贝字段属性", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "2ac056d4-0ea7-403a-8da7-52f2420964c3", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "47aa8770-330c-4450-972c-bb1de7b2fd8c", "name": "versionEnable", "title": "是否启用版本控制", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "4e7723c9-2a18-435c-8876-54f200df3edd", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "4f8afb14-1e00-42e3-8c88-de4b101813e9", "name": "genLatestVersion", "title": "是否生成Latest版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "4fd7cb56-611f-4862-b4dc-442bf85feb38", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "53981c00-17fb-4991-a6ec-5ad1273a7d54", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "54d6fefe-9206-4d55-a9de-1b32b7e2045a", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "6ba4d82d-1a85-4b57-b695-c8943fb59c3e", "name": "versionEndTimestamp", "title": "版本有效结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "6c60f4ff-a02e-4eac-b7f2-3d8790321234", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "8dc2bb64-36bf-48b0-88f8-bd10a4ab6fdf", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "9c33b1ac-b9be-432b-be08-913f6b67ae4d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "be7e8750-7a93-47bd-8e0a-8f26bf543047", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "c80d72c2-4a03-4e17-be6c-6373a74ce828", "name": "entityInstanceVersionId", "title": "版本号", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "ce4f54dc-8dbb-49f8-9586-94aef7c99c9d", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "d95601c5-2fe7-4a73-b991-3dd41b74ac33", "name": "versionStartTimestamp", "title": "版本有效开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}, {"id": "e4f5c0a7-4a92-423d-ae4c-07c22b99e590", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b5570c49-da9e-4615-b10c-4a0bd853b3cf", "valid": true}]}