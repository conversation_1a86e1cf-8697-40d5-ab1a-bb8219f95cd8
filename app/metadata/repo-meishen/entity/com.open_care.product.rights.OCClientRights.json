{"id": "7472a688-99f4-4404-9c4f-7bc6156461e6", "className": "com.open_care.product.rights.OCClientRights", "name": "OCClientRights", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00963f15-6277-41a6-9744-49ed65c2d374", "name": "rightsFromApp", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "0359d4c5-17a1-417e-926f-ba5d42913d8f", "name": "productPack", "type": "java", "classType": "object", "refEntityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "07026006-e1fb-4a49-82b6-349623fcc8f1", "name": "nick<PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "07ca665e-8745-497d-909d-513c0517d34b", "name": "versionEndTimestamp", "title": "版本有效结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "0c0e5d2c-a965-4d6a-865a-9c3f0e58159a", "name": "dateEnd", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "0e47a4c3-5a8c-40c0-be16-7bb58a0344e0", "name": "directPayStatus", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "117863c9-c7fd-4d81-97a7-d983cc90f427", "name": "salesman", "type": "java", "classType": "object", "refEntityId": "86a7d68f-a0a8-4683-83d7-365e024dde4f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "1242a2fa-d80e-457a-9e87-6b155ed84813", "name": "auditStatus", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "1554d9ed-da91-4655-923c-b7d9b23dd34f", "name": "salesUnitPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "1880ae02-d71b-46b2-87f5-533b05a28090", "name": "rightsFrom", "title": "权益来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4bb7734e-878b-4f37-9941-e66c812831a8", "style": "Input", "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"rightsFrom": {"$id": "1880ae02-d71b-46b2-87f5-533b05a28090", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "权益来源", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "1c921502-365a-4024-af6a-9b8fff8d3266", "name": "ifRightItemFixed", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "1dcc5f3d-3e0d-4400-ae3b-97716b587e24", "name": "entityInstanceVersionId", "title": "版本号", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "1ec7f3c7-fddb-4848-b041-a01268222563", "name": "rightsFromWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "1f0f46b1-46bc-4dde-9319-b5326933b94b", "name": "var5", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "2180fa83-08b7-4df6-9766-b9b3c6e61bfe", "name": "dateStart", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "24b0d209-79a0-4758-970b-63061296f57b", "name": "ownOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "25076a38-60ae-4ff1-91cd-c34a10271917", "name": "beneficiaryId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "279ae756-ff7d-437a-af53-8f048a43a74e", "name": "ownerId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "2c6b4c23-850e-4c4e-b7af-e81b3d7b77cf", "name": "levelId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "34441804-ce8e-4dd1-bb04-1397ceef9739", "name": "orderSubmissionTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "35c57a14-76e7-40bf-a97f-63da4ac6631f", "name": "var2", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "37b99ea2-6381-41cc-b25f-f0d9de5ad354", "name": "var9", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "38ca404d-b15a-4547-bf6d-817f3dbb6713", "name": "ifSelfOnly", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "3d10f402-1678-455c-a075-ea6f307e206c", "name": "cardNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "3f01bde3-5c12-42ce-af4e-bdfc2cf8c27f", "name": "var1", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "406d4588-591a-4810-be19-e95d0d0c53ee", "name": "enterpriseId", "title": "企业ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "411d334f-3cf1-453a-9883-d40b53134ca6", "name": "orderNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "46748692-5d61-4dca-9008-f2d5f7ac6a63", "name": "var10", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "48a0e8a2-e570-4da0-ae26-fb69ef3f44a1", "name": "pensionAgencyCode", "title": "管理机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"pensionAgencyCode": {"$id": "48a0e8a2-e570-4da0-ae26-fb69ef3f44a1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "管理机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4c74458a-f5b5-4ce8-a2c1-f775e2641ae0", "name": "purchaserId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "508f941b-8a07-44f6-ab66-09ef013feb9a", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "5d3640e7-0b7d-49ba-af56-3936e4180a4c", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "5ea86b9a-ea42-455e-bf02-be47972ed052", "name": "purchaseAmount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "60dcbc3e-2565-468c-9eed-b65c9e0fb0b1", "name": "enable", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "638e4fef-ed5a-41d7-834d-e3deb64f53d1", "name": "clientProject", "type": "java", "classType": "object", "refEntityId": "562237b9-2940-4f99-9f77-eef50035cf02", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "65fe5740-d803-41d3-99ab-a19949002714", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "67240def-709e-4878-b4fd-85d9d189b2e0", "name": "rightItemEBAS", "type": "java", "classType": "object", "refEntityId": "18d6e604-f980-465f-a8af-b84c1783e662", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "6afd124e-a405-4e9d-aedd-788ef1c7b1c9", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "6bce9df7-d84c-44ca-8b6a-934774fdee99", "name": "versionStartTimestamp", "title": "版本有效开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "6d7d9a8f-0748-409c-9713-c0e361743da9", "name": "var6", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "71c76573-92fe-4a1d-b0b7-cb952bc58985", "name": "ifNeedActivate", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "73ee7419-6591-47d6-b21d-12eb30881dab", "name": "beneficiary", "type": "java", "classType": "object", "refEntityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "75783557-cab5-4d16-8227-7b66bc098e67", "name": "var8", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "7be20161-ab3d-4951-a329-7ce7ee786cfe", "name": "var3", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "7fa1d82d-3517-4ff6-a032-76ce0b54f031", "name": "sortNo", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "824db1b7-1861-440b-8252-bad1db21e526", "name": "ifSupportPick", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "8302cec9-07d4-49bd-a47e-b9f94f583cdb", "name": "relationShip", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "83285890-5026-4d52-90cd-a170eecf37ed", "name": "orgPurchaser", "type": "java", "classType": "object", "refEntityId": "6b3bca44-b285-42a6-a8f2-e8670e722b7a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "834d20a2-0950-4ea1-8440-f93be4231318", "name": "rightItems", "type": "java", "classType": "object", "refEntityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "83bbec82-438a-4197-b426-7f74bc5099ed", "name": "totalPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "8449c9ed-8f79-45a8-8ad5-4c52916c7eba", "name": "enterpriseName", "title": "团单所属企业名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "84eb3290-ddd8-45ae-9b55-0c4c796644bd", "name": "owner", "type": "java", "classType": "object", "refEntityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "85684102-1d26-458f-ac7a-2d783c199a4f", "name": "beneficiaries", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "858a0679-a40e-4ace-9d59-4e8dadd3769b", "name": "note", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "868d7182-c88f-418d-9729-2128c8e1c35f", "name": "ownership", "title": "权益归属", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "880aac94-1a08-486b-b599-872a1dd3a65f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "887c30c2-2390-435d-9693-e50bb2c13fa3", "name": "genPublishVersion", "title": "是否生成发布版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "8905246f-367b-4a96-bc04-2ea83a4cc901", "name": "cutWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "8b9715eb-525f-4ca4-879e-743f98dd53de", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "930c948a-ff80-41d6-9e67-601c33053955", "name": "rightsAuditNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "9340ef24-7180-4e37-84e9-30f6546da87d", "name": "var7", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "934d0167-e9f0-4e00-bd63-81a51c355fd9", "name": "rightsObtainMethod", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "94ca2c97-ccf1-4722-8f3d-38a17e5fef6c", "name": "currentFlag", "title": "版本状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "9874710c-eeba-4413-8648-97a018778827", "name": "amortizedCost", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "99aa9eb2-ec83-4c83-8bd7-7ea4bd1c4dd4", "name": "clientGetDatetime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "9e2a7a0a-34fb-42c1-8497-3d8dd9247c40", "name": "settleStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "a0f585a7-5b95-4f67-88e0-ba2f00644177", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "a3380045-7ea7-4873-a569-5fa2f2e061f6", "name": "productPackName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "a4e93fc1-4daa-4b29-86f9-ed365c63ba7b", "name": "var4", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "a581aff7-87eb-49d4-ab7e-1e12df1b9f47", "name": "planId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "a7074393-7539-49b8-997c-5d46529256d5", "name": "activateEndTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "aa9ca7e9-155d-47ea-bddd-287acc4c716d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "abc4e415-8e7c-4c12-a819-59fcfa916e55", "name": "dataFromRemark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "ae2a9192-abfd-44e5-b7bb-f8d5e2dba856", "name": "genLatestVersion", "title": "是否生成Latest版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "aedd3a36-2a8e-4fc7-83fe-93dae1f494eb", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "b45d4943-f739-4ebe-9058-ab0fd900d5f9", "name": "supplierOrderId", "title": "供应商系统订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "bfa0e607-ff9b-4057-ae5a-36f21bb8a991", "name": "dataFrom", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "bfd665bb-ee48-4e10-9928-36c889a92474", "name": "deliveryMethod", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "c2da9476-8890-4cbd-a55a-095d7b64f0ca", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "c40bb055-b30a-4a8f-a280-1af2cd3de4ab", "name": "channelSource", "title": "渠道来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "cb663626-7833-405b-9cc3-ea52ec40fe74", "name": "payInfos", "type": "java", "classType": "object", "refEntityId": "b4b70eb2-ad0a-461e-9e4e-f1464279c4c2", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "cbb8295c-0c46-44c8-8c5d-b05dcca5babf", "name": "payStatus", "title": "付款状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "cd1ba4f4-eb2c-4a4d-9cd2-91963018e9a0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "cd6a5e96-a757-4115-a8fd-281648e0f1a7", "name": "versionEnable", "title": "是否启用版本控制", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "d1319510-ac5a-4c0c-b1a8-e7fd889a90e3", "name": "ifSupportTransfer", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "d205a557-a79e-43f7-ba81-8e0ecdcbdc25", "name": "purchaserType", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "d23faaeb-4e2c-4898-8d9e-6ae98331edab", "name": "orderPaymentTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "d37b9b05-1f05-4ae5-95eb-33a60c24ad84", "name": "refundReason", "title": "退款原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "d937000e-a642-4bb3-bc20-80596d89c82b", "name": "purchaser", "type": "java", "classType": "object", "refEntityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "e0016dc1-d36c-4333-8d00-073f63cbf00d", "name": "activateTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "e51c2bb7-48ec-4304-9bd9-6779328e352e", "name": "rightItemSales", "type": "java", "classType": "object", "refEntityId": "2d22d9cb-7be8-4f37-9d62-41b6a45ad36b", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "e6c457f2-bd82-4c87-9840-bf6a655a7cc8", "name": "clientRightsBeneficiaries", "type": "java", "classType": "object", "refEntityId": "7f641b56-782d-4421-ac6d-b9fbbb606dcc", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "e710f133-869b-4d2d-93ce-23fa1672bbe6", "name": "shallowCopyFieldNames", "title": "浅拷贝字段属性", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "e756b157-d007-4b44-aa40-277bb3dcca54", "name": "entityInstanceUid", "title": "版本公用实体ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "e83ca23a-0c95-41af-a52e-80bbf1db1fbf", "name": "directPayment", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "ea345f23-a02e-4252-a7de-bc615383eb76", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "f00afafd-8d67-48c5-a459-d462752085f1", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "f08de47e-234f-414b-a055-431543408ee1", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "fb216d02-4d66-4f42-abf8-8509f08969cd", "name": "groupPolicyNo", "title": "团单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}, {"id": "fd062d90-d0c6-40eb-857f-96132ee448b4", "name": "refundTime", "title": "退款时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "valid": true}]}