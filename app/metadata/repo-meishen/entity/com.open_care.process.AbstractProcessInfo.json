{"id": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "className": "com.open_care.process.AbstractProcessInfo", "name": "AbstractProcessInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "09b3ca10-5a77-43e0-b298-1a1a924dbfaa", "name": "variables", "title": "流程变量", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "0af3ed45-b1a6-4944-b4e7-9b5b16d0f28f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "1617be28-7dbe-4c17-aece-7f5f7df514f1", "name": "lastCommitter", "title": "流程最后一次提交人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "16df30c5-2a6b-4c0b-80f0-27752831c5f2", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "273aedbe-cbe1-472c-b25b-439790e9f1cf", "name": "processBusinessType", "title": "流程类型", "type": "java", "classType": "ProcessBusinessTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4de256127195d130bc32c47c46b8e352e1137882", "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "2c008660-19df-48e6-94b3-1c613dfe82ca", "name": "className", "title": "关联类名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "32309c95-a020-41ec-a228-3b03a045b02f", "name": "businessKey", "title": "业务扩展字段", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "3ca9f925-0098-4c7e-b8dc-2c549d400626", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "48d28ec2-40c7-4eee-9302-53bda8287ae6", "name": "handleResult", "title": "任务最新处理结果", "type": "java", "classType": "object", "refEntityId": "c685dd30-8acc-4a38-a5b1-8c498589f867", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "49f7cb6d-bffb-4ee2-bbd9-0878a1561681", "name": "taskInfos", "title": "当前任务信息", "type": "java", "classType": "object", "refEntityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "639b1932-d1c8-4016-905b-4c94ea4fcaf9", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "64e07e9d-82e5-4c76-98dd-392b0a8c2c48", "name": "commitTime", "title": "流程提交时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "67003a74-83ad-46d7-91aa-c13808500dd1", "name": "committer", "title": "流程提交人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "678db1bf-02ff-4c90-b423-3473d9cde758", "name": "processInstance", "title": "流程信息", "type": "java", "classType": "object", "refEntityId": "dd9ca923-48c8-4be9-ac1e-080723343dac", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "6e1004c9-b4b5-4b92-91a7-d700d31b5e52", "name": "lastCommitTime", "title": "流程最后一次提交时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "7931c275-27d7-429a-ab03-6c28e259fd16", "name": "processNo", "title": "流程编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "7f09d94a-6083-4979-a0ef-e30399e66d85", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "804cd71a-b9a6-432f-bae5-c391f99cca97", "name": "taskHandleLogs", "title": "任务处理记录", "type": "java", "classType": "object", "refEntityId": "9bc9c1b8-37e1-468a-95da-f8e2c680c729", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "8a161c86-90d3-46ab-ba06-47c2647369ba", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "8a62f8f6-7193-4544-a799-f40b60f04206", "name": "processTitle", "title": "流程标题", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "903a119c-db00-49a7-a0e0-cc682bcec58f", "name": "source", "title": "流程来源", "type": "java", "classType": "WorkOrderSourceEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1c4dfd922dae2d24e5c7384940f61def2795f507", "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "980d7a93-1a0c-4a15-94cf-cb42237736a6", "name": "entityInstId", "title": "实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "ab81ed3a-fcff-4b40-9644-08707bc11605", "name": "startingVariables", "title": "流程开始时的变量", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "b5383a37-d4ff-4a23-aa83-5f7017470aea", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "c1aad105-c582-4f29-9578-37418d299b33", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "d6d1d574-d579-4037-8156-8d36def92827", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "dda92348-db12-430c-aaf3-750f5aebf360", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "e238a4ac-9738-4ff0-83fe-9ae293b77ef5", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "e7ade5ac-940b-40ad-b6cf-48957c7e034b", "name": "attachments", "title": "附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "e8b79fab-b0b6-4085-bc81-30703e18cfb7", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}, {"id": "f774e0c1-6615-4ad9-97df-762ae83e5219", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b0b83fe4-9b28-4066-afc4-7a2224b6d716", "valid": true}]}