{"id": "306b7c68-7194-4a6f-b109-8769e33d7c37", "className": "com.open_care.dto.scheduleManagement.ParticipantDTO", "name": "ParticipantDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "716b272a-e556-48e5-bf35-b97d15fb8047", "name": "username", "title": "参与人员用户名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "306b7c68-7194-4a6f-b109-8769e33d7c37", "valid": true}, {"id": "891e541a-e40d-4e15-a35e-5a5b05ab9eb8", "name": "name", "title": "参与人员名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "306b7c68-7194-4a6f-b109-8769e33d7c37", "valid": true}, {"id": "e4988f58-99be-4ceb-a210-6b47ecd2003e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "306b7c68-7194-4a6f-b109-8769e33d7c37", "valid": true}]}