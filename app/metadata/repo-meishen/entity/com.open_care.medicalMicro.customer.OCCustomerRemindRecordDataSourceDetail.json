{"id": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "className": "com.open_care.medicalMicro.customer.OCCustomerRemindRecordDataSourceDetail", "name": "OCCustomerRemindRecordDataSourceDetail", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "20c931b8-baf4-45d4-a9ea-496065bc89a6", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "23a84c28-cfbe-48ae-bd53-422aec674561", "name": "sourceDatas", "title": "来源数据", "type": "java", "classType": "object", "refEntityId": "16b05aa2-0b08-4f32-ab19-bbb179590f64", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "3ddae09f-c3cc-4463-a9c7-5ecbaa3f3319", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "4e33b404-fe56-49de-b7a8-170b35c61d08", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "4eaec0d1-2acc-4c25-8cd2-72f5c29c1a82", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "5392f472-5326-4ce9-a05b-3e2d6bb7369a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "737d6f73-84fc-443d-8fba-744259188d27", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "764eda7f-df2d-4ef1-ab42-d7fb26ab965b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "77f14112-49c4-4b71-952a-3b76b7590e48", "name": "remindRecordDataSourceType", "title": "来源数据类型", "type": "java", "classType": "RemindRecordDataSourceType", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "7f38f424-6c2d-4b43-bdea-3aa5267e0254", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "845fac16-0cd3-4b22-ad8a-cd4231b68540", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "bb7af090-64ac-4c39-af2c-551d9ab7ef21", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "e9bda8f0-ee35-47e2-8e6f-5304edfab8d1", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}, {"id": "fa90aafe-b146-4e69-9af2-f9c072bb2ae8", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "3f5ab160-c6a4-4aa3-a5c0-22301feb93f2", "valid": true}]}