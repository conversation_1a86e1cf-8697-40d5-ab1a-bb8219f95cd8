{"id": "d26574fa-f0af-4fcc-9ead-54364b973da6", "className": "com.open_care.huagui.rule.OCDirectPayInstitutionRule", "name": "OCDirectPayInstitutionRule", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "244c58ef-513d-4293-b84b-12f1bbca4ca6", "name": "expiryTime", "title": "失效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "34e5c7da-0f11-4aa8-a725-bfd1e87f758f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "3a70bc4f-67f5-4674-9584-d8d48c31a163", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "415256b4-ebdb-46c9-9f1c-a88a306f9b90", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "79b41973-ac48-4643-9f79-478ecf61f443", "name": "effectiveTime", "title": "生效时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "7cc513a9-8790-4331-8639-3484720d7f92", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "815771f3-c04d-4a78-83a3-8673574c5f43", "name": "strategy", "title": "规则策略", "type": "java", "classType": "PriorityStrategyEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "7198f4b38b6320d264a8492ae60ea2f712310c90", "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "8c578e37-8415-4a5f-9bd7-87776ae1372c", "name": "dynamicVolumeConfig", "title": "动态单量配置", "type": "java", "classType": "object", "refEntityId": "d37010eb-b30d-41bc-8732-b9c744fb2e32", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "8e62eca7-404d-4271-8e6b-453dcf11a77c", "name": "ruleDescription", "title": "规则描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "902690c0-60f2-4969-88df-f068befdc762", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "97f33eeb-064a-446b-9a60-1d73ab2c1be9", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "a1de2277-f2fe-4c84-a7c4-1668495752f6", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "a34fb6be-6493-499c-b985-c15b8c2711d4", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "adac918c-51c9-4034-afc3-047f460097e5", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "b36067b5-b398-4ab8-a552-74a020ed61e4", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "bed55a49-97d6-4c90-84bb-a14195d9215c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "d02efce0-a252-412f-8047-e50496dd78db", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "d313cc54-04ed-4962-a7d6-c3e51a2363cc", "name": "ruleType", "title": "规则类型", "type": "java", "classType": "RuleTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8d22f76ebdf5256c56b8bb2ae1cff8eb2eebb91a", "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "e415ce8d-e64a-4ff5-a076-bc1150d94703", "name": "fixedPriorityConfig", "title": "固定优先级配置", "type": "java", "classType": "object", "refEntityId": "9e589d94-1f6e-44e5-93bb-fa0d3975ebbf", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}, {"id": "eac866dc-1172-4988-b215-0b7d954528c9", "name": "ruleName", "title": "规则名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d26574fa-f0af-4fcc-9ead-54364b973da6", "valid": true}]}