{"id": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "className": "com.open_care.product.topmedical.OCFeeItem", "name": "OCFeeItem", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01ee95fb-3ebf-495d-9e0c-c3d60675f492", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "0dd2fe15-296d-4ea4-a702-1b1957cdb8bb", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "364dcc67-95da-483b-8bd0-6afd33209488", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "481108eb-b420-4cad-a3f3-19f3e82469fe", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "4c2f74d9-30f6-4e5c-9d8c-e13256f881e4", "name": "name", "title": "项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1989ec87-9e32-426f-a501-03d1c5b4fe54", "style": "Input", "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"name": {"$id": "4c2f74d9-30f6-4e5c-9d8c-e13256f881e4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7bc50f83-e1f1-44c6-a464-8c1f8c39b393", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "8b75b785-b848-4cbe-9ff6-fad402ad1820", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "9c335eca-c7d7-4f79-a2f8-428e3d2d488f", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "afb750ce-0a40-41f6-a72b-3443e87ec6bb", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "b2a63f21-7c80-4842-b5c9-a551cb293da6", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "b43abcef-5903-401c-9427-58bc86a8ba6d", "name": "medicalSettle", "type": "java", "classType": "object", "refEntityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "b8bf0795-120e-426b-95eb-53c80fa9750e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "be2e1c53-0edd-410b-85b6-65f896a44f9e", "name": "fee", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "e84239e4-5e4c-45e3-a535-2b1c1aa4ff3b", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "f2ca7f76-bf13-4a2d-b35f-aab9856891f6", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "f38e63a0-e544-4b12-ab1e-9f1948f54aad", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}, {"id": "f4080cff-e953-4290-bb0c-2a4b311ff555", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6ef358f5-85a5-4d76-b47f-116bf02d581d", "valid": true}]}