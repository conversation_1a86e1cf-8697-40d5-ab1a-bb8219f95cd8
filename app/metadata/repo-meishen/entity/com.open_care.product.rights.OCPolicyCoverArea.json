{"id": "66c9186e-3dc5-4022-a577-8e42532880f4", "className": "com.open_care.product.rights.OCPolicyCoverArea", "name": "OCPolicyCoverArea", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "b2fce5ff-8fad-4dbd-b6be-82719a9a740f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "66c9186e-3dc5-4022-a577-8e42532880f4", "valid": true}, {"id": "ccb3111c-7b43-40e0-bccb-4f91171dbed2", "name": "town", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "66c9186e-3dc5-4022-a577-8e42532880f4", "valid": true}, {"id": "ccb3111c-7b43-40e0-bccb-4f91171dbed4", "name": "city", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "66c9186e-3dc5-4022-a577-8e42532880f4", "valid": true}, {"id": "e5c11fd9-801f-4c8c-91e5-83ad50e6970c", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "66c9186e-3dc5-4022-a577-8e42532880f4", "valid": true}]}