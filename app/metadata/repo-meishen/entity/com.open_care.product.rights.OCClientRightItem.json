{"id": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "className": "com.open_care.product.rights.OCClientRightItem", "name": "OCClientRightItem", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05f1744e-e9b5-4cd3-9f37-be72956a6c43", "name": "var10", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "0609f330-a5e7-435d-bf23-e2fe3ea6f200", "name": "confirmerId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "07401d9b-67f9-45b8-a26f-53538a5c0e75", "name": "isCancelable", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "09f17ee9-57fa-4946-8b76-da644e0b1786", "name": "src<PERSON><PERSON><PERSON>", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "0aa615dc-9541-420e-a1ba-e2c3d71b768f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "0d31bff7-9fef-41b9-b251-3f13eef4fcfd", "name": "var8", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "0e161126-fb0f-4d1d-87a0-85812c068002", "name": "servedTimes", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "181a7a9a-0d9e-4c56-b976-4c78af8a8ccf", "name": "ocProduct", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "1c07e172-827b-42be-a99b-cf1604e2049f", "name": "clientRights", "type": "java", "classType": "object", "refEntityId": "7472a688-99f4-4404-9c4f-7bc6156461e6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "1f6b95f5-0510-467b-944f-1599f61c82a5", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "22b08727-278d-4ab1-8152-24a62e506474", "name": "waitEndDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "22c401f3-719c-4c77-bdb9-b8f1f72a13a1", "name": "servedQuota", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "2417d8eb-72c8-4c4f-b8da-aeae18964a7f", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "25591173-941e-4cd1-834d-8142f1f20c18", "name": "var9", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "27f747a9-c5bd-4015-a1c1-18faf195e43e", "name": "versionEnable", "title": "是否启用版本控制", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "2803f335-b271-4d6a-bd72-5ccc0e25c40e", "name": "versionStartTimestamp", "title": "版本有效开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "2b6b40f2-991d-4908-81db-d85f85af7419", "name": "isSettle", "title": "是否结算", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "2ef6d949-1f6d-416c-971c-688321a8aec4", "name": "confirmEndDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "3181f1aa-be82-46ff-96c0-e0e9bf75c15a", "name": "var7", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "341a3fa4-1bb0-4370-98a7-4ee3891ada0d", "name": "frozenTimes", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "394088ae-824d-4f42-88c9-0afb840129a0", "name": "var4", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "3b52a4a0-c486-4c11-b230-186bf85be8a6", "name": "pickStatus", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "3caa7204-b216-4a54-8909-8a3d643ec9ee", "name": "isNeedAttach", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "3cfd2907-cde7-4ace-80bc-8cf5a9f17f9a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "427def84-a18f-457e-b0cd-4dd83fc7fdb7", "name": "cardId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "44f728a7-55aa-4a07-97f4-b48fd170a5a1", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "47d11f16-0f55-433c-9fb8-26e7e6c116eb", "name": "children", "type": "java", "classType": "object", "refEntityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "4aa0d5f9-e576-449e-804b-5d3abb550aff", "name": "entityInstanceVersionId", "title": "版本号", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "4e99487e-f313-4a02-a9f1-99b50619186c", "name": "ownOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "512fe44c-a879-452f-8902-a7426d061f12", "name": "var1", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "5440a9df-1e0a-4cd6-8ed3-281bad7e32e0", "name": "nick<PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "54a9531e-5a8c-4861-9532-b16bf9ec7413", "name": "productInPack", "type": "java", "classType": "object", "refEntityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "5a18fb66-b350-4801-9663-35d04a021d53", "name": "servTimes", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "5dd2bbfb-42cb-4a6d-913b-d2fd92cfcadb", "name": "productSettle", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "64ab4954-1ab9-44bc-b945-92561e95e298", "name": "frozenQuota", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "70a3690e-af85-4a3b-bb2f-f707886134a3", "name": "timesCalcPolicy", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "746d01f9-1f13-4ffe-8977-25a60dfcef7e", "name": "var6", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "7c83671c-a013-4571-b23f-0a7db4e08753", "name": "versionEndTimestamp", "title": "版本有效结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8572a05c-ce41-4781-aeeb-fab8c6c50dd6", "name": "parent", "type": "java", "classType": "object", "refEntityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8b637a41-6bda-439a-91aa-7313cd40c846", "name": "contractId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8c31605d-50a5-45f4-9b48-4388a6635460", "name": "endDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8c5a8630-7635-4b0e-883e-6fe0d7ec598b", "name": "restTimes", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8db5f40a-021b-425f-8877-67901d973bc5", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8dd417c9-f574-45a0-ae05-96bb1b9dbfb4", "name": "cutWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8ea1a663-188a-4d67-9405-8fcee3e7843a", "name": "restQuota", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "8efa7c97-0b95-4cad-a16f-9c9544156f15", "name": "cancelDate", "title": "作废日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "9b9cbf6c-4050-4128-b1c4-9fb7616b94cb", "name": "cancelReason", "title": "作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "a0bea7d8-c669-424e-aaec-9f04aaa3a1e5", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "a6f83336-0d57-4991-a0bf-898a62f61cad", "name": "dateStart", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "b21defa8-9910-43bc-acc5-2e093cda5294", "name": "var3", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "b2faeabb-1990-431f-b06c-b4116a320227", "name": "importSupplierName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "b4e46065-ad89-465d-95e0-bf69994967a0", "name": "isNeedAudit", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "bb1c95a4-3de4-4a93-8798-4b6b5b21e7a2", "name": "var5", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "bb613be8-62bf-49ac-870b-1cb91e251278", "name": "settleStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "bdcfdda3-f2ac-4f4b-832d-b7374b1a0b5d", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "c3bf9461-2d23-4ade-ae69-3a774504633d", "name": "genPublishVersion", "title": "是否生成发布版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "c589ff43-e8aa-4241-97a6-4b680be512e8", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "c6eed109-7e28-4bf3-ac39-0aca3aba9c8f", "name": "supplierOrderId", "title": "供应商系统订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "c995e90b-a0c2-4524-ad8f-0068e1b674ae", "name": "var2", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "cb6998f2-84e1-4e18-8dfb-90c291b5c7ec", "name": "entityInstanceUid", "title": "版本公用实体ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "cb9ae21b-c480-414a-9538-b0067087c516", "name": "currentFlag", "title": "版本状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "cc98352f-11ce-4dd4-8998-cd50431a7efe", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "cf7affda-cb98-41fe-95b9-bbedd84ad562", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "d0bd9bdb-10c9-4cab-a6bd-c25e88b3dabf", "name": "sortNo", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "d36f9c53-85db-4ada-8aed-2cee269fb65a", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "d6b50a21-399e-494d-a2c3-90d32d359d92", "name": "genLatestVersion", "title": "是否生成Latest版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "d90c269a-b4ab-4189-b9be-b800837f1128", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "e29a7315-df76-41ed-bca7-04b16c915a56", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "e6871d05-8c27-4e18-a9ee-33dcf19be2a2", "name": "supplierId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "e9947826-4f54-4c3d-ab64-ea4aff4d4829", "name": "confirmStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "edaae230-4e67-44f0-9e30-6fbf6024d4c5", "name": "type", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "eedf42a7-a856-482a-a8c3-72a79ead4bac", "name": "subItems", "type": "java", "classType": "object", "refEntityId": "ad56fc56-0db4-4854-8ebc-74796fa27925", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "f35acbfa-0ae0-4b4a-8ab2-68152575a921", "name": "shallowCopyFieldNames", "title": "浅拷贝字段属性", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}, {"id": "fcf39cad-b6d3-4098-968b-746b1a2a71ee", "name": "supplierProductId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7c119dee-23dd-4b99-95ea-cc146a0df0e4", "valid": true}]}