{"id": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "className": "com.open_care.resource.queue.triage.OCBaseTriageRoomGroup", "name": "OCBaseTriageRoomGroup", "standard": true, "authority": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05a902bb-2947-480a-8d69-38d128eecfa0", "name": "canAppointment", "title": "是否可被预约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "15048185-bce3-4a87-8fe0-e5cd752c6a09", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "1575598c-4c4c-4a50-b4d3-7251c75d4763", "name": "resourceName", "title": "资源名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["resourceName"], "properties": {"resourceName": {"$id": "1575598c-4c4c-4a50-b4d3-7251c75d4763", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "资源名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "1719f1cf-aaa3-413b-a389-8844354c6ffb", "name": "roomGroupCode", "title": "工作组代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "1ed34e17-2f23-48b3-ac00-3a044b494325", "name": "autoTriage", "title": "自动分诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "21f68e12-2fe0-4a5c-95e1-b0ebb5cb6b61", "name": "profitLevels", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "26733310-af0d-499e-8580-944d2d499c96", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "27b21a0f-3f84-4df4-9b99-4ce637dd418e", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "29933114-8385-4358-85c7-6cdc1264381a", "name": "baseTriageRoomGroupDistanceList", "type": "java", "classType": "object", "refEntityId": "53bee56e-e239-4d8b-97e9-f561e5c4f2d5", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "2cfe5a9c-6722-4f31-8687-54d522778ccc", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "2fb002ef-3a7c-4ac6-8fe3-bdcf812006b1", "name": "partyRoleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "304e5331-2ac9-4c42-9d6b-692e464e98f9", "name": "inService", "title": "可服务", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "true", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["inService"], "properties": {"inService": {"$id": "304e5331-2ac9-4c42-9d6b-692e464e98f9", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "可服务", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3e1bdcf5-650d-47a4-8224-95c8b1790bae", "name": "party", "type": "java", "classType": "object", "refEntityId": "39f9bf4f-b0fa-41eb-abf1-0d2dfbb2c2ab", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "42971de5-d68e-4596-9b29-da6e864ecc41", "name": "medicalDepartmentResourceOcId", "title": "医疗科室信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "66c1671c-7d2b-43a7-83b8-913d3f85d56e", "style": "Select", "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"medicalDepartmentResourceOcId": {"$id": "42971de5-d68e-4596-9b29-da6e864ecc41", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "医疗科室信息", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4b002fc7-c6ba-4dd7-ac7e-ea212049bb54", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "5334a25d-7f8f-41d1-aea1-d8427ede62dc", "name": "changeCustomerTime", "title": "换人时间", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "5b1e4ab5-48d4-4adc-9f90-297fb91b91eb", "name": "receptionDistance", "title": "到登记台时间", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "5fd452b1-fc71-4212-8d40-a65526949922", "name": "required", "title": "必须工作组", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "650daf3d-1ba1-445d-9220-9726d7134bb6", "name": "planAndContexts", "type": "java", "classType": "object", "refEntityId": "5df4fe41-a389-4c02-a1b0-1a74ef3cb5ce", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "6efb5714-fb5d-4943-badc-cba302686c4a", "name": "averageTriage", "title": "平均分诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "72c9d809-8b73-452d-8a77-9ae28f86c5fb", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "72ef09c7-4dc7-41b4-bd6c-df56d4ce39ed", "name": "baseTriageArea", "type": "java", "classType": "object", "refEntityId": "eea6742e-5ae3-4e16-89e4-e57f1c8690f0", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "7e38d172-3777-4670-a005-b462cdd0e137", "name": "resources", "title": "资源", "type": "java", "classType": "object", "refEntityId": "7dd3506b-c0a7-4d8b-8def-73c2cc121012", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "7e561636-2cbc-42d4-9bf2-bafc9e6fe77a", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "815545c5-d07d-4aac-9b0f-1c50d96cf030", "name": "shortName", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "944fb240-3f74-4a60-a1dc-940b28e7519f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "9734eebd-5dbd-4372-ab0f-8f5cfdfde861", "name": "servicePlans", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "b179e219-5a0e-44f2-a0e3-6e9b28179d71", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "b2920feb-1bbb-4dec-806b-ee61f3777ffd", "name": "children", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "b6a50679-36ed-4e36-befc-19c2dddbe814", "name": "weights", "title": "权重", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "0", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["weights"], "properties": {"weights": {"$id": "b6a50679-36ed-4e36-befc-19c2dddbe814", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "权重", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "bc944b30-847e-4456-b86a-e361b5c2bdf9", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "be9d3b8e-8861-4828-b28d-16ed4eca7e10", "name": "job", "title": "职位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "bf89e7cc-b59b-4d49-812b-953fb83eedf3", "name": "resourceProperties", "type": "java", "classType": "object", "refEntityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "c791eaae-549e-4031-bdc7-2319f1db97b4", "name": "parent", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "c8910bf5-a1c1-417c-9980-52843c930205", "name": "mustBeFull", "title": "必须排满", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "c92a5a36-eff6-407f-98ec-5e057e0eaf59", "name": "mealType", "title": "餐前餐后项目", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "e93ba801-7caf-4a95-b02e-06e9d4fc82dd", "style": "Select", "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"mealType": {"$id": "c92a5a36-eff6-407f-98ec-5e057e0eaf59", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "餐前餐后项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c9c2b9a1-247f-4b67-8548-4dac27787e9e", "name": "occupied", "title": "占位", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "ca90ecd2-2295-466c-806b-28a03292fc49", "name": "baseTriageAreaRoomList", "type": "java", "classType": "object", "refEntityId": "f99c3478-f93e-4da7-b6bb-0c1a0d48b46a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "cbf85233-9481-420f-9a13-25f558c64eec", "name": "resourceCategory", "title": "资源类别", "type": "java", "classType": "object", "refEntityId": "516dcdb4-8f83-4098-ad59-65da46ff2174", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "ced98fc4-d352-4a9a-8db9-1a167463b582", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "d1405e52-3574-4cb4-8584-78ce2aabac44", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "d487fb04-7638-49d6-beae-5f1c6be2bb78", "name": "departmentResourceGroup", "title": "科室", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "d7baa61a-cf7b-4f4b-a059-c3cce5bb59bf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "daffc509-ffca-4d6f-a126-0f3f75fc743c", "name": "tags", "title": "标签", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "dbd6ca12-c6f4-4622-871d-f7d866322916", "name": "resourceGroups", "title": "资源分组信息", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "e0d25e1c-8fdf-4b40-9916-0a8a3d0e884e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "eb8781e4-55b8-4cab-9010-af08f1e6f8c3", "name": "sequentialTriage", "title": "顺序分诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "f1a5c13a-a3f7-48fd-9eeb-ee6d2f7bb19d", "name": "medicalDepartmentInfo", "title": "医疗科室信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "f1d2bc66-0046-4615-a998-54a28c742ebe", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "f2849d5e-5096-493f-9ecb-5f5f82db3700", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "f763283c-326b-4f7a-8f0a-dabc50bff0dd", "name": "description", "title": "描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "f8ff7068-920e-444f-a74d-2df3cb653951", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "fa82cae0-fc7c-4590-bb81-251c3d420b27", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}, {"id": "fcff5f4c-38b7-41b6-863e-06e660ccbf04", "name": "code", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "6bef2541-3021-4dec-8799-6e4fc83e5b39", "valid": true}]}