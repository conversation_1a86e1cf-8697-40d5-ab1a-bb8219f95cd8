{"id": "2c61eeff-d5f0-4f06-8ae3-4670dee90990", "className": "com.open_care.product.entity.OCInquiryProductFee", "name": "OCInquiryProductFee", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "60777073-fb88-4d47-b714-172ad45efa5f", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2c61eeff-d5f0-4f06-8ae3-4670dee90990", "valid": true}, {"id": "dc70c31f-11eb-4850-9d9d-ced47a9871b5", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2c61eeff-d5f0-4f06-8ae3-4670dee90990", "valid": true}, {"id": "e2827624-f7ce-4fe8-b461-ce4d6390f168", "name": "name", "title": "费用名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2c61eeff-d5f0-4f06-8ae3-4670dee90990", "valid": true}, {"id": "f76530cb-e8bd-4c28-922f-35bdc493593b", "name": "fee", "title": "费用", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2c61eeff-d5f0-4f06-8ae3-4670dee90990", "valid": true}]}