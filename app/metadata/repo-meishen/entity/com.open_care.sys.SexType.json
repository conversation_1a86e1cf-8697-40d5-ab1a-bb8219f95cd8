{"id": "77b56047-85ca-451e-bcbf-fafa05c5336c", "className": "com.open_care.sys.SexType", "name": "SexType", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05813264-f9b7-4099-ae50-7343a20bbfd6", "name": "ordinal", "type": "java", "classType": "int", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "77b56047-85ca-451e-bcbf-fafa05c5336c", "valid": true}, {"id": "9eae6069-2acb-4f10-8fbd-87c35821ed4f", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "77b56047-85ca-451e-bcbf-fafa05c5336c", "valid": true}, {"id": "a9edf7a2-74bc-4601-a020-a5d870ee7471", "name": "dbValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "77b56047-85ca-451e-bcbf-fafa05c5336c", "valid": true}, {"id": "fe6f6da1-775c-4bdb-93d9-97a664e66044", "name": "label", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "77b56047-85ca-451e-bcbf-fafa05c5336c", "valid": true}]}