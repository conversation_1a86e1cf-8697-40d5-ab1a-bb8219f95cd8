{"id": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "className": "com.open_care.order.entity.OCOrderAccountItem", "name": "OCOrderAccountItem", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0f3fbf28-7fe1-4d1d-9afe-c1d6e9488548", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "156ed7a2-ff20-4d25-a06d-3d0014e80928", "name": "orderItemType", "title": "订单项类型", "type": "java", "classType": "OrderItemTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2691611761bee910d5cf1ffc1c47acbedf1fc044", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "157ba1e7-1c4a-43b5-8603-98f91b2ed876", "name": "children", "type": "java", "classType": "object", "refEntityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "15c4a0a9-bf2e-457d-9ed4-ebc42cb5d71c", "name": "validEndDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "190e9120-0361-4e51-b061-33fb8eec5015", "name": "discountReason", "title": "折扣原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "1aa1f0dc-c8be-4a8d-a35f-27a4bbad5c1b", "name": "isSeparateAccounting", "title": "是否单独核算", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "1df20bc7-1213-46ac-a7da-a5513d7c8ca1", "name": "sellerId", "title": "销售方ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "242b1eee-b8c0-4997-9065-ee17b4d4fddd", "name": "totalDayInHospital", "title": "总住院天数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "2fa60b05-bd85-4df4-a14c-9ce92a7ec969", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "32236512-8f4b-47f3-bb35-ccc6d4875ee3", "name": "productCategoryName", "title": "产品分类名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "33a3b48d-8fed-43e1-81f2-25807dc767fc", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "365375dc-5a9a-4659-bee5-e8f88b30bfb2", "name": "orderAccountItemGroup", "type": "java", "classType": "object", "refEntityId": "1088925c-2a0b-49a9-9ec5-8c0520ebe3a4", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "3d54f0c8-53ef-41ed-ade4-471cffd069be", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "3df0ae4b-228c-40b7-bd6e-bb04c26d74e7", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "428ed4cd-a919-473d-b230-330b9b9d7e71", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "47362aac-d0b8-4dad-9e11-ec3532b0e5c2", "name": "productId", "title": "产品ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "4d124272-e878-4cbf-9658-30434515b21f", "name": "productDetailImages", "title": "产品详情图", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "4f9fd1cb-dad0-48d2-95ac-3c4564126aa5", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "53777fab-a66e-4175-b36c-cf2c38cc4aa1", "name": "projectType", "title": "项目类别", "type": "java", "classType": "OCProductProjectTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4791892ca1e43ff2996167ba0d09ac285af678cf", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "53f5755c-d267-4187-8111-a76d5daf5878", "name": "quantity", "title": "数量", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "54ecd1d1-0892-4424-aa5f-0afee5f8865b", "name": "productName", "title": "产品名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "5b563fbd-8d63-4855-95e0-9e7bfcfbdd2f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "5dd5affa-8c07-4da3-9bf7-f3f701301ed8", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "6e354edf-b88e-408f-9117-d9018bf1dc8a", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "712dec45-87b7-44f1-bdb5-27d8ca0e856c", "name": "productImages", "title": "产品主图", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "7e2b9f75-f87e-4403-a1ae-56fa7c7e5761", "name": "divide", "title": "是否拆分", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "82494d26-3f27-48ad-858b-c6603584f8f0", "name": "appointmentRemainQuantity", "title": "预约剩余数量", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "8290a955-ec99-4210-ab46-382ae452d339", "name": "orderAccount", "title": "订单", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "8d9b73cb-9516-4aaf-8df5-6dfe3338fabd", "name": "serviceDuration", "title": "服务周期", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "91bda433-06b2-4912-ad15-e462a25e2ff7", "name": "serviceDurationUnit", "title": "服务周期单位", "type": "java", "classType": "OCDurationUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1b0dfda16b4f63e1fe168aeb4d1f2bf5f61e8395", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "92222f32-591a-4cd4-aaff-2117690440da", "name": "totalAmount", "title": "总价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "93d05778-128a-49c4-885b-081e66da6bfa", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "96f7363b-9893-43b8-b2cf-2f3de8825adb", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "98259816-af7c-4f24-9fac-6ce4ed6c2c5f", "name": "remainQuantity", "title": "剩余数量", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "9d0613cd-7a4f-45a1-98f5-634f7451f4e8", "name": "productDirector", "title": "项目总监", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "a68f47e3-06f7-400d-b888-c08d871ee99f", "name": "unitPrice", "title": "单价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "a7a2df4c-1692-4984-a55a-5ebbd68ee6d0", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "ad74023e-aa95-4b60-9cc3-c14f474cabbf", "name": "discountAmount", "title": "折扣金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "b17145c2-7c8e-41da-9d8e-4823458b9e4f", "name": "productComposition", "title": "产品组合形式", "type": "java", "classType": "OCProductCompositionEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "fb77728c82b4976215813d01658d30647905b917", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "b4873776-d5d7-4420-9d66-d062b220b6c6", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "b5a637b7-76f7-406f-9c89-d841771d6232", "name": "productForm", "title": "产品形式", "type": "java", "classType": "OCProductFormEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "9b987c10bd9348aacfa4807810d99bb0995b8c54", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "b827e798-a744-47cb-9619-bfd9726a6b5d", "name": "packageInnerPrice", "title": "套餐内价格", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "be2d109e-4a59-423b-8849-a31ed2f39309", "name": "hospitalizationDays", "title": "已住院天数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "c1a9bd1a-d54e-48dd-8f24-12a0a1b21c94", "name": "parent", "type": "java", "classType": "object", "refEntityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "c3b47ca0-b3e6-4a40-b9e2-dc0cdff19d44", "name": "validStartDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "d892467c-082d-4f5b-94a9-e0f517c2e859", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "dc511e47-2685-44b2-87bd-9f0f1390d991", "name": "serviceProductServiceQuantity", "title": "服务类产品可服务次数", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "ded17d2a-a180-4432-a81d-f17b61d3f671", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "df5483e3-4709-45ae-a0fb-dea7a3cb1482", "name": "discountRate", "title": "折扣比例", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "df9fc28e-f0fc-4abd-9bef-925cc79638d8", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "e0e97834-1a58-416d-a835-7ace0f34f5b2", "name": "discountType", "title": "折扣类型", "type": "java", "classType": "DiscountTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a56926a80cc12264ec9ee5abd22a9c288e747647", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "e723d057-f6c2-4324-83f0-a5e626e99379", "name": "serviceRemainQuantity", "title": "服务剩余数量", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "ea8959d9-546a-4595-9e2e-fe497b55c843", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "eb58eddf-0313-4792-9912-0cf30093783e", "name": "paymentStatus", "title": "支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "fb326f87-a82e-4448-965e-f7d00643e1d2", "name": "orderItemStatus", "title": "订单条目状态", "type": "java", "classType": "StatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "4a457c4d79867551dcf570b6f56b7ea185e6d944", "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "fb3f011c-542c-4848-9296-22168d0614b0", "name": "sellPrice", "title": "售价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}, {"id": "fd7e957e-7cc5-4120-b94e-696141f38044", "name": "productDescription", "title": "产品描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4f21cd3a-0d22-45d5-b7d8-60e248c534f5", "valid": true}]}