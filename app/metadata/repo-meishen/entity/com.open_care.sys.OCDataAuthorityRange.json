{"id": "fae543c6-956f-4481-b656-98545b0a80da", "className": "com.open_care.sys.OCDataAuthorityRange", "name": "OCDataAuthorityRange", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "a490852e-17b3-4d5a-b3b8-a36b6ba7c8a2", "name": "entityName", "title": "实体名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fae543c6-956f-4481-b656-98545b0a80da", "valid": true}, {"id": "e42ab6cd-80be-4f25-86ad-1b590f43ee20", "name": "fieldName", "title": "属性名称（java中实体的属性名称，而非数据库中表字段的名称）", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fae543c6-956f-4481-b656-98545b0a80da", "valid": true}]}