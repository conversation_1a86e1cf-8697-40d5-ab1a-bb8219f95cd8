{"id": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "className": "com.open_care.jsonObject.OCProductSaleInfoJson", "name": "OCProductSaleInfoJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "027bc02b-e08a-48c9-9c59-392e56ab103b", "name": "saleRange", "title": "销售范围", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "ff8fd643-68d8-426e-a7a4-94e02f123490", "entityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "valid": true}, {"id": "324edc08-ea71-4e94-ab72-6ce6dc8795ce", "name": "internalCostPrice", "title": "内部成本价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "valid": true}, {"id": "5e7603bd-98cf-4435-9696-8fee3f861f4d", "name": "unitPrice", "title": "销售指导价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "valid": true}, {"id": "ae6fa819-8e95-4329-8ecf-56f4fbb0a9f0", "name": "floorPrice", "title": "底价", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "valid": true}, {"id": "d38a5303-9516-4e85-a19d-0ada91801f75", "name": "endDate", "title": "销售结束日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "valid": true}, {"id": "ebbc5774-9bb8-4f69-bf48-592603c45211", "name": "saleScenes", "title": "销售场景价格配置", "type": "java", "classType": "object", "refEntityId": "e20aeb2c-97de-4109-9ce4-933c8fec65ae", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "valid": true}, {"id": "ec4b1bb6-5b18-4cd5-84f5-57f85e8343b1", "name": "startDate", "title": "销售开始日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "valid": true}]}