{"id": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "className": "com.open_care.order.entity.OCOrder", "name": "OCOrder", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00e91674-0111-464b-89c1-7459fcad04ca", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "023e2a34-e0be-44b3-976a-078bd5799aed", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "074e0b9b-4bac-45a4-861b-27678dc1a953", "name": "reasonForApplication", "title": "申请原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "0aa85fb6-0b56-4cb8-bc15-723aa511f452", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "10434707-cf1f-4f63-9408-c0608cfd6eb5", "name": "productPriceInfoSnaps", "title": "审核通过后的产品价格快照", "type": "java", "classType": "object", "refEntityId": "d1b2e111-7825-4378-8137-cb6297029364", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "10681775-2ea7-44e9-a129-cd9618e52370", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "109fc4a1-4569-4881-b5b7-baf8065a5f79", "name": "customerSex", "title": "客户性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "1810c6d0-9295-4b38-936a-807701e0b837", "name": "replaceOrderId", "title": "替换订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "18529252-c3bb-4193-8d6c-4198b0a9c078", "name": "salesStrategyId", "title": "销售策略", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "18e7f042-c0eb-4865-b145-f28ebaa2fc52", "name": "cancelInfo", "title": "取消信息", "type": "java", "classType": "object", "refEntityId": "cd2543df-8799-48e5-8a37-a870977a9a19", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "1fb8e0a6-4b3e-47a4-982f-652b67f56e95", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "20bbba1b-1c51-42d9-a378-d5721df51c25", "name": "agreementId", "title": "协议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "28485d90-155f-46e3-9838-a255aab98b08", "name": "paySource", "title": "付费来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "28c87cd7-4df8-4dee-8094-556e24024978", "name": "cancelReason", "title": "订单作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "28f2ffe9-6ef2-4aa3-b472-f6b301c4a2fd", "name": "needUploadPdfReport", "title": "需上传电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "2b0651aa-2fdb-487c-850a-98b32c82e772", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "2c07aa04-f634-4518-ac78-c0fe4bcc0592", "name": "beneficiaries", "title": "受益人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "329d402a-4cbc-4555-a4a2-901ef34e2831", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "337f0e54-1f66-484e-acaa-101c810079f4", "name": "seller", "title": "销售方", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "3a70b11f-81ea-4c6b-b362-73033a2bf402", "name": "rcptNo", "title": "预交金单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "3f53ce00-9c3b-4734-9737-27b1fa26af2b", "name": "refundFlag", "title": "退费标识", "type": "java", "classType": "OCOrderRefundFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "87fc558a08e65ed030848e89c3e183a36266bf15", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "43b03eb4-5755-4387-aea1-efc8313bfd74", "name": "attachmentList", "title": "附件列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "48384e70-52a7-4ac2-96cb-978896dc5324", "name": "children", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "4d989840-4bfd-4478-993a-73ed54541f90", "name": "orderLocation", "title": "下单地点", "type": "java", "classType": "OrderLocationEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b4354294f6b84222c01e0d2e7f1b13777edb09c7", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "5712f49d-4fce-4d13-af62-ff32a3f01c1e", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "5729de2d-f90e-4aeb-b6e3-eea009b3ef02", "name": "highAuthorityUserId", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "5a41307c-45d4-41f3-9eeb-5b35c8f9e557", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "6062b8fb-ed20-41cf-bb87-e8050285d0ff", "name": "channel", "title": "渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "60a6b173-4fd5-4a9a-9b23-1481893a19c9", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "61c681d5-6753-46d3-9027-238f502d6248", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "626565a9-2665-449e-b7b9-3d84722431da", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "66b006f7-b3cf-4466-bfba-0ac4b5729006", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "69c86bec-bc36-492d-af94-a1a69f6da932", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "6affc6a6-a654-4a36-9221-b8a71393b257", "name": "meeting<PERSON>ame", "title": "会议名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "6c4fee06-ca2c-4a72-8af2-186d27438236", "name": "orderItems", "type": "java", "classType": "object", "refEntityId": "7b028dd2-f0d3-4b62-bc28-09489c72bd32", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "6d914f0d-b036-4b18-855b-92d199c22c17", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "71b45a17-0288-4c46-a2f0-03d5daa955ce", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "739a52cb-1b89-4a74-8b4d-45cd1f1a96d6", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "7982b661-acb1-4cd3-9ea6-88b39e5240eb", "name": "paymentStatus", "title": "订单支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "7a8348c6-241c-4ecc-9ea2-841399e7904d", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "803310cd-5e6b-40f6-a83e-5f40c20028a5", "name": "settleTime", "title": "结算日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "8141b414-2f11-4d5d-b3bf-901f9957544b", "name": "owner", "title": "所有人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "8741f56e-16db-4926-863e-fba43a60b5f3", "name": "additionServiceOrderId", "title": "加项服务单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "88a58a9a-8e6b-4be8-ae26-5a7dc1f92b8f", "name": "salesExpert", "title": "市场专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "8bb9308a-1fa9-4dd2-a8f0-a20cef7ae479", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "97d6e06a-035e-4cdb-8502-e0e9c0cf311d", "name": "profitLevels", "title": "权益级别", "type": "java", "classType": "object", "refEntityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "985b005d-d9f0-43bc-b840-2e01a55cf6e2", "name": "parent", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "998887b8-d592-4212-a152-f625efddcc0f", "name": "advanceSettleDate", "title": "预结日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "9e0d14c4-35b0-4906-bd2e-f755b306ee7c", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "OCOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c91e35a85e7bca35cb090a6b23a9ad875e31951f", "style": "Input", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"orderStatus": {"$id": "9e0d14c4-35b0-4906-bd2e-f755b306ee7c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "订单状态", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9f632e8a-9e99-4765-9575-3af8ad4cfc31", "name": "branchId", "title": "门店ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "a69e6b43-a147-4c4a-b029-ed2eaaf4c26f", "name": "saveType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "ab81ce93-486a-49c2-ae5b-f698af55221b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "ac57f80f-56c0-4d2c-83e8-48bb90f7170a", "name": "sign", "title": "标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "afd6f83f-79c6-4744-b9ec-26d94c6e4299", "name": "qrcode", "title": "收费二维码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b07dd4ce-5095-46f8-a6a9-c33e6d61b8a9", "name": "enterpriseCustomerId", "title": "企业客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72666", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b143e3b5-b795-4aa2-b736-80c24a0a2c56", "name": "selfUse", "title": "本人使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b336911b-0b47-4e33-93eb-205808a08ff0", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b37ecbc3-0be2-47cb-a8c4-661014187c2c", "name": "orderTime", "title": "订单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b40eb961-1472-49f7-94fc-9be373805dd9", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b57ae7df-cd2e-4d3f-8716-50123ff1280a", "name": "brandGroupName", "title": "品牌组名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b7715df7-6804-40d5-9684-10fbf3c5abc2", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b78d844c-6342-48be-91d4-20ba7b050dfb", "name": "orderAccount", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "b8107771-e4f2-43e9-92e5-7949b181e8e8", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "d0ccd5f6-1d3d-4804-959b-d819a189d3e2", "name": "examServiceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "97e7ed54-58b9-4b83-97a6-e842806c3094", "style": "Select", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"examServiceType": {"$id": "d0ccd5f6-1d3d-4804-959b-d819a189d3e2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "服务类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d0ed651b-cd18-4d4e-bb42-a6ed45566759", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "d1eaf086-a3fe-498e-8aa6-b820a3206ac4", "name": "refundOrderId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "d4763f03-3051-48ef-b9c3-b9703d00dae4", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "d51a26dc-5940-4379-99c6-5b991b391d59", "name": "orderItemGroups", "type": "java", "classType": "object", "refEntityId": "12386553-11c9-4e42-a4d2-b7c8014a8a85", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "d7eceb67-708c-40d2-a2cc-2ad1bb084d15", "name": "orderFlag", "title": "订单标识", "type": "java", "classType": "OCOrderFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "06eb12b855e66cabf22c3772eea900e9746ca8e0", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "de16a6d4-fd1b-45d0-925f-eec83091c332", "name": "orderItemsJson", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "e887ce72-089b-4875-aaca-bd8a447b5944", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "e9ac1b16-7c9b-474d-b2c3-8fc0a2594a71", "name": "orderNo", "title": "订单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "eb1e5bac-1df0-45da-80f5-c49b6def3a90", "name": "serviceOrderNo", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "ee10cd00-a1e6-4181-bb3c-268abb558090", "name": "parentOrder", "title": "父订单", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "f805139a-80dc-4f2c-91de-f52331234b95", "name": "orderType", "title": "订单类型", "type": "java", "classType": "OrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ce7f095131e8e2446b2a9a9c184e62dfb78ef8d7", "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "f9835888-335d-4abb-803f-a35cb0ba39ea", "name": "hospitalRegistrationId", "title": "挂号单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "fad4df15-4d64-4bd0-bb90-6725a3e33ae0", "name": "partyRoleName", "title": "客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}, {"id": "fbf58b4a-8ccb-40bb-bd42-1329826dcfa3", "name": "brandGroupId", "title": "品牌组ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "valid": true}]}