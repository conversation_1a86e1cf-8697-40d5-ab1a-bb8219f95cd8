{"id": "b8343577-e4c1-478f-b788-0754dcc5e1a2", "className": "com.open_care.product.entity.OCDrugLimit", "name": "OCDrugLimit", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "193f1055-13b0-4cb5-89c4-14fd863a0e9e", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b8343577-e4c1-478f-b788-0754dcc5e1a2", "valid": true}, {"id": "75a30d25-6be0-4a60-bd0e-6e21ed713568", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b8343577-e4c1-478f-b788-0754dcc5e1a2", "valid": true}, {"id": "99dd0aef-4176-4c5d-95c6-ea92231efc8a", "name": "wareHouse", "title": "库房", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "b8343577-e4c1-478f-b788-0754dcc5e1a2", "valid": true}, {"id": "d40d7902-d3f9-406f-9955-7ab1f804f94b", "name": "upperLimit", "title": "库存上限", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b8343577-e4c1-478f-b788-0754dcc5e1a2", "valid": true}, {"id": "f0bdb843-f738-43bd-8be5-147750996948", "name": "lowerLimit", "title": "库存下限", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b8343577-e4c1-478f-b788-0754dcc5e1a2", "valid": true}]}