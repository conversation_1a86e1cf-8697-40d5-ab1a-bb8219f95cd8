{"id": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "className": "com.open_care.todotask.OCTaskReminderTimer", "name": "OCTaskReminderTimer", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0b60fa34-002c-4ee0-92ad-0f0afdb478ca", "name": "timer<PERSON>tatus", "title": "该timer的状态", "type": "java", "classType": "OCRemindTimerStatus", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "10713a5b7897b2615feb060939dcc088c73f9774", "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "0b885221-fc4a-4509-9eea-04b4b191b968", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "3198d4b8-7308-48ae-a563-6aaf6247fda0", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "31cabde4-2ce8-4b09-9af2-154dd6aeca3d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "43c28f4d-ced2-44be-9823-31b379ce06ca", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "472b4023-ba6a-4bd9-a9b5-08b6accab152", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "4ac988f3-1775-47af-8881-b28b06f08848", "name": "exception", "title": "timer在创建过程中出现的问题", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "5a2d13b5-0cf9-4419-bf1b-2221b0024931", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "7464bb65-14af-4226-a185-eb9b90632ac0", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "85f0c438-e36a-4b6e-a9b6-886ef6a04542", "name": "serviceProductAppointmentId", "title": "该timer关联的预约单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "8aa01915-ae06-4bb9-83f5-baa903605f78", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "8c415607-ef4d-4504-af67-70b150c93632", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "90420348-90dc-4302-bf32-f1e73dcc9f01", "name": "reminderDurationWords", "title": "通知提醒的提前时间段描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "906210e9-baf3-4cd7-97ac-96cca91c1e63", "name": "taskEndDateRuleEnum", "title": "计算基础时间的枚举类型", "type": "java", "classType": "OCTaskEndDateRuleEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "09f047453ac87968a3abf7760c9365cfc8a17fac", "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "930ab9df-bd31-4809-810d-7246b2822023", "name": "taskNotificationRuleId", "title": "该timer关联的任务通知规则ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "9b2cbc68-5d89-45ac-b1bb-abeb91b889fb", "name": "cancelTime", "title": "timer取消时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "9be42164-41e9-47a5-8507-ffad710a48a2", "name": "eventOccurTime", "title": "该timer实际触发时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "cd39d9e1-adc8-4926-a7e2-b033b9eda86a", "name": "reminderDateTimerWords", "title": "通知提醒的自然语言描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "ce34c836-4a49-4ac9-a273-29cd69c68ba1", "name": "timerInstanceId", "title": "在Powerjob中timer注册成功后返回的instanceId", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "d99af5f5-d19b-47c5-9275-91c6fc75d338", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "dcdeef02-c217-4d66-9873-d9b90866a15c", "name": "baseDate", "title": "已预约单或者团次某个属性所得出的基础时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "e5a498a2-08a9-4046-9016-3eae5ab7a83e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}, {"id": "e8db854e-13c5-4446-8c85-a97d85775e9c", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a5e2d78c-1da3-485a-9a54-6d327cf9964b", "valid": true}]}