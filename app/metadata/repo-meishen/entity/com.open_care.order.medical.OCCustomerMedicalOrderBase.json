{"id": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "className": "com.open_care.order.medical.OCCustomerMedicalOrderBase", "name": "OCCustomerMedicalOrderBase", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01104dea-6c2f-43df-8042-4924eada26cf", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "0876b17a-9737-4126-bbe4-5eb38a760319", "name": "meeting<PERSON>ame", "title": "会议名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "08915d70-8424-4c86-9789-96820e9f2298", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "094d2e96-6942-498d-ac39-01dd719f74a8", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "09d90e15-1db1-4c9e-9235-b8285ae9b93a", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "0d3696af-70b2-448d-8129-775bce7d8eda", "name": "branchId", "title": "门店ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "0fd62e7c-0387-4147-bf21-c64f1379b6fb", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "11b00509-489f-4e39-8990-6718abc10497", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "13e7f75b-17cd-4373-a637-ba50b7471a13", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "161aac5e-6583-4a63-b788-629d04d05c1c", "name": "children", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "169b2898-b046-473f-83f3-26075e405a41", "name": "orderAccount", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "17ec14ae-0489-45b8-aead-100db49e4c53", "name": "examServiceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "1bedbbc1-8932-4529-8100-d737deb055cf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "1bf40075-1b66-467a-a071-fcc0548537a5", "name": "cancelInfo", "title": "取消信息", "type": "java", "classType": "object", "refEntityId": "cd2543df-8799-48e5-8a37-a870977a9a19", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "1c23e1b7-463d-49b1-86e3-fa9b58387943", "name": "orderItemsJson", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "1e35ec1a-9bac-47fb-b00a-b305478a7297", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "1f5be793-10e1-473c-bf71-60e4851337fc", "name": "parent", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "207ea7a7-2bc8-4c97-8ee4-2f30c28ae203", "name": "cancelReason", "title": "订单作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "24b9d488-deef-4b6a-890c-054b03469b82", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "24d91a5d-3af7-43dd-906c-2aca546634d4", "name": "salesStrategyId", "title": "销售策略", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "25382707-c84f-47f0-9a96-b1bdbe142c96", "name": "serviceCode", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "2a44943d-02f1-474c-9d20-c8f17c4aaaf2", "name": "customerSex", "title": "客户性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "30ec8c91-22c0-4108-b894-29bb6c82c716", "name": "additionServiceOrderId", "title": "加项服务单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "35d8f7a3-3a1a-4109-8083-d328254ff796", "name": "orderNo", "title": "订单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "36ef29ff-4965-4e73-8913-81f23494dc3c", "name": "beneficiaries", "title": "受益人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "4364677b-22b0-414c-9406-a4f841b54791", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "OCOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c91e35a85e7bca35cb090a6b23a9ad875e31951f", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "49f106ef-c2d9-4ec8-9d2c-be9f60bb79ce", "name": "orderName", "title": "订单名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "4d564125-9fd8-4d91-9f01-b3b7b30c9d23", "name": "sign", "title": "标记", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "50b5ab83-c2bf-492a-8eb9-f5e197190637", "name": "settleTime", "title": "结算日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "5237f083-57a6-4ff7-8a80-bbaaa985bcfd", "name": "reasonForApplication", "title": "申请原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "53682553-b1fc-4445-a5a3-f0ec6e1773a4", "name": "channel", "title": "渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "5401f1d6-2565-4676-bb48-ff42463a7ac0", "name": "attachmentList", "title": "附件列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "54d50ae5-4107-470e-94e4-f40c8256c869", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "5b886eb9-bfdb-4873-816e-d72de5caf7d3", "name": "orderFlag", "title": "订单标识", "type": "java", "classType": "OCOrderFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "06eb12b855e66cabf22c3772eea900e9746ca8e0", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "5eaa33bb-d229-473a-bb2c-12fe414270e0", "name": "seller", "title": "销售方", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "61498b1f-1101-46ea-b7fa-fad48e3d39cd", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "633f5a4e-68a5-4562-847c-cb1c3d0c57ed", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "651737a6-1b0c-4e44-a9aa-f9f3262c6d97", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "6b2cae90-3b29-4d9d-8caa-756075ca69ef", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "6bf412f3-037d-435d-8e00-39874f8d3dde", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "6c40c6e1-cae4-4940-b5dc-19e811d6de04", "name": "saveType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "6fbe1482-6720-4e00-b852-ec135c60bc16", "name": "brandGroupId", "title": "品牌组ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "70bd7e3e-b078-4e3b-8ac7-c4c15ee1d9bd", "name": "hospitalRegistrationId", "title": "挂号单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "79384392-da2c-4f06-8fc9-88859d20e0f2", "name": "partyRoleName", "title": "客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "79633a8f-563b-4875-8f82-f2d61ae8678d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "7d28f787-61d8-4fe7-bb61-b70a4b6ec859", "name": "parentOrder", "title": "父订单", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "8a37e704-3c8d-41eb-ba05-ca10bf883868", "name": "refundOrderId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "8ba606e6-f004-4023-b74b-663d8e042bec", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "95c08b20-c4c9-4ef9-969a-85b55f7aab35", "name": "owner", "title": "所有人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "9680feb1-dad9-4bea-bea6-89d926e7e8d4", "name": "highAuthorityUserId", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "97404c36-054c-40f6-be01-2733cb776962", "name": "qrcode", "title": "收费二维码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "9894aa15-a62c-49a3-bd5b-8d8422ae0942", "name": "replaceOrderId", "title": "替换订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "98c9e72d-3e7e-4cc0-915d-35474af6fb30", "name": "orderItemGroups", "type": "java", "classType": "object", "refEntityId": "12386553-11c9-4e42-a4d2-b7c8014a8a85", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "98f5c0e7-980e-4b77-9209-19ac1ce9a562", "name": "orderType", "title": "订单类型", "type": "java", "classType": "OrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ce7f095131e8e2446b2a9a9c184e62dfb78ef8d7", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "9cb1dfaa-39f3-4355-8d12-461ed3b67b54", "name": "orderTime", "title": "订单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "9e6716a5-c006-4a35-a510-560350da8f37", "name": "sourceDepartment", "title": "来源科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "a61a6706-89bd-4119-a7c1-58586dd28752", "name": "print", "title": "是否已打印", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "a9070b7a-d279-4938-a157-35c13b37c055", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "ab1d1b14-778d-4c72-b2df-88482e811494", "name": "profitLevels", "title": "权益级别", "type": "java", "classType": "object", "refEntityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "abab8bce-c753-4048-a51d-df5aed0afb32", "name": "serviceOrderNo", "title": "服务单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "afc6ebe2-da78-40d2-8c0e-c45804d8abaa", "name": "orderLocation", "title": "下单地点", "type": "java", "classType": "OrderLocationEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b4354294f6b84222c01e0d2e7f1b13777edb09c7", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "b1397435-9157-4d64-9819-9f4ef334f850", "name": "orderItems", "type": "java", "classType": "object", "refEntityId": "7b028dd2-f0d3-4b62-bc28-09489c72bd32", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "bc0a5a62-84a8-4540-8494-380d1cccf0d0", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "c110d639-e96d-469d-9a8f-0867d9f29e6f", "name": "advanceSettleDate", "title": "预结日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "c2983038-358b-44ae-9fe2-6833acb684ce", "name": "needUploadPdfReport", "title": "需上传电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "c3eaa380-5580-4457-babc-24a7d39ab543", "name": "enterpriseCustomerId", "title": "企业客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72666", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "c6e6aca7-5838-4bcc-9710-721cc7334e23", "name": "rcptNo", "title": "预交金单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "d2daf7cc-2f63-4727-abbf-c7acb5a20064", "name": "paymentStatus", "title": "订单支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "d55bd0bd-9455-4001-8a4e-43255cbbb66b", "name": "selfUse", "title": "本人使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "d5ad4505-c738-4832-a70a-71acf406647e", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "d70652f9-0545-4a6b-94b0-295cb1fe0f2b", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "da5038d5-d630-4f67-b10a-bcd47470d26e", "name": "brandGroupName", "title": "品牌组名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "dcf53807-a74c-42f8-bf5b-909ffbc0ae7a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "dee9558a-ac0e-449d-88c2-0e9ffc2a6ef4", "name": "paySource", "title": "付费来源", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "e07f42e4-a8bb-4465-9ea4-483e40d3c4f3", "name": "refundFlag", "title": "退费标识", "type": "java", "classType": "OCOrderRefundFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "87fc558a08e65ed030848e89c3e183a36266bf15", "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "e1272b39-46ca-400e-9db5-4f6d6c066a61", "name": "agreementId", "title": "协议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "e23c5fb9-4205-41af-bfd8-4ee408f0fe32", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "e3fb93ee-e771-40cd-b988-7b4d24da1746", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "ea8157bb-f2e7-4374-8477-454b373ea128", "name": "salesExpert", "title": "市场专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "f05a8ae4-39d4-43ec-b669-4bfb074858e1", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "f84fa1c4-dfcd-449a-8b1a-2821d3b58b65", "name": "productPriceInfoSnaps", "title": "审核通过后的产品价格快照", "type": "java", "classType": "object", "refEntityId": "d1b2e111-7825-4378-8137-cb6297029364", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}, {"id": "fffbaef4-b9a1-4d1d-87d4-90eae40923fd", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "a3a4e45c-9f9b-442a-8d4d-7d4260ad1cb4", "valid": true}]}