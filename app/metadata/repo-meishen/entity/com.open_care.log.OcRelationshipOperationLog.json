{"id": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "className": "com.open_care.log.OcRelationshipOperationLog", "name": "OcRelationshipOperationLog", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0294e9a4-15c7-4905-95b9-1416a8ed45a0", "name": "businessNameBindRole", "title": "业务名称绑定角色", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "0fb5957b-ca14-4f1b-9fff-a984309a33e5", "name": "businessType", "title": "业务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "152a9c2e-9188-4aed-9f69-ec6427fe9b4f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "19fa994b-7409-4370-b164-fa6a61a6a2e8", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "31be7f24-c733-4e84-9c45-88b321ab4aa0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "3f29b3e5-c59d-4506-b75c-66cc3b6e7094", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "48ede647-7ccd-44be-9b2a-20177abdb16c", "name": "newData", "title": "新数据", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "6ef50f68-7c44-4a6d-b018-a9aebcd650ea", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "71d09a68-86a5-479e-915e-160fe83496f5", "name": "historicalData", "title": "历史数据", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "7a268c1b-56d2-4861-8342-1c7faafa492f", "name": "businessValue", "title": "业务值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "84c7f875-6d0e-4d2f-b5ed-1822d504c683", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "84fb69eb-ba2a-402c-929b-d92ed7c3013c", "name": "operationClass", "title": "操作类", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "9b52ff7e-a542-407c-b0c8-d7d7fcd8d673", "name": "modifiedColumnTitle", "title": "被修改角色名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "be729273-62f4-479c-8c56-3e09c14b9d91", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "d2bf69fd-4b2c-4a2e-9b66-631e2d01b949", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "d6660bde-3d27-45e3-8822-b0bdb9e77cc3", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "d8439ee5-c8b8-43ee-93bb-7a57512b6895", "name": "businessName", "title": "业务名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "f05c3474-9e39-4dfb-9630-e0aa3d000c70", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "f955f27e-34a2-4815-a258-95605766f5e1", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}, {"id": "febc0d60-26c6-4e91-b35c-9d94abe4756e", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "40f33ed1-2ba6-436c-8fea-c156cbaac01f", "valid": true}]}