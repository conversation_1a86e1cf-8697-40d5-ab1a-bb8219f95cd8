{"id": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "className": "com.open_care.event.service_order.precision_medical.OCPrecisionMedicalServiceRecord", "name": "OCPrecisionMedicalServiceRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01628bea-2623-4cc7-9ba1-bc259393d1b1", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "0162f5b1-c9b0-4026-9449-36a1d8909760", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "03907e04-8baf-4bb9-b464-1e40ebe689ac", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "20c44c4f-6927-46c2-b491-fdf7cbc30e08", "name": "appointmentServiceOrderPrecisionMedical", "title": "关联精准医疗服务单", "type": "java", "classType": "object", "refEntityId": "f2bdafa1-4203-4b4d-9271-c198214cad9c", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "24962c5b-a6ef-4924-adb7-dd3d2eb89d86", "name": "actualCheckDate", "title": "实际检测日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "3d129a8a-f383-434f-b934-1d35c7813182", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "40ecd52d-4445-4476-817d-65fbdcc3c8cd", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "56e920a4-85b8-494f-83bc-152445c866b2", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "6c76f821-5bbd-42db-b000-0f560c1f9696", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "9c7e7094-55c1-4f22-8499-d7f9aeb7c60b", "name": "attachments", "title": "影像附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "a05ed03f-3d32-4f98-8941-d4eada0d3685", "name": "customer", "title": "实际使用人信息", "type": "java", "classType": "object", "refEntityId": "b011f8ee-475f-46b1-b3a5-e18c99ca01b7", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "b2aad623-f796-4753-a899-86d64b368c3b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "b7d3fc67-2f08-4ad5-829e-7d2659951f3a", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "b806f6a3-882c-44e6-b0d0-3db6800c0985", "name": "actualCheckService", "title": "实际检测项", "type": "java", "classType": "ServiceIntentionEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "262003322f993d4e6e8f0e6121fb60c3fe098658", "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "d7131072-a9a8-41e4-963a-ebbb21512e29", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "d959f71c-40c8-42bf-8a18-87c8fd7a2642", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "ecc1fc2e-45bb-4ca6-89bb-fa92d1249d28", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "f212bad9-de7a-4347-9414-be9150a23174", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "f6828c29-b9cb-4cc2-9d57-7a1441a2db98", "name": "description", "title": "病情描述", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}, {"id": "fa84abc4-44b5-4149-8292-d22319679f2a", "name": "currentDateFollowRecord", "title": "当日回访记录", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ecc36d89-40e5-4f61-8fdd-3960349a04ae", "valid": true}]}