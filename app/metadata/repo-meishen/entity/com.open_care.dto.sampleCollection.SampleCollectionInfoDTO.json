{"id": "8377dc40-1548-435f-97cc-3571d0dfe944", "className": "com.open_care.dto.sampleCollection.SampleCollectionInfoDTO", "name": "SampleCollectionInfoDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "2d6390b7-7b6b-434a-8bc2-68796575ab4a", "name": "applicant", "title": "申请人ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "3410ff56-5e87-4d2d-aa25-71d68dab350d", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "SampleCollectionInfoAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "dde14ab1fd0c58505ca8f4e792ef457f0d4dfc72", "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "3504ab78-423e-4b97-89ff-f563dbe3c24c", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "56f42c14-a545-48a3-bbec-0663200ab3c0", "name": "trackingNumber", "title": "邮寄单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "77957675-1682-4ae7-a5be-3380611137f2", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "802d6765-d70d-43b4-8c13-f1f74e081643", "name": "needExpress", "title": "是否需要快递", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "bb23b6e1-0047-4fe4-8ef3-1588631a358d", "name": "applicationTime", "title": "申请时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "DatePicker", "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "bc908f4b-5a4f-4116-930c-9b1dfbafdf53", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "c9b12f11-cc5c-42b1-94ea-1d73ebe838c3", "name": "attachments", "title": "快递附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}, {"id": "fc89fd78-d834-4967-81d0-21753a7e9c25", "name": "personInfos", "title": "会员样本", "type": "java", "classType": "SampleCollectionCustomerDTO", "refEntityId": "1eabbc96-49dd-46a7-913b-0b013446c523", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8377dc40-1548-435f-97cc-3571d0dfe944", "valid": true}]}