{"id": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "className": "com.open_care.order.OCServiceOrderProcessInfo", "name": "OCServiceOrderProcessInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "01b56e2a-73be-428f-8d98-a2aa03f9c54c", "name": "type_noFinalAudit", "title": "不执行总检流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "07e0d716-e4a3-4bd5-9c40-3d58f91b626e", "name": "type_noReportToTrans", "title": "不执行报告交接流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "1855e67c-a9c8-4f68-9e96-0549d13c6555", "name": "type_noFinalExam", "title": "不执行初评流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "20eaf58c-9e14-405c-8b3f-d6c277093f38", "name": "type_noPrintReport", "title": "不执行报告打印流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "631be415-e5cf-4afe-b001-5517a7a50ee5", "name": "type_noSubmit", "title": "不执行送检流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "6def194e-59de-49d1-af91-9e3b2d19b1f0", "name": "type_noReportToSign", "title": "不执行报告签收流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "8e230235-8307-4a39-8680-3b7ec5fcc9c0", "name": "type_noReportIssued", "title": "不执行报告领取流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "a248c786-9849-450f-8aca-aaa2f97353b9", "name": "type_noNurseAudit", "title": "不执行护士审核流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "ae7ea26b-9897-46ba-a0cc-c07b83302003", "name": "type_noSampling", "title": "不执行采样流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "bcc813be-ad69-4e8e-85a8-d1a0ea1ecc06", "name": "type_guideList", "title": "开启服务后直接生成导检单回收任务", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "f35cbe65-ab91-40eb-ac72-8286c08aca47", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "fb268515-9679-4b29-8339-385c1634b6ca", "name": "type_noSurvey", "title": "不执行问卷流程", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}, {"id": "ff702c75-791f-494c-9b39-4a32a47e66ea", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "valid": true}]}