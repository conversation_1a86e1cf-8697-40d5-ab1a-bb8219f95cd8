{"id": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "className": "com.open_care.medicalMicro.physical.OCBaseReportSetting", "name": "OCBaseReportSetting", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "报告项目设置", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "15978fa8-a01e-4798-849b-e720da525e6b", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "1a658e74-accf-41c5-9831-1f97116e686d", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "1e88d879-50a1-4496-8d4d-8b6b0834b65e", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "2019f715-d6ef-43fc-8395-195d4bcc3ca8", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "653f9b51-c263-4b57-8103-bd9c9bfcb9a8", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "84260a8e-65fb-447b-9c52-0b8ca8583d49", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "893b8a5b-70f6-4be6-a6b0-94a08433ca95", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "963813de-741b-4089-8b23-58721b0b8c34", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "a4880dac-7543-430d-bacd-86b788d831b5", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "b757a48d-c623-4476-b608-ecf5398c4a8c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "b818fd97-47c2-4ea6-92aa-5d0bcb2f5d9d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "b9700bc5-3d5c-4a46-84d8-8d858e44659d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "ba8dceea-47e1-45ee-9090-77bccec2277b", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "c7dcc624-8492-4a60-b795-87934ed08d8b", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "df370d7d-c4a8-4b25-a538-ffe430a96a42", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "f17c60d3-7724-435b-95e4-ad8601dca6e9", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "f1b2cc95-a1c0-4918-b6c7-b6eedbde4eec", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "f76d73b5-486a-4ff2-8fe3-d8622f23ccc2", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "f8903f5d-0533-4c35-8e11-28b3702a0338", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}, {"id": "ff70ad14-0751-48bf-bb2d-95e2fce045f5", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ec47be69-7dd4-4c6f-bb7f-0e307cdb08dd", "valid": true}]}