{"id": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "className": "com.open_care.sys.OCPersonalCustomerRelationship", "name": "OCPersonalCustomerRelationship", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "31472a7b-8a63-44bf-9bab-c60ba3882d67", "name": "relationshipType", "title": "关系类型", "type": "java", "classType": "object", "refEntityId": "ae2ee6c6-c65a-40d1-87c8-f03d528cc5d6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "322b4590-4d12-4370-9cd3-f2259af16327", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "631e1134-a7d8-4fcb-8cc9-c5114c8d1c9a", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "66efc6f1-60d9-48dc-ad0c-ec1dd35ba21a", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "6c2e8b2d-8aa8-4603-9fa8-bd8fb6bee735", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "81d4b5ad-91f3-473a-b2f4-baac1ebb239e", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "84da4b37-6132-41b5-b71e-f7deb56a14cb", "name": "sourceId", "title": "源客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "8549b602-33f6-48a5-8db2-9fb005d7be5d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "9767233d-d36f-4121-8597-5d5fa9b57cac", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "b10aab78-85ad-4509-a10a-bded158b8e74", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "b6390f26-2db1-42c6-bafd-1e7d8a647af2", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "c6101b64-a8fc-4649-94a3-184477680bb9", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "d31ab494-e7a0-4ab1-840c-de717f0bde7d", "name": "targetId", "title": "目标客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "e55fdfac-b8e7-452c-8c36-c6e38285e8bd", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}, {"id": "ebc2ea55-997d-4966-9b66-7db8b15fade2", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "330e42ef-20e1-4788-9362-aabd8c7aa7ef", "valid": true}]}