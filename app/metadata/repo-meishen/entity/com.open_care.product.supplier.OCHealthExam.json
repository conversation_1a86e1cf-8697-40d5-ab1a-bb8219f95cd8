{"id": "e61bd965-04aa-4a7c-936d-68471996d1dd", "className": "com.open_care.product.supplier.OCHealthExam", "name": "OCHealthExam", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "03502491-b0c5-4b54-a62f-2bff84094561", "name": "drafts", "title": "草稿箱", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "03ecedef-70e3-488d-a5ff-057e3e5f07f2", "name": "comments", "title": "提交备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "083a964d-5bef-4542-80f4-e5af91d7be43", "name": "preStageId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "0e7885b3-44d3-4b6d-b9db-bbf6f1a29e5c", "name": "enable", "title": "有效", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "148609cc-cc80-44b6-9c54-6dd94a5b352a", "name": "supplier", "title": "供应商", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "20a9ab2f-6f2b-4b04-8fa3-9c4f9a81fcd8", "name": "internalRemark", "title": "对内备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "2241c44f-de5c-48f4-99a6-473c26da05e8", "name": "externalRemark", "title": "对外备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "3635ab2c-1673-4857-95f4-eb94df4f8cab", "name": "productNo", "title": "供应商服务项目编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "3a7512c5-f774-4189-be53-9663d0728d4b", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "3a89e0e6-f12f-4ad3-98fe-49ae02dc8fee", "name": "invalidReason", "title": "无效原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "3c57ab3d-5ee1-4bc3-9530-12790ad96ad0", "name": "lastSaveDate", "title": "最后一次保存时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "4165118d-2605-4253-bf11-019d99f2adbb", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "4dd6ec69-8016-4946-9f49-b413fa143d93", "name": "thruDate", "title": "结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "55c9aa64-535a-4d2a-b465-dfd9fc920001", "name": "fromDate", "title": "开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "566ebd47-cee9-4326-9e10-3c3f6119614c", "name": "explanation", "title": "服务项目释义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "5f479c79-8a5f-450d-8767-89c1054c3312", "name": "product", "title": "产品", "type": "java", "classType": "object", "refEntityId": "83b77f63-f957-46ca-a1b8-e5d556f59fcb", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "5f8a8202-e0be-4044-a540-b3c5776b4467", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "615cad66-26f5-4c0f-946b-746a21cf3a5b", "name": "landingForm", "title": "落地形式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "6214916d-1401-4002-ab96-695e11a30f6b", "name": "material", "title": "是否有物料", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "6629cf99-b7ff-4ddf-9d05-9e101e8b17ca", "name": "materialInfo", "title": "物料信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "70f352bd-1c8b-4086-8c84-f1daafe40c2d", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "849eaea1-1b96-477e-aee2-ca1c577b91ea", "name": "deliveryMethod", "title": "服务提供方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "851038b1-ad63-4432-9088-cdc3fc77a235", "name": "currentAuditBusiness", "title": "当前审核业务", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "87ccae13-087f-4ba2-8570-72010e759ce6", "name": "draftsOperator", "title": "草稿箱操作人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "955b6552-388a-4b4b-a7ba-e0cf0f0cb777", "name": "attachments", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "9d427ecf-0d8b-4269-b178-867903f877a2", "name": "currentAuditStatus", "title": "当前审批状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "a26d747b-47e7-4c2a-bd4e-dba18bd69a33", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "a5d74c23-69a0-4bf1-b21f-74944d537235", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "a93b0fa1-b837-4c44-a8d9-e7b8b9f1ede2", "name": "h5Url", "title": "供应商服务项目H5页面Url", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "b3115525-cf3b-45d8-bce6-948e99a55ee7", "name": "contract", "title": "合同ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "b7c8c105-6970-457f-8ac5-4c66d111db13", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "bcbc126d-e348-4a63-9560-93a3a103044b", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "be92ea8e-cb45-482c-9c09-5c8ecf64b21a", "name": "name", "title": "供应商服务项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "c65a2ceb-b447-4345-876f-a7278998459e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "c9b20107-ba1b-4221-a6d7-d5e24d662fd6", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "e4826d4a-6c92-4da8-b0a2-30651a606cd0", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "ef6fddd1-9f58-4f88-b426-67bfa8b6f030", "name": "advanceBookingTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "f2b3088e-50b5-4213-89a5-e2a2f484e2aa", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "fe8112b4-e011-4aad-ac0f-53aaa722d514", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}, {"id": "fe868566-a72b-427f-831f-ad3d8f43ecb3", "name": "content", "title": "供应商服务项目内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e61bd965-04aa-4a7c-936d-68471996d1dd", "valid": true}]}