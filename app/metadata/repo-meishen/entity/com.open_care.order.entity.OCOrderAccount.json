{"id": "57b72459-ee70-4ba7-9940-f7b9d857476d", "className": "com.open_care.order.entity.OCOrderAccount", "name": "OCOrderAccount", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04bba448-d2b4-4cb9-b16c-56e9fdec3016", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "05b35608-f697-4590-884e-037a209f07c4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "0a9e4470-e806-4077-b069-17eab543a109", "name": "children", "title": "子订单账户", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "133a2584-0a02-471d-9dd5-a902f6388570", "name": "source", "title": "权益来源", "type": "java", "classType": "OrderAccountSourceEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8f4947341040b1284c125d94414abf45e50a7474", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "159e6ee6-df8b-470f-9ce5-a74322a07769", "name": "branchId", "title": "门店ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "1dcb72b9-1dc4-4541-86c3-447e7fa969bc", "name": "paidAndVerifiedTime", "title": "已支付已确认的时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "1eb3219f-5a44-408a-aa66-2ef74150a5ac", "name": "servicePackageBindId", "title": "服务包绑定id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "20aa5770-7478-4722-841b-db15a34c3c5f", "name": "policyholderCustomerNo", "title": "投保人编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "241792cb-e5a0-411b-a549-1f29fe75976d", "name": "equityUserChangeHistory", "title": "权益使用人变更记录", "type": "java", "classType": "object", "refEntityId": "5b6e3b98-d2b3-46b8-8f78-4a47eb982bc2", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "2811058b-076c-4f71-9609-8ec9c071d40b", "name": "useStatus", "title": "使用状态", "type": "java", "classType": "OrderAccountUseStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "5974f682599e51e6971acdfe73b73092c8e6fa55", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "2c3923ea-1a82-4b96-9b25-058eb85dc493", "name": "useCustomerPhone", "title": "使用人电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "2f50fe78-03e1-4610-9bc7-09733494daa4", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "35e4e628-26c8-4e8f-99e3-88cc164a66fb", "name": "policyNo", "title": "保单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "383155a3-cd51-4108-a108-c1a5ea66a2a4", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "3887e383-b0de-411b-add2-826d6e5a009e", "name": "customerNo", "title": "使用人编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "38fa9283-335e-48d9-8228-7d949420b3f7", "name": "seller", "title": "销售方", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "3d5398d2-38d0-4592-a4e0-fe8a9666a43e", "name": "orderType", "title": "订单类型", "type": "java", "classType": "OrderTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ce7f095131e8e2446b2a9a9c184e62dfb78ef8d7", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "3f3a9ef2-c7dd-4417-b1b0-8dfadb1ed082", "name": "partyRoleName", "title": "客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "3fc2e3ab-8fc2-46af-997e-ad75b3869b5a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "40cb0eea-cd3f-4d52-8d2a-af6ba72c58ce", "name": "useCustomerId", "title": "使用人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "4afe2433-c083-4d7a-87ba-e8e9b57add7c", "name": "hospitalRegistrationId", "title": "挂号单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "4b3ff9a8-9559-4616-a8bc-95ebd80b1312", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "4fb69448-2c79-4907-82a6-6c50b156e6e0", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "514bfe50-c3de-4021-9101-5afdfb622342", "name": "policyholderCustomerId", "title": "投保人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "52a07c34-4097-474d-8df3-12c606489199", "name": "attachmentList", "title": "附件列表", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "54851c24-8fdc-47c0-b2cb-8d081f565004", "name": "sourceName", "title": "原使用人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "5873cbb9-2d6f-4824-af0d-b419809c1798", "name": "svcPackageName", "title": "服务包名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "5d59d771-8f9f-476a-a9f0-c23e1f5d18fc", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "60d483cc-c7b7-49e6-9ba9-5cf7f66682d0", "name": "expDate", "title": "服务结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "63484aed-6844-4912-908d-5e44a0fad82e", "name": "svcPackageCode", "title": "服务包code", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "667fb9e2-82ba-43aa-8276-9facc86b2081", "name": "productPlanName", "title": "产品计划名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "69751dfb-06e5-4cae-bcea-e953ce40421c", "name": "useCustomerName", "title": "使用人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "6aeff22b-24d4-4689-8788-232134a7fa6e", "name": "orderTime", "title": "订单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "6b81da72-2c27-44dc-812f-1eb08392bb1a", "name": "reasonForApplication", "title": "申请原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "6de2d430-bdb3-45c0-95ae-591e1abb9b91", "name": "appointCount", "title": "服务包指定次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "7005214a-a7ad-4997-b2c8-b177b4308f5c", "name": "orderFlag", "title": "订单标识", "type": "java", "classType": "OCOrderFlagEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "06eb12b855e66cabf22c3772eea900e9746ca8e0", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "789ebd96-308b-40c1-8e17-955991bd60b1", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "79a94ba7-07d4-44af-97f2-70f076852f64", "name": "orgName", "title": "投放机构名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "7b169296-991f-4031-bffe-eef8ed9e3fe6", "name": "owner", "title": "所有人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "7eddb7f4-5dbe-4501-ae61-a7c9f7724aef", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "OCOrderStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c91e35a85e7bca35cb090a6b23a9ad875e31951f", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "82f15494-1eea-43f6-8c2b-78eb6c7776e9", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "85b06b12-d794-446e-aeac-55deaf372cf1", "name": "customerAge", "title": "客户年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "872ff446-98f2-4021-85b5-01d0cb29e4db", "name": "orderNo", "title": "订单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "8cb57692-7a26-4e76-8509-3c656870c7b0", "name": "changeFlag", "title": "是否变更过使用人", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "939efdb8-a8e8-4a96-b487-c4fcf40e7204", "name": "profitLevels", "title": "权益级别", "type": "java", "classType": "object", "refEntityId": "38ec36fa-000a-4bbd-9515-23062e584ee4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "9a7493f5-215f-406f-9e8b-10d72fbc12ba", "name": "appointFlag", "title": "是否指定使用人", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "9abefadf-17a2-42e0-9904-36d7bf78bed2", "name": "premiumStandard", "title": "保费标准", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "9ad9fd83-4dc6-4148-9048-8a2ecc34e1e3", "name": "orderLocation", "title": "下单地点", "type": "java", "classType": "OrderLocationEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b4354294f6b84222c01e0d2e7f1b13777edb09c7", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "9be03916-800e-418c-a111-e01b126af2eb", "name": "customerSex", "title": "客户性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "a0d7991c-64d2-4e00-b162-3363413b0ffd", "name": "channelName", "title": "投放渠道名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "a61463d1-8a1a-47b0-a317-89312fe5854e", "name": "selfUse", "title": "仅本人使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "a7242148-337f-440b-9d37-f32c5bb1c214", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "a9b8d3c8-2d2a-44ca-ae48-e0e8235f50be", "name": "residueCount", "title": "服务包剩余指定次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "aa25551a-e562-481e-b2a9-9aa0d1ada432", "name": "<PERSON><PERSON><PERSON>", "title": "指定使用人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "ad8c170b-50cd-48fc-9aac-e31b19b94d80", "name": "brandGroupName", "title": "品牌组名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "aea82f06-dbbf-4de9-a520-390c013b56d6", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "af72abee-c4eb-484e-a553-32457f24597f", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "b17fcfb4-427e-4bbd-8fb3-fc0697239656", "name": "RemainingSpecifiedTimes", "title": "剩余指定次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "b81d0368-47d8-45b3-af7c-4e962b2438ce", "name": "partyRole", "title": "客户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "bf660cb0-049d-40aa-96d4-f8cd52d796ab", "name": "effDate", "title": "服务开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "c663eaef-b8f9-4f4e-ad01-233fd14d13eb", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c8eb9cc4168a109b6ebc87d2042acf3e000cf98b", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "c70095f1-243e-4c99-af92-0886300fa1b5", "name": "orderAccountItemGroups", "title": "订单账户条目组", "type": "java", "classType": "object", "refEntityId": "1088925c-2a0b-49a9-9ec5-8c0520ebe3a4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "c824d40d-4783-49d5-ba88-cdc6dcfaae79", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "cd1490e2-d4f0-417c-a8b9-71d4abd23ab6", "name": "paymentStatus", "title": "订单支付状态", "type": "java", "classType": "OrderPaymentStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "19743cc23696eebc4d53a0e20932ffdee3e9f169", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "cdef7a39-854d-4418-ac4b-5cf8d8b52f50", "name": "productPriceInfoSnaps", "title": "审核通过后的产品价格快照", "type": "java", "classType": "object", "refEntityId": "d1b2e111-7825-4378-8137-cb6297029364", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "ce997155-e4aa-4959-b2aa-8f2e7abc5152", "name": "salesExpert", "title": "市场专家", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "d0b8ba9d-2b06-4ebd-b5a9-56b47b6f1ad6", "name": "policyholderCustomerName", "title": "投保人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "d81e45dd-f2ae-428e-b41d-a356fe3acd8c", "name": "highAuthorityUserId", "title": "审核人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e020f55a-24a9-4413-88e1-b4e62f9c8c7d", "name": "branchName", "title": "门店名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e0fdc450-0024-4efd-8fe3-33f38fa9e159", "name": "cancelReason", "title": "取消原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e1373ee2-7162-480b-bc48-b98c90fe9b4d", "name": "relationshipWithPolicyholder", "title": "与投保人关系", "type": "java", "classType": "FamilyRelationshipEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c138aefd0275b97c0506e830d7cda9d7dc21f7a9", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e46bab07-7d4e-4f50-aaf9-3da0000fae7c", "name": "isSpecifiedUseCustomer", "title": "是否已指定使用人", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e535c457-adc5-4c76-bff8-cd8c7f760506", "name": "orders", "title": "订单", "type": "java", "classType": "object", "refEntityId": "c90dfce9-cd0f-49e5-8e4e-7ee8ed00df2f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e7f25028-f9e2-48c0-9911-2fcdca69153c", "name": "reservationNo", "title": "统一服务平台预约单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e90f074e-bdcc-418c-94a0-11b7ad1baf27", "name": "beneficiaries", "title": "受益人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "e94d183f-9fb8-46cb-801d-eada07d2b7bd", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "ea305fc3-f126-426f-9c6a-c8c3cbd73a56", "name": "refundStatus", "title": "订单退费状态", "type": "java", "classType": "OCOrderAccountRefundStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "02bd978d50e3ac8d3507dc842d70c9345cb024c0", "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "ee58c2b4-68f4-4f45-9118-3aa518d27311", "name": "belongName", "title": "服务包所属人姓名（投保人姓名）", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "efdd621b-c1cc-494f-b18a-0775c06811d3", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "f161f83a-a21c-4f79-9da5-a66982bb0197", "name": "rcptNo", "title": "预交金单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "f19889ae-aac0-40ea-b26b-feb4e751fd00", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "f1c50ae5-da07-4b49-86b1-06013b6b944d", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "f26c435a-c56b-43c3-a1a6-ca6d156457bb", "name": "brandGroupId", "title": "品牌组ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "f4ba9063-73e0-4a21-8f87-726b3625d5de", "name": "meeting<PERSON>ame", "title": "会议名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "f6c4662b-8147-41e6-935c-1e2a3c39fd8d", "name": "parent", "title": "父订单账户", "type": "java", "classType": "object", "refEntityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}, {"id": "ff3e029d-14f5-4da7-ab12-0a448d6f4b68", "name": "policyholderCustomerPhone", "title": "投保人电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "57b72459-ee70-4ba7-9940-f7b9d857476d", "valid": true}]}