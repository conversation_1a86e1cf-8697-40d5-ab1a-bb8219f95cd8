{"id": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "className": "com.open_care.resource.OCResourceCapacity", "name": "OCResourceCapacity", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "025abfb7-ead7-4c44-8a4c-955011897a68", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "02b457df-919b-4966-817c-33cea0a72b82", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "10cdab38-d794-4378-9bbc-71ee15251d90", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "16fc576a-cb4d-4eef-814e-0b367babd316", "name": "capacity", "title": "容量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "29e85759-56f0-44ef-bdab-6556c7996a55", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "3026a987-8c7b-4c7b-9637-25d1226168fa", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "35f9d08c-bec1-4767-8b9b-5daabbe27703", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "3aaf7034-0c2a-42d4-b880-e6bd87b70c62", "name": "resource", "title": "资源", "type": "java", "classType": "object", "refEntityId": "7dd3506b-c0a7-4d8b-8def-73c2cc121012", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "56db4ede-ece3-4063-bbf8-27a14047175e", "name": "product", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "5cabb901-a48d-4859-b3d1-073a74171dd7", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "683c0334-5b21-43fe-9c38-43d86959beba", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "689cc31f-427c-431b-84c0-d7b43776b8e9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "6cf6e2ef-b774-4b97-891a-42af89e74bac", "name": "department", "title": "科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "8d102ed1-bc53-40b5-ab02-55b8f74ca056", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "9c9e093d-00b2-42a3-aa5e-0df1ec7e2768", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}, {"id": "eef91010-677f-4bb8-bc96-8d24d06a9871", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f51bcdb2-fcdc-4243-bbc9-f4f8b22ba3c8", "valid": true}]}