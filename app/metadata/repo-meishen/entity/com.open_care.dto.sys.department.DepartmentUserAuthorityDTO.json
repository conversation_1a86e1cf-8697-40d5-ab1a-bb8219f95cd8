{"id": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "className": "com.open_care.dto.sys.department.DepartmentUserAuthorityDTO", "name": "DepartmentUserAuthorityDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04b3c6c0-5abd-4100-a7fc-e2efbe5f5fc8", "name": "productIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "valid": true}, {"id": "0d3be4e6-2641-488d-8f89-23633d9b8525", "name": "defaultAuditor", "title": "默认审核者", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"defaultAuditor": {"$id": "0d3be4e6-2641-488d-8f89-23633d9b8525", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "默认审核者", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "118dd594-3135-4a6d-9a3b-06fe57657c27", "name": "resourceGroupId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "valid": true}, {"id": "19869547-280c-4438-9bb2-0997b1de2b9c", "name": "seqno", "title": "序号", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"seqno": {"$id": "19869547-280c-4438-9bb2-0997b1de2b9c", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "序号", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "292a3025-e520-488c-806f-f2e0a02cf2e4", "name": "defaultDepartment", "title": "默认科室", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"defaultDepartment": {"$id": "292a3025-e520-488c-806f-f2e0a02cf2e4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "默认科室", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4fa3117b-b3e1-4b49-befe-e03eeaa1d414", "name": "userId", "title": "用户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "style": "Select", "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"userId": {"$id": "4fa3117b-b3e1-4b49-befe-e03eeaa1d414", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "用户", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "5b44017a-cf68-4993-b659-ffd184fa4e87", "name": "resourceId", "title": "用户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "eea5cac5-c720-4b86-af4a-58f140ec6c90", "style": "Select", "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"resourceId": {"$id": "5b44017a-cf68-4993-b659-ffd184fa4e87", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "用户", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d72670bc-e905-43c1-8c30-deecf4218554", "name": "resourceName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "valid": true}, {"id": "e6958ade-c164-417c-8b62-538a274d7964", "name": "auditable", "title": "可审核", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "9f0693f8-4b78-44da-b8a3-9bb50063e755", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"auditable": {"$id": "e6958ade-c164-417c-8b62-538a274d7964", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "可审核", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}