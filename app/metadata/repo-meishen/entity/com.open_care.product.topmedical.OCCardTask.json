{"id": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "className": "com.open_care.product.topmedical.OCCardTask", "name": "OCCardTask", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "008341da-db4e-485b-bf23-cbde8439a0d8", "name": "riskName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "07d3c6f5-cf01-4b1d-8626-1919be86d525", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "10570946-2b37-40a0-a394-e4570f173b30", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "1a278f2d-a589-4437-ab4e-989374e376ee", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "1c77ece2-91f3-4ae6-aca7-9d21ecf32ad0", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "2b903581-ee8f-46c7-8109-62f7abf95e66", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "2dfcd619-0837-4a22-bfb8-d42f1d519647", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "30cd57c6-a930-42e0-9dce-c126dfaa4b5d", "name": "cardsNum", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "31a99bb4-b94a-4e83-8b71-33c0650eb5ed", "name": "effectDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "3a6f59af-1c8f-4b51-94d9-bda98614da33", "name": "purchaser", "type": "java", "classType": "object", "refEntityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "410a3de1-a30b-4f9f-be7f-e414e7ad5f21", "name": "insuredDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "46b0ae55-f3d6-4945-bf8d-701434086caa", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "67154ca8-ca33-4e79-96ba-a728bfe01884", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "83b6cbc2-60a4-4417-9004-b13e1b715650", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "8dc5f500-ed08-41c2-bf05-fbd09e5ef319", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "963d8d54-844f-402e-951e-2486278b1f96", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "a0f677ac-8371-4f80-b2ef-e88efed1cfe8", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "b65abf57-c841-432c-80d5-7e48b7100e74", "name": "policyOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "ca8df888-361c-49fb-a5a2-0badbae3aa9a", "name": "grpCountNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "ce94aff0-478a-4b47-8f28-085951ba68d6", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}, {"id": "fb2e3021-6672-4c62-90af-d446df10725c", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7695c7c9-3b71-422b-8dd7-666e6766cd97", "valid": true}]}