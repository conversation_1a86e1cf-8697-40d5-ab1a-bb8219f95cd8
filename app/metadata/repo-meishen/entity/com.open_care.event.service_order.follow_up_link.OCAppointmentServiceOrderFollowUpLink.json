{"id": "ad9b4331-7341-4a17-864c-99c026a0a928", "className": "com.open_care.event.service_order.follow_up_link.OCAppointmentServiceOrderFollowUpLink", "name": "OCAppointmentServiceOrderFollowUpLink", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "059590f0-d3ea-47e6-bc5c-e2ddf324baca", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "0ea7ef78-ab6e-43a0-b374-1017602a82f6", "name": "isServiceEnded", "title": "当前服务是否已结束", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "185ba364-e7de-43ca-a27c-c0039da1f390", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "204b4fd2-a862-4e76-ab13-507bb3618fb3", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "23282a47-a909-4c89-b75d-ad150b842053", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "24546b33-8999-4c19-ae6b-906d505e629b", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "26cba969-45e5-4df1-80f4-23cd1df5b156", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "27af8265-cbee-4bca-93c8-86ac4e6e3267", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "37a07347-5a94-4e99-9a6a-5520d9a825af", "name": "appointmentPlan", "title": "随访方案", "type": "java", "classType": "object", "refEntityId": "00180d34-4f39-43bd-9bbb-bd4015e44f65", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "3f7cd3ba-a110-452f-8797-09e3449ddc99", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "60588adf-0ff1-4bb3-95dd-a321afeac5dd", "name": "autoNextServicePowerJobInstId", "title": "超时检测任务id", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "6b98e6b3-64e5-48aa-9da8-f28303058086", "name": "recipient", "title": "实际被服务人", "type": "java", "classType": "object", "refEntityId": "2b95e4d6-4185-4b3b-bbff-6bd77b2dc8c2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "791efef1-d359-49b2-9479-bb06bf9c94b2", "name": "appointmentRec<PERSON><PERSON>", "title": "预约记录", "type": "java", "classType": "object", "refEntityId": "187a27be-5816-4891-8406-19626320027d", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "828646a0-7932-48ad-b650-3c0cf12af9b7", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "932b0927-a90b-4e76-abcc-6165b653e2c2", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "a88a8157-67d7-4cdb-baae-76e9a4f7a5fc", "name": "followUpMedicationRecord", "title": "用药服务记录", "type": "java", "classType": "object", "refEntityId": "f75e33c8-f061-4491-b202-cbb5987b046a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "ba0c169c-c5a3-4231-b58a-2a01bd217345", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "cf00e105-00d4-457d-91e3-b744b38eb8a1", "name": "annualSummary", "title": "年度情况汇总", "type": "java", "classType": "object", "refEntityId": "1116be4f-8c9a-48e9-9942-00f31000be8a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "cf27674f-80dc-4a65-b1bb-c067c9528fea", "name": "recoveryPlan", "title": "康复方案", "type": "java", "classType": "object", "refEntityId": "46c1573c-9e87-4e1d-a4be-e86eeb3a7a4c", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "e48dab37-fad7-40ea-9988-c8ab97b88789", "name": "appointment", "title": "预约单", "type": "java", "classType": "object", "refEntityId": "49cfba33-7ecb-442b-ac95-91d7fe113b8e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "e7b974b8-7cc2-4d9d-8c12-0eae5ad3e8a6", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "eaae84c6-2fc9-4e76-bc5e-b0b0683bdd19", "name": "rehabilitationPlanPowerJobInstanceId", "title": "康复方案规划的powerjob定时任务id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "f3dd644e-961a-46f6-b56d-d05c8278daf0", "name": "processInstId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}, {"id": "f5bbc0d1-544a-4afb-afba-8df606adb61b", "name": "currentServiceCount", "title": "当前服务次数是第几次", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ad9b4331-7341-4a17-864c-99c026a0a928", "valid": true}]}