{"id": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "className": "com.open_care.dto.product.topmedical.ChaseTaskDTO", "name": "ChaseTaskDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "05c9fb20-d67a-4b9c-aa92-76306542a269", "name": "msgRecordList", "type": "java", "classType": "object", "refEntityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "0dc494ca-f057-4706-9366-8b382cbd01dd", "name": "contactRecordList", "type": "java", "classType": "object", "refEntityId": "53dee07d-3533-45a0-983e-dcc43aaf69b9", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "0f0bfcde-a04f-49ba-bfef-c9ecf1dcb27a", "name": "insured", "type": "java", "classType": "object", "refEntityId": "4ed0f7a3-d7d0-4291-a8b7-fcd39d763a48", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "109bb03f-24f7-4aed-9415-4c9c5e91d48b", "name": "taskInitDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "12b3f4ee-3fb4-451c-a11e-9001909ad8ad", "name": "feeitemType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "173df13a-1baf-4896-bc31-1323a458b60d", "name": "batchNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "19b654c6-5f70-4e00-b76f-eccf387258ea", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "1c6c31c5-cc6a-438a-9a1d-e27bf6514416", "name": "caseReportNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "2b03db37-3b32-4e2a-8d49-0b446746157c", "name": "receiveTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "32cf271d-a5f9-42a9-b2ee-7c1263bf978d", "name": "clinicDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "35daafee-e16d-4512-b202-120514452255", "name": "gotUserId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "3e5b34bf-11bd-468a-a66d-3fb1ec39abe4", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "406f50d9-2ead-405d-a8a3-e943936d8aa6", "name": "finishTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "46d1b5b8-8b44-4849-b16e-ac8d0b35ae3b", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "48e33e6f-3895-4bdf-8b3d-27ad84699878", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "4dffa5ac-0100-47f0-ab2c-df2254ebec8f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "5417d973-7562-4629-8fca-cc2a2a3e273a", "name": "ifPbyByBillAmount", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "5801d9f6-fd89-4c32-b349-7c3670ef6e88", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "5cf82662-1660-42e8-b559-4ee4fd7edd5e", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "5e575c6a-838f-459b-8333-00e762fd9577", "name": "payWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "676e398e-73d4-44b2-b736-44d7eb1c05f1", "name": "hospitalName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "6b5676cd-5452-425f-a3b5-4e685a220a3b", "name": "payTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "6c4cca89-a904-4934-b823-8706e63f25a1", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "77c5f928-4976-4668-9b0c-27fb76954d0a", "name": "billAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "7a09b7c2-a619-4375-935d-094abd4b3356", "name": "gotUserName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "811395bb-c628-449b-a8ed-5a72cdc1adc6", "name": "processTime", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "81bd4384-7de1-41a0-8a49-d393743e0cda", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "843a9c67-8737-40d3-b7c4-88adb8569a28", "name": "claimApplyDTO", "title": "理赔信息", "type": "java", "classType": "object", "refEntityId": "e44674de-0b4b-4421-91bb-f64c31b47ba8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "846ce694-25e3-4920-855c-31f9d0ab7b6e", "name": "hospitalId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "943b2562-5b0f-4bc9-aa5a-8ed85c7f5364", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "9958443d-6f4f-4963-962a-ca8af5321ff3", "name": "opinion", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "9ee09d25-b5cf-480f-a06f-8164f40af597", "name": "gettableTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "a6daaf68-f0f3-48af-ba45-6b177734fcad", "name": "caseCloseDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "a9679b01-532c-49a9-92bd-3b897db7847f", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "b6960f29-ab9e-44f8-bdc4-d745606e8673", "name": "chaseAmount", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "c6ce5806-93dc-4265-9a34-af32cd6e9a57", "name": "contNoAndGrpContNo", "type": "java", "classType": "Map", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "ca2acf78-8ce6-4405-bcf2-a4ba1fb78146", "name": "ifSubOrgAgree", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "cfd05816-0883-4edf-ab01-ede6bffd330f", "name": "policyDTO", "type": "java", "classType": "object", "refEntityId": "23886c00-1d22-495c-83d3-43b7c2604552", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "d5045988-06ab-4ff7-af61-a67492e9b06e", "name": "claimAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "d57f0b9e-76ed-4b6a-ae73-6c3178d829a6", "name": "insuredMsgList", "type": "java", "classType": "object", "refEntityId": "e993b002-710a-4d4d-bcc5-5a0aeb90c420", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "d7c66a25-af7b-4aa6-a6f7-2442f5c1151f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "e2b31a2d-0396-45a8-b594-181e80cba03a", "name": "purchaserPeople", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "e377991a-1cf3-448f-ade7-c298647b137b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "e6421738-dee7-4f27-be36-d125a4e2a2e0", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "eac692e2-91cb-49d3-9e2c-492438ae4530", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "eff4a6e4-c879-40c4-ae8f-db9c12b63ef6", "name": "applierMsgList", "type": "java", "classType": "object", "refEntityId": "e993b002-710a-4d4d-bcc5-5a0aeb90c420", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}, {"id": "feb40ec2-f754-4f97-ac37-7abf4efc5d35", "name": "vipSign", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ff31264-648d-4911-8d4e-c7c4fea84d7e", "valid": true}]}