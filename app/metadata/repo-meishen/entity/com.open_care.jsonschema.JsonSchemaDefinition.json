{"id": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "className": "com.open_care.jsonschema.JsonSchemaDefinition", "name": "JsonSchemaDefinition", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0afbc8a4-783e-4725-bb67-cd9e3a2998a9", "name": "ocSchemaId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "103cc68e-76b8-46a3-9d4b-3dea78304f23", "name": "appInstId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "1f161fe1-2266-4858-bb78-2513ac3594b1", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "1f6540f7-59cd-4704-9ae6-c7100de6d769", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "2cd43688-3650-4bd6-b20a-7f78d65107c4", "name": "appDefId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "5e948add-5f65-4cf8-8cfb-423e8d6289bb", "name": "jsonSchemaDefinition", "type": "java", "classType": "object", "refEntityId": "5ca64122-a368-437e-a86d-29536a6be204", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "785f52ae-6dcc-41a6-926d-8ac9410af7d6", "name": "id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "7a48ab04-3056-4f90-909b-997ec264e8bb", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "7c70c193-68d1-4c75-9f93-b25e21d8479b", "name": "jsonSchemaData", "type": "java", "classType": "JsonObject", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "9478bf97-4c73-44ef-8292-f8efdc740d02", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "b1119475-c795-4fa4-ba56-ccd25089c8bc", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "c25677c0-9c45-49d4-b983-ed31affd5bbc", "name": "client", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}, {"id": "f3a07349-e51e-40a1-b29f-6e8fc440486e", "name": "tenantId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ff5cba3-d6db-4c5e-b626-3e4286bd13b1", "valid": true}]}