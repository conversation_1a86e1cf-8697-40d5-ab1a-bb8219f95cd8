{"id": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "className": "com.open_care.medicalMicro.medical.OCBaseMedicalTemplate", "name": "OCBaseMedicalTemplate", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "模板详情", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0004f130-fbf0-4295-91f2-63b8f9367cd7", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "0b66c525-2bce-420a-916e-27d3bc928b45", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "0f11be15-164a-4649-8e9a-15274bcec1a9", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "16195eea-c2ee-4958-ae8e-9446d7d6d5cd", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "165d036a-ba4b-4157-92f3-e1e670c5f48f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "178f1784-0410-4a7d-a268-756fb31e872d", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "3159cf98-2a58-46b0-8a4b-e099a90913d5", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "38d91014-b1f8-4bf4-a4cd-a88d4edf09f3", "name": "sourceDepartmentOcId", "title": "模板来源科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "4d56fe85-6979-4893-8a8e-065b31f636d7", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "635d191a-e474-4081-8752-727262f8c8f7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "837075a8-c2f9-4158-907f-75198a71a2ae", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "86a26d30-6884-49d0-96b4-b0b73f21522d", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "8793198e-0ba1-41de-9f80-965470e32e52", "name": "templateContent", "title": "模板内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "8db3dddc-771f-45b7-a1b9-43f05679ba9c", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "a4cf3fd5-e7c9-4c43-a503-1ee5da4f8187", "name": "templateType", "title": "分类", "type": "java", "classType": "object", "refEntityId": "025ca549-fb8c-4e44-9dc7-0cff2708254b", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b7972db7-22b4-481a-960a-21e5123c3ab4", "style": "Select", "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"templateType": {"$id": "a4cf3fd5-e7c9-4c43-a503-1ee5da4f8187", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "分类", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a6443a75-3a75-4ca4-8828-e0d62b0783f3", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "a93d1551-1200-4eb1-be9f-fe6c7f1f2270", "name": "medicalRecordTemplate", "type": "java", "classType": "object", "refEntityId": "da8d7215-97d4-467a-8a72-e6a0dd272114", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "a96d3f68-6480-4e68-9302-c0bdd208fa0e", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "ada4a02f-01c1-44d6-bd48-8ade654e79d8", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "c4317b83-4754-4abe-85dc-0c895d86f9ed", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "d24d9a64-6829-47f0-be4e-a5b324676e1b", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "d595f577-54eb-4c51-9b10-3d64ac631235", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "ddd41664-4c6d-41d3-b689-e4c15db50609", "name": "sex", "title": "适用性别", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "dff97745-c629-4af7-b489-997aa475b92c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}, {"id": "ebe3331f-c141-4d48-ba50-9816db228824", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5bd086af-5ca4-4fa3-9aa9-b18e8b0f566d", "valid": true}]}