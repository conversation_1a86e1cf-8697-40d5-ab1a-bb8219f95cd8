{"id": "79b19015-7f68-49ef-a103-c388f74d7bb9", "className": "com.open_care.stock.OCStockInApplication", "name": "OCStockInApplication", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0636edc6-5625-4003-969d-fce50c45c019", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "2050afd4-2ff0-4b4b-9c98-ed66e723c8a1", "name": "stockInApplicationItems", "type": "java", "classType": "object", "refEntityId": "dae58c14-0edc-4288-94df-a88eb044efe4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "2597a1c7-1be9-41f2-8059-c9fa56b7587c", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "26af18cb-af33-4f75-ab16-52aadbe53125", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "34f50596-00cd-40b5-a04f-e0896e88552c", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "4900b6b7-4e90-4170-bd56-7aeb16f1cd9a", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "5566bbfc-f195-4b94-bbec-595b08dd6893", "name": "method", "title": "入库方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "810d2bf6-ffd0-419b-b027-16849b7793cf", "style": "Select", "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["method"], "properties": {"method": {"$id": "5566bbfc-f195-4b94-bbec-595b08dd6893", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "入库方式", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "587019c4-ca6f-4c09-9b63-ce2a8ecce127", "name": "operateTime", "title": "操作日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "61511e71-e8b1-4fcc-85ae-a27580495c0a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "7286ad6a-4dfc-4fc2-b499-a5ed723dec94", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "85305601-9402-4cae-a3f5-00c3aa0dd412", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "87f259e7-8bee-419f-8991-3cac497c1c9f", "name": "wareHouse", "title": "领用药房", "type": "java", "classType": "object", "refEntityId": "7793905f-066a-4cd8-85e8-0059dc7c9b10", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "5da04725-1de4-4653-a7a9-682cd5310136", "style": "Select", "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["wareHouse"], "properties": {"wareHouse": {"$id": "87f259e7-8bee-419f-8991-3cac497c1c9f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "领用药房", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "958cf787-cb7e-4798-bed8-3799197cd8eb", "name": "stockInApplicationNo", "title": "入库申请单编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "96270588-94ee-4d38-a43e-c8eca60e08da", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "a15600e7-20f0-4300-9cb8-097bb275881e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "a63298b1-0064-48b0-a93d-5f5c8d62b112", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "a79fe6f1-7842-4189-a933-6719b65b46f4", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "c0f8820e-22f3-4814-9c7b-779c634fc012", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "cc84d4df-0d35-4a50-a76a-7c8f5a44a1f8", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "cda40f06-66a4-4436-a7f3-cc79bc62c026", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "d17ed44e-0b1b-4806-b92a-3872285d7206", "name": "operator", "title": "操作人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "style": "Input", "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"operator": {"$id": "d17ed44e-0b1b-4806-b92a-3872285d7206", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "操作人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ef4e2e1d-4695-44fd-ad69-61995d3adeeb", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "f125883e-825b-494e-b4d6-c11e02f237a0", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "valid": true}, {"id": "fb30e19b-76e5-4c53-b6b0-8340d46d9015", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3e41775a-9b62-439f-adab-6d30c77b1804", "style": "Select", "entityId": "79b19015-7f68-49ef-a103-c388f74d7bb9", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"status": {"$id": "fb30e19b-76e5-4c53-b6b0-8340d46d9015", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "状态", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}