{"id": "d4b7e76f-6c00-491d-b1f7-44a582cebda8", "className": "com.open_care.dto.wordOrder.AssignedCount", "name": "AssignedCount", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "406d0aa6-ddac-48fd-9275-db73315f6f12", "name": "oldTaskCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4b7e76f-6c00-491d-b1f7-44a582cebda8", "valid": true}, {"id": "a08ff682-efff-44a4-a9e6-bc877021b8c2", "name": "totalCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4b7e76f-6c00-491d-b1f7-44a582cebda8", "valid": true}, {"id": "be735b44-d52f-4d76-b7af-6bb63a7c975f", "name": "newTaskCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4b7e76f-6c00-491d-b1f7-44a582cebda8", "valid": true}]}