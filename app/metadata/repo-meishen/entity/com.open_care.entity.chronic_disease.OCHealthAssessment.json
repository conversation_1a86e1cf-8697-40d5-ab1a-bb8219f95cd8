{"id": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "className": "com.open_care.entity.chronic_disease.OCHealthAssessment", "name": "OCHealthAssessment", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "091fde94-d84e-4420-b969-aa9a88ec6260", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "1456d455-2085-4332-be2c-1b48c64f90b6", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "221a573e-64ea-4d67-b1a3-a33bb77b1868", "name": "heightCm", "title": "身高(cm)", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "255b3983-73ef-420b-9002-9c9c18936409", "name": "hba1c", "title": "糖化血红蛋白", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "2736db23-d162-49c4-9d4e-bb52d6398b74", "name": "bmi", "title": "BMI", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "2f8947f2-031c-4eac-a6bd-366cfde034ea", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "34223a8c-adee-4555-9218-e00bffb9a4fb", "name": "assessmentDate", "title": "评估日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "46251710-2b7c-491e-9b39-abd5b2517750", "name": "weightKg", "title": "体重(kg)", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "4acb67ee-3c4a-4c22-8e02-b29f00b0d59e", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "5a9f5da0-dba6-47ab-9c66-fc86a75a0715", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "6f0bc1b7-45c2-486f-a4e5-979957c4c865", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "7fe5e922-d8e1-4e5e-9533-4502fcfa11f9", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "8c22406f-2932-4a90-a50d-b3e6987a144a", "name": "fitnessTestResults", "title": "体适能测评结果", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "958729ce-6736-4bf9-8da4-5deaa0133ff4", "name": "postprandialBloodGlucose", "title": "餐后血糖", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "a7185be4-0ba3-41b2-be8d-2d476a31ee2d", "name": "bloodPressure", "title": "血压", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "a75c9616-88ef-4e08-9d35-0c6931106818", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "a923f970-82ab-402e-b424-f8e2584470ef", "name": "fastingBloodGlucose", "title": "空腹血糖", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "b415e3c7-ba6d-4461-89e7-dbc556893da5", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "ba561d46-3ca0-46e6-9ac1-30a3638d0573", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "c4ea08c1-42b2-4de9-96db-f68d5d4d0a7a", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "efa376e8-1cc1-442e-9115-0780cd9cbd2f", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "f8ba3c30-50f5-4859-b57c-48cf8ab8cb98", "name": "appointment", "type": "java", "classType": "object", "refEntityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}, {"id": "fbdac91f-6f3c-4982-9ddc-96df9c6880bc", "name": "otherComplications", "title": "其他并发症记录", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "valid": true}]}