{"id": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "className": "com.open_care.entity.chronic_disease.OCQuarterlyReview", "name": "OCQuarterlyReview", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02ffe8e9-81af-4b29-8658-20dc983d2a86", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "03b179c8-a2be-446a-844c-340909bf2156", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "19a58caa-ff80-4518-90ab-aefd25d2abf4", "name": "reviewDate", "title": "评估日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "1ab4db8c-3ebd-4262-9564-cfbba7a9ed6c", "name": "currentHba1c", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "1e20078d-1553-4456-9f07-aee524e6657b", "name": "complianceAssessment", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "1f5885ec-bf03-48ef-9f8e-7ccebbbd5145", "name": "adjustmentDetails", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "24bc9408-cf22-48ba-a4fe-68d904205054", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "2766c067-ded4-4037-8de8-1a0d5a0dda76", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "284e2e04-2a5a-46d2-a4cf-8879178ad969", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "322347fd-9d04-47ac-9f57-8e901af136f5", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "507b9330-41f4-42d5-8397-895a6cc9f51e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "57541630-9a2d-464c-b3f6-a9ef6fae06ea", "name": "goalMetStatus", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "5a464775-f6f6-4a17-b247-c71b1a675f2c", "name": "appointment", "type": "java", "classType": "object", "refEntityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "660e2f0b-72f2-4546-854e-45423c9b1b53", "name": "continueManagement", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "67730dfe-513e-4a52-879c-d80d427a325f", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "7c34580c-c913-476b-93e5-ceaba1c7d5e0", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "7df0b734-13b2-465f-a62e-79586241c2fc", "name": "planAdjustmentNeeded", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "a0a88c9c-a6e9-4f20-b4a4-a6973b673acf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "ce920e42-ed5d-4e8b-ac43-4340a462e1eb", "name": "reviewedBy", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "d6724a0f-8b4e-48a4-a6cf-6f4807b14abe", "name": "glucoseSummary", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "de747efb-a8b9-4cca-af97-c70cdceb22b3", "name": "assessmentSummary", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "eed1df34-6c99-4f20-b551-34a32c30f713", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "f515fd6b-ecde-42cb-b38b-97580e5f9246", "name": "complicationScreening", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}, {"id": "fef679e5-aeda-437a-a80e-75d01d66719c", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "valid": true}]}