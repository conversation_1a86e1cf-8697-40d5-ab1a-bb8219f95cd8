{"id": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "className": "com.open_care.jsonObject.HisScheduleInformation", "name": "HisScheduleInformation", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "609c5035-45ed-4629-a997-e5875081c64c", "name": "timeInterval", "title": "时段", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "830cdc2b-5fb4-44d0-bd1c-d7e9512a98dc", "name": "numberClass", "title": "号类", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "93a8bd55-2676-4d18-80db-4e75d364a124", "name": "numberCategory", "title": "号别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "99d98047-4d1b-4d35-a6eb-2c80edf64081", "name": "actualCharges", "title": "实收费", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "c4d76e4e-8b18-41f4-b17f-208c4ec13556", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "d4112622-1bb8-43db-af7d-aec449c7234c", "name": "diagExamFee", "title": "诊查费", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "e445275a-3bbe-4598-a88b-e635b7d92918", "name": "registrationFee", "title": "挂号费", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "ee8060ea-97ed-4a55-8e01-7e3a21382137", "name": "otherFee", "title": "其他费用", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}, {"id": "faa37fec-8960-4581-8dbd-6f16cfce7450", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "7ad8ad6e-8a6b-421a-b5c7-ac8b83cfb9be", "valid": true}]}