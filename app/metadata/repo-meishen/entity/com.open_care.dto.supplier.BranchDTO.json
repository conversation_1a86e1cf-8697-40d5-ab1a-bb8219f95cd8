{"id": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "className": "com.open_care.dto.supplier.BranchDTO", "name": "BranchDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0c8f371f-1a18-4073-8a40-e0d0dff1edcb", "name": "subscribePreDays", "title": "提前预约天数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "1159c568-07ff-490c-b14c-b79e5503eac3", "name": "intendedFor", "title": "适用人群", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "19fc33ef-402e-428f-96a8-be02931bd410", "name": "updatedBy", "title": "更新人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "1b3bda03-48c7-47ff-8408-76e35ddcffe8", "name": "partyRoleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "27372b96-52b4-446f-acca-2b826ffa0d22", "name": "planAndContexts", "type": "java", "classType": "object", "refEntityId": "5df4fe41-a389-4c02-a1b0-1a74ef3cb5ce", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "2c461059-e7f2-466a-9c4f-c8bcd4969ac2", "name": "otherBranchId", "title": "对接供应商网点id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "3a04b7da-64ed-4368-8d75-54c959a5e55b", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "54b3a302-91f5-48a9-ab56-177b4a7eb893", "name": "servicePlans", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "55a72dcf-370e-4f8e-93c8-33e655d53694", "name": "auditStatusJson", "title": "审核状态", "type": "java", "classType": "object", "refEntityId": "9a815837-52c2-4449-9fdb-c0271fd3c337", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "5673a688-be3e-41d9-9e90-dc37671ca3dd", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "java", "classType": "object", "refEntityId": "6e93f702-02a3-46d2-8a4b-da3918781fe5", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "56cc81a7-4c03-40e2-9a51-522129a93651", "name": "srcName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "581019b9-b7dc-40ed-82b0-556ed9d78514", "name": "longitude", "title": "经度", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "5a35b0ab-6ec1-494c-ab27-f28f5756e2bb", "name": "servDaytimeStart", "title": "门店上班时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["servDaytimeStart"], "properties": {"servDaytimeStart": {"$id": "5a35b0ab-6ec1-494c-ab27-f28f5756e2bb", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门店上班时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "5b2e80a8-cd63-4e9d-8b2d-a764db028b8a", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "5bd64cb8-69b2-40bb-aee1-03909dc3a672", "name": "supplierOrg", "title": "资质类型", "type": "java", "classType": "object", "refEntityId": "077fbecf-8104-4f20-9f1a-bdef92dc52cf", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "65329ed7-6779-4c36-a42e-9de04a48a782", "name": "holidays", "title": "节假日安排", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "6e0157a0-4a5a-48de-9438-d669e14349f8", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "719e00c0-d19e-4e72-aadb-3f2a77e35e2d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "746cbb28-1322-4b5b-9896-a8ee77322db8", "name": "latitude", "title": "纬度", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "76419825-b20c-4426-8a38-c866de2c7729", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "7cabb8c7-748e-4c4c-92f2-2d2b349be7f1", "name": "contactPhoneNo", "title": "对接人联系电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["contactPhoneNo"], "properties": {"contactPhoneNo": {"$id": "7cabb8c7-748e-4c4c-92f2-2d2b349be7f1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "对接人联系电话", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "83627e70-5754-4170-8e25-afcb7e4da1cf", "name": "branchNo", "title": "网点编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "862bfc39-af7e-4cdd-a9f0-1cd34c940ccf", "name": "organization", "type": "java", "classType": "object", "refEntityId": "71a5a33e-3569-4da0-a51a-f04534e74be5", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "88473c15-1aef-4425-b6c9-6d94bda73668", "name": "vendorURL", "title": "对接平台地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "88585d17-e572-488e-a520-471a2217ded6", "name": "supplierId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "8d63d64c-78cc-4979-b71c-8a80d8b641e0", "name": "selectedBranchIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "8eaa3d66-64cb-4afe-b5dd-1b21e2c3b834", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "963f273e-009e-497d-a6da-2afbe2d235d5", "name": "productId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "97c6a0a5-bfe4-4d4b-ac2b-d3800b2e8c71", "name": "processInsId", "title": "流程实例id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "9d5a325a-ffe6-4a16-ad91-334efdcbce0a", "name": "branchLicense", "title": "医疗机构执业许可号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "a00daf37-49f9-4f09-9399-e0d1e5c0e677", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "a0ff08b2-f199-4907-a7e3-5666472c89c2", "name": "abbrName", "title": "门店简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "a2274f2b-bf57-4cdb-a09b-8e84f2df0565", "name": "created<PERSON>y", "title": "创建人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "ac343600-4e2f-442d-997d-a52b9eb01859", "name": "updated", "title": "更新日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "b15190a2-0178-426c-a80a-f5a10efb3636", "name": "branchStats", "title": "门店状态", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "b243635e-a015-4828-adbe-c82f33be240b", "name": "servPhoneNo", "title": "客服电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "babbcb5a-4d88-4deb-bdb8-4e487b114bcb", "name": "profitLevels", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "c3a1ca24-5638-4929-84e5-4770a4dc7334", "name": "branchName", "title": "网点名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["branchName"], "properties": {"branchName": {"$id": "c3a1ca24-5638-4929-84e5-4770a4dc7334", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "网点名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c3ed805d-5141-4dfa-b159-aac5f7217c5c", "name": "region", "title": "省市县信息", "type": "java", "classType": "object", "refEntityId": "e9097e72-d407-4a68-b6ca-94065614e460", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "c40bfafb-447b-4848-8485-f754d97fd7ae", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "c9911a7b-4ad7-42cc-8c71-3d3d975321f5", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "cad1c89c-1d38-4ae8-9533-6eb994418fc4", "name": "businessLicenseAttachments", "title": "营业执照附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "cc7b5562-329c-4038-b41d-18fef695afb5", "name": "created", "title": "创建日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "cd6324f9-fb9c-4ec3-8f12-e7daf7d7b309", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "cf8d6f40-b5c8-4732-9f94-e73d1fbda0ae", "name": "branchOrg", "title": "所属机构", "type": "java", "classType": "object", "refEntityId": "6185b714-0292-423d-830b-ef642633a22a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "d1c1be77-46ef-4d0c-8ade-43e1a67c3e6a", "name": "certName", "title": "营业执照名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["certName"], "properties": {"certName": {"$id": "d1c1be77-46ef-4d0c-8ade-43e1a67c3e6a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "营业执照名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d6992ab8-5153-4aa4-a223-b4c08a920ba6", "name": "attachList", "title": "附件列表", "type": "java", "classType": "object", "refEntityId": "c844322a-52f0-4d7b-ae61-921b5e4b7b7c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "d6d9340b-5420-4482-898d-2b38f6ea9e20", "name": "ifHasVendor", "title": "是否对接平台", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["ifHasVendor"], "properties": {"ifHasVendor": {"$id": "d6d9340b-5420-4482-898d-2b38f6ea9e20", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "是否对接平台", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d71b9722-111f-4b21-8408-79fd0d2b09f1", "name": "servDaytimeNote", "title": "营业时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "dd03ce8f-711d-42ae-b73a-7b3d9a7ead67", "name": "party", "type": "java", "classType": "object", "refEntityId": "39f9bf4f-b0fa-41eb-abf1-0d2dfbb2c2ab", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "e3479d6f-8f38-4822-91a4-14fac8f94bad", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "e842fc11-805e-4bd3-bcd0-5b4d525f1923", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "eb48731c-7fbd-4c2c-b1fd-ff500389558b", "name": "emailURL", "title": "邮件地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "ed8f7f66-48ad-48a4-a80e-b790f1d2f51b", "name": "otherInfo", "title": "其他", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "f221b25f-caed-4110-974e-0c542d6cc38f", "name": "contactName", "title": "对接联系人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "f483304e-fce1-460d-b5f1-3a37a50ed576", "name": "orgIDNo", "title": "统一社会信用代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "f6319572-f949-4421-a87f-f337c95f872b", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "valid": true}, {"id": "f873df90-7b85-442a-8d48-3f72dffc3bb6", "name": "servDaytimeEnd", "title": "门店下班时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d4930e93-a7c9-49a5-a04f-ae1a007d25ce", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["servDaytimeEnd"], "properties": {"servDaytimeEnd": {"$id": "f873df90-7b85-442a-8d48-3f72dffc3bb6", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "门店下班时间", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}