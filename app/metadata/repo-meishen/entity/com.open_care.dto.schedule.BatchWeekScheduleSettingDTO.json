{"id": "1817c956-ee22-4b63-9bd8-415adb711b1c", "className": "com.open_care.dto.schedule.BatchWeekScheduleSettingDTO", "name": "BatchWeekScheduleSettingDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "08e9ffca-c792-4b66-a393-f134c18fe1af", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "090962c7-df79-4db2-b019-ba3d99748c08", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "28244332-2493-4584-93aa-cc9d478c8115", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "4119a9c3-c7d1-4c7d-adcb-bcf10fcb88da", "name": "startDate", "title": "开始日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "4576639f-2edd-454b-98c6-7e32446d65ee", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "48411006-c0c7-49a8-b953-54af19ebc2ab", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "494b81f2-eb94-43c0-b7c2-38ea44bdea69", "name": "wedShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "4cafcf58-35b4-4eef-98ce-6338819ec3dc", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "51a237bd-5c21-465d-8116-ffb90de0c6b7", "name": "friShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "6731403a-d8cc-4b42-bc0b-c7a4437b24b3", "name": "resources", "title": "排班资源", "type": "java", "classType": "object", "refEntityId": "7dd3506b-c0a7-4d8b-8def-73c2cc121012", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "6aaa9777-d149-4db8-a74f-5ff72ece61aa", "name": "endDate", "title": "结束日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "7500ca86-bbd4-4cc8-bd46-7b0f7211c27d", "name": "batchResources", "type": "java", "classType": "ResourceAndDepartmentDTO", "refEntityId": "ba3a03a1-fc29-4a35-80e1-d096aea4d569", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "7b8022d8-18c1-4e4e-b8f1-87365e72731f", "name": "tueShift", "title": "周二班次", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "7cd10aa1-8f26-4c2c-b8bb-933eb4417f59", "name": "sunShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "8178a59c-8810-4d66-86e4-d00de41d01e3", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "9b262f41-239a-43c0-bab4-9fd293a542f9", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "a7aaa98e-73f3-4064-b9b1-ca2111422959", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "a8895f80-6b9d-4dab-af62-d46110f5bbd8", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "ade84828-4c21-453a-8ecd-68667ca96339", "name": "resourceGroup", "title": "科室", "type": "java", "classType": "object", "refEntityId": "269f6076-b2f0-4301-9cba-8f40d7c9818a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "b07e2b39-8ace-4392-8f53-9d52e21d490b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "b11567e0-d69f-47d5-931f-80f9dc023b34", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "c75e88b9-ee1a-4c3c-8bd9-b127051055b6", "name": "thuShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "cf676402-aeb1-48cc-ac6e-99ac894cd011", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "d32e16ac-c857-4029-9141-40c791317674", "name": "monShift", "title": "周一班次", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "d43f6965-e77e-49b8-b12d-93a86933aa33", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "edeef23d-11b3-4263-bce4-a4acb87ba176", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "f307d081-97d9-4942-b5be-618f67bae1c7", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "f93b3ae8-f73b-49c9-af32-4ea82755f3f1", "name": "satShift", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}, {"id": "fb2b531a-87b8-41b0-adf9-4c87a44da75e", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1817c956-ee22-4b63-9bd8-415adb711b1c", "valid": true}]}