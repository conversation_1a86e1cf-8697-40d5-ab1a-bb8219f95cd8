{"id": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "className": "com.open_care.resource.OCResourceProperties", "name": "OCResourceProperties", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "44de6163-561e-429e-89f2-b12e09f57afb", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "valid": true}, {"id": "68124aa7-ed10-42ef-91e9-1224704ec01c", "name": "canScheduling", "title": "是否排班", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "valid": true}, {"id": "89f0e2f5-44c3-4ac9-a2ec-a68c967022fe", "name": "canBeBooked", "title": "是否可预约", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "valid": true}, {"id": "c52f73fc-08ce-4fb0-9e79-8e23ccc1df10", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "valid": true}, {"id": "f3de4fe0-c0eb-4e80-a36a-6f2a5e18f50c", "name": "products", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "2daecdfa-6b70-4cd6-86a4-a579640eabe9", "valid": true}]}