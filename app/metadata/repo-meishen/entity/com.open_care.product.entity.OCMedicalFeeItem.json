{"id": "f1775bad-15de-426e-a32f-54d16a53d40d", "className": "com.open_care.product.entity.OCMedicalFeeItem", "name": "OCMedicalFeeItem", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "医疗收费项目", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "013833e2-7d11-49ce-816e-e0498354b3c2", "name": "itemName", "title": "项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["itemName"], "properties": {"itemName": {"$id": "013833e2-7d11-49ce-816e-e0498354b3c2", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "1c2a4083-e9ec-48b4-aedd-94c7faffebc1", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "23283310-c06e-4edf-b6a1-7ef68603153c", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "296969ed-f44b-4820-a3ff-670b3624f2be", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "2ee18aaf-82c4-4ebe-8694-114d33de5e09", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "4c44e7e8-d99e-4575-918e-c682e51f37e0", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "5d3019fe-2732-441e-9601-5d3879a29cb5", "name": "itemType", "title": "项目类别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "6099e077-2a8f-47b0-ab38-d82dfcdba4db", "name": "basicFee", "title": "基本收费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["basicFee"], "properties": {"basicFee": {"$id": "6099e077-2a8f-47b0-ab38-d82dfcdba4db", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "基本收费", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6dcd929c-1e02-4e51-acf9-2f4d2546e9dc", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "7e18f17a-332d-4a85-ba70-785cb2e56d5b", "name": "itemNo", "title": "编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "accdb0f7-3233-4ca7-b038-afafdf00852c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "b9365e79-a264-4de1-9b81-90624e9b8859", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "bfb67ea7-1ee8-4fa0-9cbb-ba89eac127eb", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "e1e567a2-05af-4d57-9c42-b804e5a92647", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "e4042f61-33a2-4760-a072-f658eddd2c8e", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "eafcbf84-9aeb-4e29-8060-5557735542f7", "name": "healthMaterialFee", "title": "卫生材料费", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "f0a99108-c328-44c3-8e14-6b2b3ba0db6a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "valid": true}, {"id": "fe2fd6d2-18e8-4166-a6cc-0c964e58ca3e", "name": "unit", "title": "计量单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f1775bad-15de-426e-a32f-54d16a53d40d", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["unit"], "properties": {"unit": {"$id": "fe2fd6d2-18e8-4166-a6cc-0c964e58ca3e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "计量单位", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}