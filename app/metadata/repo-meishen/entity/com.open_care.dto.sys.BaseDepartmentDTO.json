{"id": "8059a882-e610-49f1-8fa6-7b608dec57b4", "className": "com.open_care.dto.sys.BaseDepartmentDTO", "name": "BaseDepartmentDTO", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02d30745-54ab-472b-bc47-505dd483a308", "name": "departmentType", "title": "类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "valid": true}, {"id": "05672d30-deec-4730-869a-fe070e14e51f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "referenceId": "da973ffe-91ee-4e34-bf16-d78cf63aa12b", "style": "Select", "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"ocId": {"$id": "05672d30-deec-4730-869a-fe070e14e51f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "ocId", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "23d48dba-6cb3-4a9e-a131-ec79370c2cc1", "name": "seqno", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "valid": true}, {"id": "33e23f67-7840-4731-8f18-7b7251c5f82f", "name": "defaultAuditor", "title": "默认审核者", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"defaultAuditor": {"$id": "7be75b45-7df0-4a6c-8c95-fb9fbf215003", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "默认审核者", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "5b80df57-1ee5-434b-ba91-e7d8b46870d3", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "valid": true}, {"id": "617e0feb-f2bc-4d82-afc1-cc474c12f615", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "valid": true}, {"id": "af6ef52a-484b-4b27-94d2-2602e42e08ac", "name": "auditable", "title": "可审核", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"auditable": {"$id": "7be75b45-7df0-4a6c-8c95-fb9fbf215002", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "可审核", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ca5701ae-400c-44bc-8361-9417ab5185bc", "name": "serviceProductInfos", "type": "java", "classType": "object", "refEntityId": "8b6ada80-b69a-4199-819a-99fab1790a79", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "f57cd7a3-b987-4d00-bfb7-f46293c260f3", "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "valid": true}, {"id": "e7fe8818-c492-498f-885c-3243f3d6618a", "name": "baseDepartmentOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "valid": true}, {"id": "f4c7dcd3-21fd-489d-ae78-4747c2129a81", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "valid": true}, {"id": "f6ddb08d-cde8-4ba6-960e-2e4821b9850b", "name": "defaultDepartment", "title": "默认科室", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "8059a882-e610-49f1-8fa6-7b608dec57b4", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"defaultDepartment": {"$id": "7be75b45-7df0-4a6c-8c95-fb9fbf215001", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "默认科室", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}