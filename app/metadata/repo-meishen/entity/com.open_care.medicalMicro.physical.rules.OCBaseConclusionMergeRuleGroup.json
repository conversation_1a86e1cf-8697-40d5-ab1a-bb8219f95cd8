{"id": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "className": "com.open_care.medicalMicro.physical.rules.OCBaseConclusionMergeRuleGroup", "name": "OCBaseConclusionMergeRuleGroup", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "结论词合并规则组", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "098dca97-2dbc-4ec7-8f85-20765a84caad", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "0d730d2e-dae5-4954-b0d5-3347be90436a", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "16e79ce4-0e42-4a5b-b926-0839656772d2", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "18369b1f-325c-407c-bdf6-19b7c50ab3c9", "name": "baseConclusionMergeRules", "type": "java", "classType": "object", "refEntityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a20", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "27df7ee5-eae8-47b9-93f6-60f20969d6ec", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "338c9b48-323e-4a7b-ae48-331c50a54b64", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "385dff42-7047-4390-83c0-c91830e2297b", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "4d08530c-2f93-40f5-ba11-89ecc8dd316d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "4d34bf20-ee7b-4575-854f-f80634dc1a5e", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "500e0efe-fbcf-4ff5-bb9c-a30b516d0b12", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "5cbb632b-9379-43aa-b01a-f827dd923fb0", "name": "name", "title": "规则名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"name": {"$id": "5cbb632b-9379-43aa-b01a-f827dd923fb0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "规则名称", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "5f3ce90b-1f7f-4ad4-9f0f-a5264646e750", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "5fbb372d-6276-4f47-aa12-d20745b8dea3", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "607ef104-6d10-41f3-bf6f-c79862f78da8", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "62e8b94c-2f03-4145-a5dd-996a5b116de3", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "70597322-1148-4263-affc-ab344e2e56d5", "name": "fullyMatchMergeRule", "title": "完全匹配合并规则", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "8bf6cef3-8738-4e16-8639-7f7b3d60ecc8", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "8dbb395e-b4eb-489a-88b4-fb39dd0b0dbe", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "b1d204e4-9c8b-42c2-ac41-606319d00217", "name": "forUseInDepartment", "title": "在科室中使用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Checkbox", "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "false", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["forUseInDepartment"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"forUseInDepartment": {"$id": "b1d204e4-9c8b-42c2-ac41-606319d00217", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "在科室中使用", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "bfafe23d-05a3-4f43-bb6b-b9b4c3a901cf", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "e4eef1a3-64fc-41c9-a14e-271bd56dc4fd", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}, {"id": "e822a2f4-5f3b-4314-b8a6-118b96bd7be3", "name": "productOcId", "title": "产品", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "36da498f-388e-493a-bfe6-80058963245c", "style": "Select", "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"productOcId": {"$id": "e822a2f4-5f3b-4314-b8a6-118b96bd7be3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "fcc68ebc-8487-463a-b79d-53049e89a62a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ba1e3879-3e29-4179-a0a9-0edb12d13a21", "valid": true}]}