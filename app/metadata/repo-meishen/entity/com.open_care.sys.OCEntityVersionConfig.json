{"id": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "className": "com.open_care.sys.OCEntityVersionConfig", "name": "OCEntityVersionConfig", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0def43b7-8dc1-4ffd-8b83-4c037d597a73", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "436cbd63-9421-4a20-92de-7b2a232b93dc", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "4ee2ae2f-d32e-47ee-9007-93116234ade9", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "51e5a0f8-0301-49a1-80db-ee9678169a47", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "5c5ea0a3-c3c8-4f4a-99f5-1dc5ffc6d843", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "64bf7c5b-c98d-4fcc-856e-b5221e07b9f7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "68db889d-c557-47ad-8936-2bedc0819de0", "name": "className", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "88391fea-2919-4ea9-900b-ea64c3ec798d", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "98331032-0a94-4310-8c62-a79b69cb2fd0", "name": "shallowCopyFieldNames", "title": "浅拷贝字段属性", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "af8f3fd9-d091-4ff1-b691-75e39ecfb663", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "b1582286-ce50-4eb4-ab6c-0daa0450bbf6", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "b37cd37f-9006-4c38-a0ff-48717225c1e9", "name": "versionEnable", "title": "是否启用版本控制", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "c4538cee-c62e-4c75-a523-b9811379a823", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "c8fab88d-4b2f-425e-a047-114796799865", "name": "genLatestVersion", "title": "是否生成Latest版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "d920457c-cec6-4b5a-91c0-164aa3259812", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "eaa6d34d-497e-4d98-beca-01c0a05dd159", "name": "tenant", "title": "租户", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "f5d21a1a-0423-40ab-b706-67182df38651", "name": "genPublishVersion", "title": "是否生成发布版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}, {"id": "feacf979-a329-4621-a468-e264b86e829b", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8c8e4700-3008-4c37-bdce-5ba8f022c668", "valid": true}]}