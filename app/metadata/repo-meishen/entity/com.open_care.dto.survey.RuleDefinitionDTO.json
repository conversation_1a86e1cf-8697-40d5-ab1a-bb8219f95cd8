{"id": "cc04fe57-e4bf-4812-a222-3432e56ac1ca", "className": "com.open_care.dto.survey.RuleDefinitionDTO", "name": "RuleDefinitionDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "49ab2eb4-a019-423d-9f5f-e9c085397d95", "name": "ruleType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc04fe57-e4bf-4812-a222-3432e56ac1ca", "valid": true}, {"id": "624995c9-858f-449c-bcbf-3072c17113c5", "name": "weightedRelations", "type": "java", "classType": "WeightedRelationDTO", "refEntityId": "f21950c9-357c-425d-a46c-3e760ad4e492", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc04fe57-e4bf-4812-a222-3432e56ac1ca", "valid": true}, {"id": "97d51b9a-792b-4cb7-9468-9e139019902e", "name": "jsonLogicValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc04fe57-e4bf-4812-a222-3432e56ac1ca", "valid": true}, {"id": "b5532e93-0af1-4356-a74a-9e680061c82e", "name": "relationQuestionIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc04fe57-e4bf-4812-a222-3432e56ac1ca", "valid": true}, {"id": "d42e6982-501b-42f7-930a-4f0f26331aff", "name": "ruleExpression", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc04fe57-e4bf-4812-a222-3432e56ac1ca", "valid": true}]}