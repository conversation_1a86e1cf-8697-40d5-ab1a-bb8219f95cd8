{"id": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "className": "com.open_care.medicalMicro.base.OCBaseTrackLevel", "name": "OCBaseTrackLevel", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "追踪级别", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "0ac71545-9093-4bd3-bae2-a059ac2654bd", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "0ac71545-9093-4bd3-bae2-a059ac2654bd", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0c6b4cab-d612-49b1-8579-dfa3f8ad02eb", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "0e39a6a9-efe6-4653-9d3b-1b1a2112c25f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "0f574f82-e74c-45fc-9f73-f1b93dfecf76", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "1d558665-7eba-4bbb-b571-2dd75e73993c", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "28609c0a-a93e-4bab-bf2b-c5fee72c206a", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "39740514-11b7-45c4-a1c8-4ba6495fda0c", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "4033fd04-26d5-4b9f-a768-08022d0e63c7", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "44802936-3cce-4b3e-8dac-b0417651dcbe", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "595e12c5-9157-4785-a15c-d391266b1c82", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "6a8d1de5-4132-424c-a887-ab8c6004f75f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "6aedf32a-8277-4610-9fb4-d9db35c9e397", "name": "levelValue", "title": "级别数值", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "6c1eb5b0-8967-40f7-bcf8-f6a175f1c3e2", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "71657519-98d3-4a89-9a63-32229c37163f", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "77a22181-77ca-4aac-84f6-365b14125c3d", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "8802a2be-3775-4679-8ef1-8584dc5feb9a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "9e235312-4944-452b-b9ec-4a753f7df82a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "b6c1da63-0832-4b4d-b3eb-1be0107d2ee9", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["code"], "properties": {"code": {"$id": "b6c1da63-0832-4b4d-b3eb-1be0107d2ee9", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "代码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b740c9bc-2226-4712-a1af-1f464f937333", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "da38e586-c67d-4851-9ba1-fbc4f69576c8", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}, {"id": "e87444e0-de94-4d62-8c74-e6d403ff2db6", "name": "levelMeaning", "title": "含义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "valid": true}]}