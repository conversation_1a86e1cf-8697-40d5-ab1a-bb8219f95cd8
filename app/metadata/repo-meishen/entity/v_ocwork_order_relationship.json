{"id": "782deeb5-b473-4e18-a463-5fccaf83db3b", "className": "v_ocwork_order_relationship", "name": "v_ocwork_order_relationship", "standard": true, "server": "open-care-healthcare-crm-service", "type": "view", "valid": true, "fields": [{"id": "309d0c00-f78e-45ab-8c81-84b1a0a76080", "name": "bindSessionFlag", "type": "view", "classType": "boolean", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "36470dc4-148d-4725-8902-1a231b723a32", "name": "appointmentStatus", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "388071d0-c6a5-4857-8671-b80397224feb", "name": "priority", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "48c87384-53c9-49cb-8d6e-aecc9ad61471", "name": "personalCustomerId", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "4e09a3ae-bec5-4d95-8b31-ddcdaa7f8f34", "name": "personalCustomerName", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "5a24b13d-4ecc-4640-b5a5-85851504f99a", "name": "workOrderStatus", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "66d29d84-970d-4a04-bea1-ab5fc69c765f", "name": "userNo", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "67df053a-d4ad-4f03-9a0f-5c840e8b1306", "name": "ocId", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "6fedb0c1-f86f-4212-b063-ea1a97d075bf", "name": "personalCustomerMobile", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "73465159-1aa7-4e3c-8be4-53e53c9dc5cf", "name": "orderType", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "831dda08-57bc-4e42-babb-04c8c8cd0ed8", "name": "orderNo", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "861df2c6-1f13-434b-98bf-df2f9c20aa2a", "name": "created<PERSON>y", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "86f7225a-aff4-4dac-8812-1bf31fbbec88", "name": "updated", "type": "view", "classType": "date", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "963b36ed-8e48-4e4b-ab72-5a154292b06a", "name": "customerNo", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "a0b22881-6058-450b-81d1-fa52bf3baa55", "name": "created", "type": "view", "classType": "date", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "b7d22a65-8571-4bdb-b1d7-6063b913d0d9", "name": "updatedBy", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "dba8aac7-4931-495a-a6b2-d9185ae0c3a0", "name": "sessionNoList", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "e4d5b02c-e0cc-4827-95a9-19a42d5a211a", "name": "workOrderSource", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "f2e42195-d5b9-4090-840f-346f892f18b1", "name": "appointmentId", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "fa84d6ec-f494-46ff-b419-fbb45f9f07f6", "name": "createdByName", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}, {"id": "ff7185c9-3987-4377-bd3f-275fa007ba95", "name": "updatedByName", "type": "view", "classType": "string", "standard": true, "visible": true, "entityId": "782deeb5-b473-4e18-a463-5fccaf83db3b", "valid": true}]}