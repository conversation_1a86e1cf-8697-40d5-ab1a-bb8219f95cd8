{"id": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "className": "com.open_care.product.entity.OCProductServiceOrg", "name": "OCProductServiceOrg", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "115c6492-8881-493a-b6cb-a480e5e66a5a", "name": "serviceDuration", "title": "服务时长", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "1ceed495-fac5-4885-b54e-2099e6204ee7", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "20147c11-f57b-496d-95bb-a1b6a2922676", "name": "serviceOrg", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ac09f8b9-88ae-4e95-839c-53f726232d9b", "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "5c8f65f6-c10e-4698-8a0b-afc92502aa74", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "6011418a-2daf-4bb0-87c2-8380223936e3", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "760ba0c9-ef90-42f2-93c6-92c02f5f72ed", "name": "seqno", "title": "产品行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "84fadf39-3b0f-4dc5-8583-a774c7501707", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "899aa39e-4516-4748-872b-b2e5faaabfcb", "name": "processDefinitionKey", "title": "流程定义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "9b5db5ba-a9b3-4ab9-9f61-f4fabe189010", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "9c8ea0de-8499-4e51-9414-c5e65fda2f25", "name": "type", "title": "可选方式", "type": "java", "classType": "OCProductOrgConfigEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "7493096135b6f44e6135fc14e9a70da5315283ca", "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "9cae820e-3b83-4dae-948c-2918a1546c03", "name": "productReportUnit", "title": "所属报告单元", "type": "java", "classType": "object", "refEntityId": "1d0847f3-9bf4-43eb-a8b9-17f2ae85c6cd", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c112906c-e6ab-40ad-8a67-1084093522c4", "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "a0c87388-ed3a-4662-b9e4-cfe3a621edf8", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "adc55e87-06b2-438c-a019-a7cf3e5c2d29", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "af80478a-a076-47ef-9692-9a9a56064cb1", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "b75a30bc-bf3d-4d2c-9592-c09865a9d705", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "c306766d-0d18-4301-b7de-810ae026e0f6", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "db7a4d13-a362-40d1-a4a5-e667af5e06a0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "ea01aeac-f0d1-459f-be85-a15cd586e8ff", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "ef92de9c-32a3-42dd-b7a8-71a9cc12a0b4", "name": "processTemplateKey", "title": "流程模板", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1e55cb9d-560c-41c1-81a5-4fc879478bbb", "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}, {"id": "f21598cc-5df1-41f3-a968-f5c9a680482c", "name": "product", "type": "java", "classType": "object", "refEntityId": "83b77f63-f957-46ca-a1b8-e5d556f59fcb", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d191c3f7-a2d4-4924-ab1d-9ee1a9a7f1e2", "valid": true}]}