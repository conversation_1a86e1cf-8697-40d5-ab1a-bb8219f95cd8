{"id": "5b866f09-4e6c-498b-a8d8-317673f77306", "className": "com.open_care.dto.work_order.WorkOrderOperatorFlagDTO", "name": "WorkOrderOperatorFlagDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0d480b6c-64c3-417c-a466-3d5c6a5f80ee", "name": "canUrge", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "1ad1977d-cdf6-4db4-927a-ade67b88b877", "name": "canEdit", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "221a0231-be11-44b7-b870-513141fbdad3", "name": "canHandle", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "3545ebad-857d-4a84-a291-91297ed20883", "name": "canClaim", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "373b356d-8465-414d-b1d4-7e1afee635b5", "name": "canTransferToUnifiedServicePlatform", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "5284cfe1-07ce-4fa1-a279-f95d57f58bfd", "name": "canView", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "66588ae8-04f5-4d50-9499-27b272db2c18", "name": "canClose", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "9710bac7-a7db-4edc-8b58-6a26e8b7ad43", "name": "canSave", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}, {"id": "ea3f4b82-191d-45b2-8b2c-896c29a4a390", "name": "canAssign", "type": "java", "classType": "boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "5b866f09-4e6c-498b-a8d8-317673f77306", "valid": true}]}