{"id": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "className": "com.open_care.service.finance.ExportDataDTO", "name": "ExportDataDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "013369de-66e6-4ebe-aff1-0f9a7cc54d6a", "name": "paymentAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "10a94d40-c295-4bdf-b73b-45d79b517dca", "name": "userName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "19d5288f-57c9-41b3-8571-06e3b45b5332", "name": "filters", "type": "java", "classType": "FieldData", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "2c87cef8-7ac8-40fa-a94c-a6706c01bd25", "name": "endDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "30070d66-d99d-4e40-ab16-df80eabc4615", "name": "signFlag", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "334399f0-91cc-4233-9bbb-ad1cb6a11322", "name": "printer", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "46ec316e-edbd-4c89-a762-7546d2a63466", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "53517e1c-9d71-4bfd-bac0-f52fb97cbe4d", "name": "fromDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "5624cddd-ec78-4e56-b7c5-a00cc7a54f60", "name": "reportKeys", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "5b747417-9ab5-4a6f-ae2e-fa98ff234073", "name": "fileName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "76457db1-f116-4955-a85c-6b9f89716021", "name": "summary", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "90c0a9d8-28c2-43c4-95db-ced817089b48", "name": "totalAmount", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "a7b9bfb4-c00b-4b78-ae32-e3f0afafd49c", "name": "reportCheckOutId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "ace28980-38b2-4975-8e65-4a1f28113fc4", "name": "cashier", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "befa25e9-3da2-4229-bedb-813c4138bbed", "name": "footer", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "c69d1320-788d-491a-9563-4c02d9d7bcac", "name": "reportDate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "c6fa0d4d-1acb-4067-9357-9e67dcbc5feb", "name": "userId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "dfdfc6be-9882-405a-8799-455eac152c75", "name": "title", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "e11d7e96-20ff-4c83-8a7d-01726a7afe42", "name": "monthly", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}, {"id": "f12eb695-4961-48e2-841f-597062f1f3a4", "name": "customerName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "12dfda78-79a2-48a2-ab5c-c0f0607570cc", "valid": true}]}