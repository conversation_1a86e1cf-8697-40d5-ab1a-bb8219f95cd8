{"id": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "className": "com.open_care.sys.OCBaseNote", "name": "OCBaseNote", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0409a58b-a645-4a7a-a7ea-2dc419ddcddb", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "valid": true}, {"id": "0445c6b9-9dda-401f-aa6e-ea679e69093c", "name": "operateTime", "title": "操作时间", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "valid": true}, {"id": "0bedfcc8-20ce-43f2-bead-77fc16cce9ed", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "style": "Upload", "entityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"attachments": {"$id": "0bedfcc8-20ce-43f2-bead-77fc16cce9ed", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "附件", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "60c973a9-d045-4b0c-9427-2840de63f56a", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "valid": true}, {"id": "63ace2ba-a360-485c-a25f-5e0207980ee3", "name": "note", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"note": {"$id": "63ace2ba-a360-485c-a25f-5e0207980ee3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "备注", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "cd143c2e-c4ea-4a25-a06b-c9dbbccd8955", "name": "operateType", "title": "操作类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "valid": true}, {"id": "e51f174f-96e0-4348-b685-6a98898211de", "name": "operator", "title": "操作人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "84eee9c7-2c11-4751-94e3-8fa86258664c", "style": "Input", "entityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"operator": {"$id": "e51f174f-96e0-4348-b685-6a98898211de", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "操作人", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}