{"id": "28b9155e-1f1c-4703-9cf0-acdfa17a46da", "className": "com.open_care.process.ProcessLaneFlowGraphConfig", "name": "ProcessLaneFlowGraphConfig", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "12ed3ee1-db8d-430d-8b5a-d3d7aa556bad", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "28b9155e-1f1c-4703-9cf0-acdfa17a46da", "valid": true}, {"id": "1f9a7eb6-9882-4af4-b426-17ee0c77338e", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "28b9155e-1f1c-4703-9cf0-acdfa17a46da", "valid": true}, {"id": "471246a2-b9dd-4b6f-888d-1f7797c85563", "name": "taskDefinitionKeys", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "28b9155e-1f1c-4703-9cf0-acdfa17a46da", "valid": true}, {"id": "964baab4-6ea4-4495-b583-44492bc0ce8a", "name": "lane<PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "28b9155e-1f1c-4703-9cf0-acdfa17a46da", "valid": true}, {"id": "d4e1f85e-7657-4aea-bca5-2f712822dd54", "name": "laneNo", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "28b9155e-1f1c-4703-9cf0-acdfa17a46da", "valid": true}]}