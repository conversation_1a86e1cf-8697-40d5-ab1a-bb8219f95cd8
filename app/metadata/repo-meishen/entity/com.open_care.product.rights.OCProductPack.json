{"id": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "className": "com.open_care.product.rights.OCProductPack", "name": "OCProductPack", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "018a1aa4-efd8-476b-aec0-942bd5511da0", "name": "unitPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "099296fd-82e5-491f-8500-d18e2dafd591", "name": "salesModel", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "0e3b9760-a531-408d-a4c1-a46cd041b6de", "name": "costAllocation", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "103e9b5a-377b-4d27-9e54-9a1fbb1c5da2", "name": "type", "title": "类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "167cf13d-f4d5-49a0-8985-b6f793c9e7bb", "name": "cutWay", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "18d4a95c-4c1c-47b4-b49a-185dfac0ff96", "name": "entityInstanceVersionId", "title": "版本号", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "1e55e7b1-5dbe-4212-b1f4-f63d4dc16330", "name": "distributionMethod", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "2685f1b3-cd0f-4037-b70b-62b8095d0a12", "name": "activateExpDateType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "31200383-ea36-46f8-9ae9-a1c1e0d93619", "name": "shallowCopyFieldNames", "title": "浅拷贝字段属性", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "31c29da7-b31b-4aca-925e-cf01955046cb", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "368a0e77-9ca9-4295-aa2f-a69ccb83f73c", "name": "sortNo", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "38b85a8f-27b9-40da-bdd6-6cf8b22f431d", "name": "ownOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "3bccbea0-47d6-4e15-ba1f-e2a9682fd0ec", "name": "province", "title": "省", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "37f15354-94d1-42de-a5d5-4ba1e5022222", "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "3da3aee2-bba5-41ad-b59e-5a36da925fe9", "name": "category", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "3da57c35-22d9-4fac-a470-d7dec709233f", "name": "status", "title": "上下架状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "jsonSchemaData": {"rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"status": {"$id": "3da57c35-22d9-4fac-a470-d7dec709233f", "type": "standard", "title": "上下架状态", "entityName": "com.open_care.jsonschema.JsonSchemaObject"}}}}, "valid": true}, {"id": "3e25def3-4c7e-4ce2-b45f-6e4659e0ca4b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "434c3b98-0a14-4354-99c6-81d84d640b49", "name": "guidancePrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "451e87e7-daeb-418e-8eb7-7535658e856d", "name": "ifNeedActivate", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "47d7895c-8bd1-46b8-b4be-e617382e2167", "name": "versionStartTimestamp", "title": "版本有效开始时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "536749ab-e72b-491f-a699-07e5abc5675a", "name": "salesDateStart", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "59de1174-5f3f-4322-933b-3a7e5be85c9b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "623bec4a-4c34-4c01-8d04-921b252301fe", "name": "policyCoverAreas", "type": "java", "classType": "object", "refEntityId": "66c9186e-3dc5-4022-a577-8e42532880f4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "6a1920bc-fc72-4e71-8a5b-660298cfe83a", "name": "requiredPoints", "title": "兑换所需积分", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "6aa12050-92ae-42e2-aed8-bf9b6b92c952", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "6ac4ae40-5cc1-424a-89e6-17def58616ad", "name": "entityInstanceUid", "title": "版本公用实体ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "6e3fb5ba-4271-460e-aab5-5aa904b186de", "name": "ifOwnUseOnly", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "71a5b076-21e2-4568-8967-2c82a18946c0", "name": "remark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "75e0377c-793b-4787-8f0e-5e5ef4fc9f0c", "name": "versionEndTimestamp", "title": "版本有效结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "7918b6b4-965f-408b-b48e-bec89882021a", "name": "receiveExpDateType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "844c29b6-c828-452d-956a-f8ab583c0c7e", "name": "policyStartDate", "title": "政策起期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "84c4e8aa-d82d-4980-bc18-9b2fa363b469", "name": "currentFlag", "title": "版本状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "862e35a2-4571-4d55-9bec-c22213485520", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "8f7f432c-3cb6-4cee-afbd-54d92f9cea84", "name": "genPublishVersion", "title": "是否生成发布版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "9208b6e7-553b-4064-bf16-d52dca5c8874", "name": "guaranteeObject", "title": "保障对象", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "fe324e0a-1645-4886-92f7-d253d1a7a1d912", "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "a075f674-1444-4525-881a-1f6107d7054e", "name": "productName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "a1a195c2-9d62-4eb3-9dca-7ea01b2481ec", "name": "applyToChannelSources", "title": "适用渠道来源", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "a2ce973d-6ab6-42e8-a38c-f7184a864b72", "name": "enable", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "a5c720cb-6b78-4386-87f0-69b9a2f2dc87", "name": "supplierId", "title": "供应商ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "a6906f5e-c295-4100-ac56-0febc7c256b0", "name": "salesArea", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "ada32a1a-0f96-4d40-a957-86659e59dc67", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "ae0ff41c-faf4-4b09-9f5c-e76efaade317", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "b2fc55a5-9adb-49a9-b2cf-01f54588743a", "name": "activateExpDateDuration", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "b3d006c1-f54f-4da5-af84-2cd2cad85941", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "b9641cd0-f995-406b-81f9-1898b0f5694e", "name": "serviceItemIfChange", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "bc5f1461-291d-4db5-9caf-7aa42cfbcab5", "name": "evaluationTools", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "c5ad51bf-9b20-4edf-89f1-7a469f813060", "name": "costUnitPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "c8807441-4ead-4662-ab0b-4a7bff2594e0", "name": "receiveExpDateDuration", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "c96bb6fa-e9d7-4e4f-8c75-aadccde61019", "name": "guaranteePersonNumber", "title": "保障人数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "c9cf823b-346c-4ff3-931e-ce8820148bc1", "name": "productSalesStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "cd025524-8bef-429c-9396-9819044466e6", "name": "productInPacks", "type": "java", "classType": "object", "refEntityId": "b900fc7d-2937-49a7-9ea0-3905e2af7355", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "d16e5dcb-0456-499c-a4e8-d01277bc4587", "name": "supplierProductNo", "title": "供应商配置的产品编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "d3dce5d5-18db-4119-a25c-3a5892bfb7b1", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "d53e9588-701e-4950-abb9-2d1e1a860ef7", "name": "award", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "d6e3c9a7-d790-4c78-ad6e-a1b2be668cda", "name": "ifCostAmortization", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "d8b17d9b-2c9e-4ba6-9ba7-6ad038946c33", "name": "versionEnable", "title": "是否启用版本控制", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "d9ae9bcf-5239-4a91-a9ab-2d91bdb11ebf", "name": "amortiUnitPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "dc1696b8-e583-4953-a2e3-ca0443811ffd", "name": "genLatestVersion", "title": "是否生成Latest版本", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "def5b056-b1a7-422d-9275-608247ab438d", "name": "nursingCoveredPeople", "title": "护理覆盖人群", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "fe324e0a-1645-4886-92f7-d253d1a7a1d91", "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "def611db-e0b2-4aaf-b5c1-b2a3769d64ab", "name": "ifSupportTransfer", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "dfb8ec3a-8203-48f5-a584-dfa5bb812358", "name": "imgUrl", "title": "图片地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "e25bf1cf-d17a-48c0-ae0b-52176cdd64cf", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "e51c9690-46c7-4356-b55e-c53025e3ec96", "name": "genClientRightItem", "title": "是否生成权益责任", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "e6095165-6235-464f-8f22-294f44e9080b", "name": "recordNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "e7846cdf-45fd-4a94-bb56-c20c85e5ac23", "name": "supplierH5Url", "title": "供应商配置的产品H5地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "e7b3c4ca-9378-4c98-8698-f81648a320b8", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "eff24d3d-90f7-4b03-b941-ffbde01dbda7", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "f094182c-2355-4ed5-ba31-7f767a26f517", "name": "salesDateEnd", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "f106a9a6-456f-4238-849f-acdac71cb644", "name": "productType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}, {"id": "fbb23aba-e872-4eb7-b90c-65907a534155", "name": "enablePointsExchange", "title": "是否可以使用积分兑换", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "valid": true}]}