{"id": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "className": "com.open_care.product.entity.OCProductSettle", "name": "OCProductSettle", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0342efb7-eeaa-441a-90d0-0c19605250c0", "name": "lumpPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "09b1d793-f777-491a-a002-6769a234a844", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "0e927d61-296d-43ba-a20d-012c5ef54d9c", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "13f0fb83-4c33-4ce4-9099-58357868268f", "name": "procurementForm", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "14132170-7e7c-4b97-a24a-a55d816f8e6f", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "159a284e-47aa-4dc1-ba0c-626738aa5c87", "name": "quotationUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "1728ae6f-612a-4d31-973f-777de7c01ab3", "name": "discountRemark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "1cafece5-911f-403e-a7c3-b9ab4780dd78", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "2d5e743b-3885-4e9d-9b94-66f1ab537dc7", "name": "minPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "33603f3b-825d-416b-a833-8aa799a40bdc", "name": "productPacks", "type": "java", "classType": "object", "refEntityId": "67f53eb0-b82c-4c84-acd6-08a5237c2061", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "360e5760-0be4-4e7f-b6e6-b87c83cff250", "name": "lumpTaxRate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "38a1d96a-5e5b-42c2-a161-515a6d2e8129", "name": "products", "type": "java", "classType": "object", "refEntityId": "83b77f63-f957-46ca-a1b8-e5d556f59fcb", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "3b2442f0-f1f3-48f8-9683-6aef578f93e1", "name": "settleRemark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "496b4d0b-c832-4111-8ae9-b823607b60b7", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "50990d12-9b2d-4a2f-9bfb-422e14ce6143", "name": "settleMethod", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "63a9ddef-083d-47d6-ba80-52c7c0ca7037", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "6526b735-ef8b-4017-9250-1051ded18963", "name": "dimensionQuotations", "type": "java", "classType": "object", "refEntityId": "da2de461-e04b-4a80-8ba1-48975aa492dd", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "6942866c-b3d2-475c-bffc-de00579a59dc", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "829bdab7-7286-42bd-b162-ec00c4ca93db", "name": "contract", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "84035ad0-1759-4d70-8716-fb277d636338", "name": "ownOrg", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "8c3f202b-2aae-4edc-a637-2fd3a0298e22", "name": "supplier", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "bcd7aa8b-a34c-4ce5-a1d0-b848f525fd95", "name": "price", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "bd857ae8-e980-47fd-80bf-9fa26f7265d8", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "bfa12811-7fff-4da3-8ea9-3240b4949dac", "name": "taxRate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "c084d9f9-eaaa-4c35-86e5-9105b3586a15", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "c3e9b9fe-7249-41ac-9ded-0af720d3f0cb", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "c9168f4e-760a-429e-948f-02ccc3ec4fc8", "name": "priceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "d94d21bd-073f-4290-9ad8-30500f9c95cd", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "da9fd594-3fe7-4c73-8bb6-c1fedad912ca", "name": "settlePeriod", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "dc52747b-6be4-493a-a108-a6fd23491283", "name": "topMedical", "title": "高端医疗信息", "type": "java", "classType": "object", "refEntityId": "8a31f718-7704-45eb-9ac8-a93dae8d2535", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "e55bc7c9-a535-44f7-914e-501b12c4343f", "name": "auditStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "eb2c06c2-70f2-47b1-b760-2126966fcbcf", "name": "frequency", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "f39b1ad0-813b-4e6b-8709-79770d7ed834", "name": "maxPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "f622a204-a80a-417a-9c64-3207a2a481db", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}, {"id": "fc7f6e62-ecbe-4fde-8280-1f596f9baf40", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4ba54681-51b5-40e1-b4c1-0097f97ec903", "valid": true}]}