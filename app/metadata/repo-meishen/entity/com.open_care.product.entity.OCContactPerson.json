{"id": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "className": "com.open_care.product.entity.OCContactPerson", "name": "OCContact<PERSON>erson", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "11967b23-7e0a-4501-a212-d83beb95803e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "1379e7c6-12f8-41f2-8a85-57929cbc3f18", "name": "name", "title": "姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "1a1fdbad-66fb-464c-bdde-37fd3e32d727", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "29f330ad-b1d0-42ce-bcde-c4b5fc7569a9", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "2b779392-4e4e-4b08-8fc8-b446e47786d0", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "2d28c71a-543a-4d7b-96fe-15948041964e", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "2e086bba-ff11-45de-9735-65a05f4450ea", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "3dccfd8e-7f67-4b5c-9cd5-3bf1d9e1051a", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "3e1ed25c-e538-48dc-ba01-68a035ca537f", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "74665486-bed0-43d2-a6be-fc783b4c1787", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "80024ab6-caee-4ad5-9434-0aa26f902e50", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "8f734d38-400f-4fec-a76d-d8a24ac83f4a", "name": "phone", "title": "电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "jsonSchemaData": {"items": [], "rules": [{"type": "pattern", "value": "(^(([1-9]\\d{6,7})|((0((10)|(2[0-9])|([3-9]{1}\\d{2}))([-_]?))?([1-9]\\d{6,7}))|(1[3-9]\\d{9}))$)", "message": "请输入正确的电话号"}, {"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["phone"], "properties": {"phone": {"$id": "8f734d38-400f-4fec-a76d-d8a24ac83f4a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "电话", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d4a684cf-3c3d-4758-ab3f-34603a72879b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "eb8a31d7-5892-4e70-97fa-68e8e3d5757e", "name": "email", "title": "邮箱", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}, {"id": "feac79f7-f7d6-46c5-a352-f76fc9fae635", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "99fb84f3-21bb-47cf-a31e-40db8c25decd", "valid": true}]}