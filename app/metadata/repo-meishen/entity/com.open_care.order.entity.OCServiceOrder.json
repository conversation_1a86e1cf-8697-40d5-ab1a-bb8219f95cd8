{"id": "667e6842-e26d-465b-b94a-362e0d7f1c25", "className": "com.open_care.order.entity.OCServiceOrder", "name": "OCServiceOrder", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0241b067-d8b1-4cd4-960e-facdf99afef8", "name": "appointmentId", "title": "预约单Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "063e92c3-6158-44a3-9079-0414a4da967b", "name": "completedPreDinnerProducts", "title": "餐前项目是否已完成", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "06ac108e-1fba-423c-af6f-984a17848570", "name": "customerNo", "title": "客户编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "07ce18c6-b987-46ee-8d6a-30c03adb29e7", "name": "dinner", "title": "是否就餐", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "081710e4-1584-4f2e-a89c-565801c3a5c8", "name": "eaten", "title": "是否就餐", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "0a7c51ce-4c7c-4a13-9ab4-a1c5c3ef03e9", "name": "enterpriseCustomerNo", "title": "企业客户号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "0e721439-437a-48cc-9e1c-52c27a6e31c3", "name": "serviceSupplierOcId", "title": "服务供应商id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "127bdb91-b022-4ea9-ae5b-cbb576406064", "name": "cancelUserName", "title": "取消人名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "1502a4ea-c647-4ae8-964e-b71be08fe341", "name": "extraInfo", "title": "附加信息", "type": "java", "classType": "object", "refEntityId": "83e69917-64d2-402d-b6b4-f11b53ba9e7c", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "1b1120d5-8d37-4da6-8f89-23c84718d433", "name": "attachments", "title": "附件", "type": "java", "classType": "object", "refEntityId": "fe23ab19-37bf-41e0-a365-f53a0730b578", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "1f4fd23a-d900-481e-96dc-52590c0cf442", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "21f16b1a-7845-4095-b6ee-961995f6262f", "name": "serviceOrderNo", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "3331e800-b546-4914-b49d-f38b42445490", "name": "postAddress", "title": "邮寄地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "37bb67a4-90a6-4f58-8dd9-a0f1876c3269", "name": "cancelReason", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "3aaecab4-1019-43bc-bf03-5499faf45afd", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "3f05100e-9d98-4e9b-9f5d-bd3107312ce5", "name": "queue", "title": "是否排队", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "3f2f663f-50cb-4d35-9d0d-187ffe9a5a92", "name": "customerEatingHabits", "title": "饮食习惯", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "43cced82-1f29-4397-b35f-631a0db6b815", "name": "customerLevel", "title": "客户级别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "45883877-ddad-4b3c-a445-c49c2ab5aa8e", "name": "processInstId", "title": "流程实例ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "45b87457-0c23-4a32-aa3f-db09e30f8929", "name": "name", "title": "姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "45b87457-0c23-4a32-aa3f-db09e30f8929", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "姓名", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "4a134f4b-ebd8-48a3-8d7e-a0ff4afee606", "name": "agreementId", "title": "协议ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "4cc5a610-4ea7-4f7e-bd59-b0<PERSON><PERSON><PERSON>455", "name": "maritalStatus", "title": "婚姻", "type": "java", "classType": "MaritalStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "60045592a8d37359558b3ec8f5d52f86eab66f94", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "4cd71d75-ec13-4121-97b7-5309da17096e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "4fefd639-6a17-4974-8372-2ae634b3cd81", "name": "completeQueueTime", "title": "完成队列时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "50086ea5-ce13-414b-9835-1880bad5850e", "name": "hasHealthReport", "title": "需要健康报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "59dcd553-c408-478d-be1a-dbc5be4fd6b2", "name": "needPaperReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "5d3225bd-07d5-42bc-b7c3-69c8bab7fb44", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "5efa072a-b7e1-4b01-91f2-a36c1e9f1a63", "name": "examServiceType", "title": "健康服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "620b1e4c-a1a7-4307-8509-f8a9e55221b0", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "6407aeef-61cf-414d-9f15-35c567e8e24c", "name": "serviceLocation", "title": "服务地点", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "64569554-fbc8-4b6b-ab7e-e44e53fef3eb", "name": "orderId", "title": "订单ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "6fd7a59f-febe-4266-95da-f2195578245d", "name": "registrantName", "title": "登记人Name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "747d4cee-6ef0-476f-afa5-a575d7294e34", "name": "processDefKey", "title": "流程实例定义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "7775679e-55b6-4968-a678-06713b62585d", "name": "ownUser", "title": "负责人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "7a529d18-d751-4f99-a0fc-3a561c244cdd", "name": "certNo", "title": "证件号码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["certNo"], "properties": {"certNo": {"$id": "7a529d18-d751-4f99-a0fc-3a561c244cdd", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "证件号码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7c1655f6-5120-4bc7-a5da-6f96895ff22c", "name": "serviceType", "title": "服务类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "802e8d1d-0894-4079-a6b1-bcbfc1f10beb", "name": "ethnicity", "title": "民族", "type": "java", "classType": "EthnicityEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "716302fe2f49652d7abc152cd9ffda11d36be3ad", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "8775c7c8-7828-4d57-b971-561266776663", "name": "cancelUser", "title": "取消人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "87e59894-f2fb-43f2-b7c6-5e89b06033b6", "name": "reportDeliveryMethod", "title": "报告递送方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2d1a4417-cdd2-447d-b199-a7f6c1030211", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "89c70aff-96f6-4a05-9d8f-a1c589a20828", "name": "reportDate", "title": "报告打印日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "8b4e6886-a30a-457c-b933-f5e02609e1e7", "name": "serviceOrderItems", "type": "java", "classType": "object", "refEntityId": "9de4f85e-2aac-4925-9c9c-43aa6605c62a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "920aad0b-7c4e-44fa-ae50-1f2824190da4", "name": "partyRole", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "9279bbce-a54f-4dae-a084-58c7fa64bd5a", "name": "cancelDate", "title": "取消日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "9294a5d6-cfb8-45e4-9c4b-751e4fd5b6d7", "name": "allowPostPaid", "title": "允许后付费", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "92c40793-db6e-46ba-8108-1e44ccb3601d", "name": "serviceOrderItemGroups", "type": "java", "classType": "object", "refEntityId": "dec0e822-239b-4bda-9ffe-5d6e64385239", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "94ed7d20-b55d-47fc-860c-3a7ae434d300", "name": "processInfo", "title": "流程信息", "type": "java", "classType": "object", "refEntityId": "1ae0ece1-7c0c-478b-b129-5d5c3ae78272", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a08ec780-0768-4f91-9078-89589c35ff2c", "name": "notes", "title": "备注", "type": "java", "classType": "object", "refEntityId": "d10fc664-98b9-4f40-ae06-8aa91f1fc37c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a12ccdc8-d797-4ddb-ba65-758461975d42", "name": "serviceOrderState", "title": "转科附加信息", "type": "java", "classType": "OCServiceOrderStateEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "e68dbf206d69523a6bb51c7fbb4df77d2a31c5a9", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a3c97035-eb3b-4c55-a02b-1125f0c03f64", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a422412f-ff3d-4542-b8de-f417a2253355", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a4cfab19-dca4-48d1-a69b-cb4b1973fd15", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a875a796-df17-4b28-b697-94b90d331297", "name": "needUploadPdfReport", "title": "需上传电子报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a8d09065-4f57-4074-a053-2d30149fd97c", "name": "needConsultation", "title": "需要问诊", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "a9ba9a42-93f2-4383-906a-1ddddd70c50f", "name": "printGuide", "title": "是否已打印导检单", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "aa60afd5-99dc-4f99-9d70-ae7f36b4bcd6", "name": "serviceTime", "title": "服务时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "acfed928-258f-49fe-94ef-70bcf85665f3", "name": "serviceResources", "type": "java", "classType": "object", "refEntityId": "d69ffaa9-cb16-4867-91d1-4d7aa432b68e", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "afbaed3c-7860-4f9e-b625-931725ce42cb", "name": "religion", "title": "宗教", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "b0e2b4ea-2976-4936-b4ee-9f371c2c424f", "name": "birthday", "title": "出生日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["birthday"], "properties": {"birthday": {"$id": "b0e2b4ea-2976-4936-b4ee-9f371c2c424f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "出生日期", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b49fffed-3c60-4338-9b1e-de5a3006d5fe", "name": "serviceOrg", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "bbca6d7b-0452-47e3-bf64-dbe0b70bc657", "name": "serviceEndTime", "title": "服务结束时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "bdfb6723-12d7-4a05-ac3a-411a761b896d", "name": "registrantId", "title": "登记人ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "bf618f11-e224-4d3a-947f-9e4cea2a6d66", "name": "certType", "title": "证件类型", "type": "java", "classType": "CertTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3c0c713f4a40c01d2c9a28a771ee45aafb43e943", "style": "Select", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["certType"], "properties": {"certType": {"$id": "bf618f11-e224-4d3a-947f-9e4cea2a6d66", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "证件类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c059e80a-ddec-4a10-a310-829336e50369", "name": "sex", "title": "性别", "type": "java", "classType": "SexEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a88b8f7aea57993f1a3fb6749801c5b17ed2a108", "style": "Input", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["sex"], "properties": {"sex": {"$id": "c059e80a-ddec-4a10-a310-829336e50369", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "性别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c1105e8f-5c6e-4402-b26b-8c5ce0c46bf1", "name": "automaticallySplitHepatitisReport", "title": "需纸质报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "c17e9b33-a1cf-43b4-b64e-b3618b3b7598", "name": "enterpriseCustomerName", "title": "企业客户名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "cc9fbf74-947c-4efb-99c0-b8ae8e9af71d", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "cd5b31e5-6410-43db-9d22-551697a495af", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "ce61ace1-3d38-4c10-893e-afea4e2f31d9", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "d3509094-9198-4611-ba1c-76b3a7a62ca2", "name": "enterpriseCustomerDepartment", "title": "部门", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "d4a69820-f4fd-42d7-bb77-6ce7f9c103e3", "name": "nonPersonalService", "title": "非本人体检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "d560169a-2531-4d26-adff-b7a4dddcb4a7", "name": "ownOrg", "title": "所属机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "dbabf28b-5e08-4667-bc58-7376d29148cz", "name": "serviceInformationRemarks", "title": "服务信息备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "dd697051-872a-4e5b-a41e-d5b68546b066", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "ddaa6905-49ef-4a21-9a15-b2fdfde238ab", "name": "ageUnit", "title": "年龄单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "dfd43956-1026-4216-a2b9-eddefd0a56b9", "name": "joinQueueTime", "title": "加入队列时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "e678438d-7504-440e-a399-8793b0fffad8", "name": "hospitalId", "title": "院区编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "eb2e9cdc-68e2-4f48-85ae-ef9ba3e36fef", "name": "enterpriseCustomerId", "title": "企业客户Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72666", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "f003c132-f285-4bbf-a839-f9b675394764", "name": "mobile", "title": "手机", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["mobile"], "properties": {"mobile": {"$id": "f003c132-f285-4bbf-a839-f9b675394764", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "手机", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "f530b843-e583-4b02-93fa-15f3f33c0cea", "name": "areaName", "title": "所在地区", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "f89ae533-2a68-4766-8af2-f16fabf7d1fd", "name": "areaId", "title": "所在地区Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "f96193a2-3488-4b46-b94e-2ba3eb6ddc2b", "name": "isVip", "title": "是否VIP", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "fd7e9666-64c5-4c65-8c0e-1761c83c0fd3", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}, {"id": "fd9a1b8c-1e75-4b3e-929b-3d85ef78c0f4", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "667e6842-e26d-465b-b94a-362e0d7f1c25", "valid": true}]}