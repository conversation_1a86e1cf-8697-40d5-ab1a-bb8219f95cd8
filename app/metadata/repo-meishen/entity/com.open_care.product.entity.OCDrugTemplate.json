{"id": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "className": "com.open_care.product.entity.OCDrugTemplate", "name": "OCDrugTemplate", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0874a21f-3d6b-4adc-958e-f30717d7a509", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "0f4a8682-adeb-43b7-ae0b-3346ab8eb601", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "18644796-b071-4047-b549-2f7c0ae84309", "name": "productCategory", "title": "产品类别", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "18ce1dc1-c888-43a5-83d9-6dc3764359df", "name": "marketPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "1aabc2c5-80fc-44c2-aa5e-76b24b7d34d4", "name": "taxRate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "1b6aaa29-06da-423e-85fc-d5ac5082fcc5", "name": "maxAge", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "1ce4f864-a258-4033-9f34-b3ec43fac574", "name": "sellPrice", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "2778a1c5-e2d6-4f98-b6bd-b9aa3aa8160a", "name": "tags", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "3972a561-fb33-401e-9b39-4ec4c3ae4c64", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "4222ce84-c566-4526-9d67-44b1986e19fb", "name": "marriage", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "4cefafd7-d465-4fd9-92a0-c104cfbab720", "name": "retailUnit", "title": "零售单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "5f23ee2d-bff8-4b72-844d-f363806feaf9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "5ff80930-3c5a-45d8-84b4-283379c388b9", "name": "minAge", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "63c74b12-5984-493a-b466-54d507f367ec", "name": "belongOrg", "title": "所属公司", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "69ba869c-7481-4437-943e-8ea175a5ea32", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "6b639160-97a7-4469-9c3b-fb0262b85745", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "7653c79d-6b95-4be1-a78b-d2a8671abba0", "name": "phonetic", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "8284f376-a6e2-4541-a55c-875a066239a2", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "8a36092d-f629-4b19-a770-96e04be4fe2a", "name": "sex", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "905fc80b-5eb4-45ba-beec-5243640a49ac", "name": "processTemplateKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "964101a1-49cb-4dbd-b9e1-03842c7d860a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "9e3417d8-5e88-414e-9b59-c03a347592f4", "name": "healthReportTemplate", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "a946bfad-508b-4171-a784-e2a228ed2469", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "bc15b300-c1be-44a4-8a9c-652147c136c0", "name": "status", "title": "产品状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "bcb93c59-6801-42b9-ac79-15aaeab70747", "name": "name", "title": "产品模板名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "d1988a93-af1b-4bef-85f6-9dc6be01bdd9", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "e7d63939-f961-4ef8-b4a3-2fd542821527", "name": "placeHolders", "type": "java", "classType": "object", "refEntityId": "1050e8f8-ea41-4465-972d-329ec46c5065", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "ea71a0ad-c388-44fc-a913-be046f292ead", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "ee69a882-eda3-4751-bdbf-e51e013deec1", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "f6665fdc-0ba2-4c1b-996a-0846abee57e3", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}, {"id": "fc68d595-809f-4df7-a921-7677c2489ce3", "name": "stockUnit", "title": "库存单位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "34c3bb4e-f3d9-4db3-a2fd-2a8d5ad7df72", "valid": true}]}