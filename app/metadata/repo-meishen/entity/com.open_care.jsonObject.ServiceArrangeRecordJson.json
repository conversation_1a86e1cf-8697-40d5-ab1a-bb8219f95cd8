{"id": "cf1606bb-681b-41bf-b498-4012067939a4", "className": "com.open_care.jsonObject.ServiceArrangeRecordJson", "name": "ServiceArrangeRecord<PERSON>son", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1976fbbb-3a58-4488-a932-e32493161525", "name": "expert<PERSON><PERSON>le", "title": "专家职称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cf1606bb-681b-41bf-b498-4012067939a4", "valid": true}, {"id": "4fb67d4f-567f-487b-949e-42aaa7b81b16", "name": "departmentName", "title": "科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cf1606bb-681b-41bf-b498-4012067939a4", "valid": true}, {"id": "7eb94615-d907-428c-9437-7683991b4a95", "name": "expertName", "title": "专家名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cf1606bb-681b-41bf-b498-4012067939a4", "valid": true}, {"id": "845d1a28-fc6a-4c68-b253-dc8e7cd96bdb", "name": "hospitalId", "title": "医院", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cf1606bb-681b-41bf-b498-4012067939a4", "valid": true}, {"id": "b66be1bb-f807-429b-8c08-b2bc57364006", "name": "arrangeDate", "title": "计划安排日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cf1606bb-681b-41bf-b498-4012067939a4", "valid": true}, {"id": "cad1371c-390a-48c2-a23b-ff8d0bd8410f", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cf1606bb-681b-41bf-b498-4012067939a4", "valid": true}]}