{"id": "d243b0d3-daae-44ab-983e-6b8e46816c81", "className": "com.open_care.medicalMicro.medical.OCBaseOperationMethod", "name": "OCBaseOperationMethod", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "手术方式", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "00d3cea6-514e-45b1-a6b8-593c09fa4662", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "030f17ec-0d27-4a6a-8ea8-556eb02af969", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "1a87e793-fe1e-4b57-8242-99abc6ff8be0", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "22185702-9f83-490f-8d08-e8648b243fcd", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "2da1099a-54c7-4e1c-8744-1c5a02b6e4c9", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "35fbba7e-e406-4bd5-b81e-6601ed39abd0", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "48b30015-4250-43f5-b9bc-4628b69a74ca", "name": "itemType", "title": "项目类型", "type": "java", "classType": "object", "refEntityId": "b7d944c9-182b-4017-be00-e4e594e3ff69", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "6521c6c5-045a-4dbe-a449-e2e7bbe4d4d8", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "65ec0755-e6a2-4736-a783-0e4e90066c8d", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "8d925454-2018-43a0-a210-7fcba0e0f388", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "980c7a59-d6e4-4a0d-a10c-a3a6335586a1", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "9bddca26-5696-4c72-b769-811edfd3da5a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "a417d626-de61-46b5-8736-e1251e609e1e", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "a46c5941-e208-4321-8ccc-4615a04415ea", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "add946eb-cc60-48c8-a67a-161a2e849dae", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "b3cdcb3f-11b1-4d4b-a9e3-24cc4a4db626", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "ba313bea-82ee-4d02-b2e3-dfdaa5f557aa", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "d5514bec-5505-4c53-9081-b01963d07563", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "dd0467aa-f2fb-4a0e-b481-349bb99fc295", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "edc53552-70b8-493b-b148-a6c51c30fa99", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}, {"id": "f9be1dff-766d-4901-83ea-f9bfa81da16e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d243b0d3-daae-44ab-983e-6b8e46816c81", "valid": true}]}