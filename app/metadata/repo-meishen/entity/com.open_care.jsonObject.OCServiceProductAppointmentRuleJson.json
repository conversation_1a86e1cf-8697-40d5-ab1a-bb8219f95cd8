{"id": "4fed488a-882e-4d99-ba85-102a5e835d83", "className": "com.open_care.jsonObject.OCServiceProductAppointmentRuleJson", "name": "OCServiceProductAppointmentRuleJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "18c6d0ae-5a03-44f0-af4c-ba0d751b52d0", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "需要审批", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "valid": true}, {"id": "3308c055-cf38-40ad-be99-2de3e3abda9e", "name": "operateDuration", "title": "操作时长", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "valid": true}, {"id": "674d8129-b1d1-4632-944c-74585535a513", "name": "operateDurationUnit", "title": "操作时长单位", "type": "java", "classType": "DateTimeUnitEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8a1136265010b28b2895fece827b84ef0cfe9d70", "entityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "valid": true}, {"id": "6915096b-4a29-45cc-8a5e-1f445970c3f1", "name": "canOperate", "title": "可操作", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "valid": true}, {"id": "8368acb8-f31c-4298-9e8e-19273c453f21", "name": "needReturn", "title": "需退换次数/钱", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "valid": true}, {"id": "efa4e79e-1d11-448f-b96b-75cde31a0936", "name": "typeEnum", "title": "类型", "type": "java", "classType": "ServiceProductAppointmentTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b918846e4b4cda632298596a660347e3dcae6aaa", "entityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "valid": true}, {"id": "f2ab6035-4a8a-428a-b1c7-eaa90f54d626", "name": "returnRatio", "title": "退还比例（两位小数百分比）", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4fed488a-882e-4d99-ba85-102a5e835d83", "valid": true}]}