{"id": "4729edb3-63f7-48d0-9e33-13095d1cd0e4", "className": "com.open_care.sys.OCTenantFieldConfig", "name": "OCTenantFieldConfig", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "27bf3908-5cec-40f1-b158-0e52e2d05dd2", "name": "businessFieldName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4729edb3-63f7-48d0-9e33-13095d1cd0e4", "valid": true}, {"id": "59cbb5fc-2576-4feb-894c-48b5db2af44c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4729edb3-63f7-48d0-9e33-13095d1cd0e4", "valid": true}, {"id": "c41ca7f2-a2b4-4680-93d2-82baa8ac683d", "name": "businessFieldTitle", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4729edb3-63f7-48d0-9e33-13095d1cd0e4", "valid": true}, {"id": "d3fa78f1-a663-4c61-8cd9-79b352b0fff2", "name": "externalFieldName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4729edb3-63f7-48d0-9e33-13095d1cd0e4", "valid": true}, {"id": "fc662308-e424-42a6-990c-dfbb64dae722", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4729edb3-63f7-48d0-9e33-13095d1cd0e4", "valid": true}]}