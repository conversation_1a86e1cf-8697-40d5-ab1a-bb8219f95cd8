{"id": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "className": "com.open_care.entity.chronic_disease.OCFollowUpPlan", "name": "OCFollowUpPlan", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0c017324-7ef4-465a-af0b-4260bec707a2", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "301eefa0-89c4-4dec-9fb3-ac1a2566c4f6", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "399f85be-d5aa-4184-8441-cb318d3b775e", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "3a835bcf-fdf2-4515-a5fa-9e5af2e366c6", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "739c5d9f-8180-435f-b5bb-738751ca300f", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "78a1de83-070f-4588-885f-f23d8f84b70b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "832732d2-0a7b-44f0-b49d-98e727227bbd", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "83b7c31e-2f65-4321-b6b9-2ac81b01da86", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "9d843851-0940-48fb-8873-742be6c04787", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "a6ceaa61-618b-4b01-afdb-5fad753fd039", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "b06926be-4eea-4ff9-b453-931217ed5061", "name": "managementPlan", "type": "java", "classType": "object", "refEntityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "b6bc48cb-3ba4-4ecc-a581-2ced1c05b4b3", "name": "schedule", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "ba8e7ee6-21e3-4294-a15b-173672957647", "name": "contentFocus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "bcf35121-f2ec-400f-8f96-bfbd369f4a3a", "name": "followUpRecords", "type": "java", "classType": "object", "refEntityId": "ecce42b4-0e68-41b2-a4f3-0308c564d3ed", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "bd23038c-5c78-44ec-8ee8-875aa4cfcfa4", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}, {"id": "eefee470-b69f-4a2f-a07f-ccec538f4e77", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b6d35687-a5ec-47da-968f-9adcde10d9d7", "valid": true}]}