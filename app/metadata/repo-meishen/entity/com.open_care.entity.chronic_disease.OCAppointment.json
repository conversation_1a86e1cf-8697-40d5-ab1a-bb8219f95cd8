{"id": "10be78ce-f511-4ae4-b752-800aca8d0502", "className": "com.open_care.entity.chronic_disease.OCAppointment", "name": "OCAppointment", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "126bb561-93ee-4ba3-a8f8-998089c42e4b", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "1580d9c2-c91e-4710-903d-25448aaaa5c9", "name": "appointmentLocation", "title": "预约地点", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "1adbdeb9-b3e1-479e-9d64-60c8b20b8498", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "30b0c032-fb0a-41db-92ec-8e5d3eb45c7c", "name": "managementPlans", "type": "java", "classType": "object", "refEntityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "3e956c87-7592-4088-9407-33950af1a8cd", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "551582e4-b450-4669-a2bf-508f6e4045ff", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "573ccbb7-8755-4a2d-81c6-372eae27255d", "name": "estimatedDuration", "title": "预计时长(分钟)", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "59da1b5b-aa6a-4cc0-b0c2-54f17bc6621b", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "5d22d203-ecaa-4692-9e3d-b73db607796b", "name": "quarterlyReviews", "type": "java", "classType": "object", "refEntityId": "5d97b6dd-abc5-4f00-b8a8-4425d88b325f", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "66858c13-bd68-45dc-8e5e-dbe40a2ff60b", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "68680bbb-21cd-4446-a419-9cb67e1ed80e", "name": "appointmentNotes", "title": "预约备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "6bc2932b-440e-42d8-82e5-516349f5ff41", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "70b01320-b20c-4758-9a51-297b7c0de992", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "70fc1bcd-8648-4614-a067-c101a2b7adc5", "name": "appointmentStatus", "title": "预约状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "76d74c0c-2d86-406d-8fc2-3236881e7917", "name": "activityLogs", "type": "java", "classType": "object", "refEntityId": "e03f2dac-f7fc-4f0f-8b06-d7b102b8636c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "890dd8bc-f267-48aa-a7ac-702d8eacd52d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "a7775379-7819-4ef2-a5f8-30a11589b5a5", "name": "appointmentType", "title": "预约类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "b96820d1-beb0-4321-b7be-acbfb5ded4be", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "ca14acca-01ae-4ed2-b4a5-97bf93acc07c", "name": "healthAssessment", "title": "预约状态", "type": "java", "classType": "object", "refEntityId": "7565dbfb-21e6-4edb-8ad0-34c8e532fcb9", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "dfa7167c-cbf7-4910-9946-b6e8051a54cc", "name": "appointmentTime", "title": "预约时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "e4c49c7f-0fb1-496c-b7a7-377d325ed292", "name": "managementGoals", "type": "java", "classType": "object", "refEntityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "e9d2b21d-1177-428d-97fd-b56f8007dc78", "name": "<PERSON><PERSON><PERSON>", "title": "医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "eb07ad29-b762-4d34-b273-22f3bfcae1e4", "name": "glucoseRecords", "type": "java", "classType": "object", "refEntityId": "3b8e898b-b27f-4baa-90b8-f8c23a74dd4c", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "edd12ddc-37be-45cf-8f6d-704746efb750", "name": "patient", "title": "患者", "type": "java", "classType": "object", "refEntityId": "39a14aa2-a273-4903-ba7c-061ea94e2e35", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "f4bcf87c-2af1-494d-8910-fed1fbb2fcc8", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "f75db883-1123-42fa-833a-47e19c47ba5e", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}, {"id": "fcb6944c-2861-4c3b-be20-3f6a3e137348", "name": "followUpRecords", "type": "java", "classType": "object", "refEntityId": "ecce42b4-0e68-41b2-a4f3-0308c564d3ed", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "valid": true}]}