{"id": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "className": "com.open_care.quartz.OCDateRangeQuartzTask", "name": "OCDateRangeQuartzTask", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "17f09522-f945-4270-93ec-11a43975af25", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "1d44c63b-98b5-432c-a920-f54a10efb02d", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "2a3257ba-837a-475d-982b-17fbbe9bfc40", "name": "quartzTaskLogs", "type": "java", "classType": "object", "refEntityId": "d068172a-ec56-4a60-98d0-4e97219dbe9e", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "2eb36db1-dc6f-41d7-b3b6-c2dca1b2592f", "name": "startDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "35e25298-c3ae-4231-a14d-b5aa984f56db", "name": "retryCount", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "416d5148-759b-4c3b-8a52-863659c61075", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "426c4e4c-9ca8-4c61-bcb9-c269ad1eaf96", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "43c9b769-c256-4fce-be17-cbb6bb0dc53f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "6610d16f-68ea-44ff-a79c-fd98eafc8e0a", "name": "endDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "66f7db3e-fbb5-40b5-a204-c170b4c93282", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "7f46202c-c775-44af-b725-d8a051cd8ec3", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "9485c63d-94c2-4aa7-8aec-784ed2992ad1", "name": "<PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "a14980da-27ae-4d8e-9df6-48889b266f6b", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "a41008bf-97b7-4a0d-86aa-d65decbd5efa", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "ad2e5b3f-abaf-4fee-a76a-18ea8e4ac355", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "b0eb0079-fb74-4fb0-9462-1a0e5212c628", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "c7044efa-ea1d-4e0b-85e1-ccac2551f7ce", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "df5646df-a4ac-43bd-b229-7cbe39db2887", "name": "taskName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}, {"id": "f0dc17ff-a05d-436e-8346-eca08237962d", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "409e74c1-2bc4-4e60-8274-a2c0fe82fc8a", "valid": true}]}