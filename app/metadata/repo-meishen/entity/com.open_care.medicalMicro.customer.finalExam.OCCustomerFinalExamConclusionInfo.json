{"id": "014d5c4f-32ff-441c-89d8-9d03c724e886", "className": "com.open_care.medicalMicro.customer.finalExam.OCCustomerFinalExamConclusionInfo", "name": "OCCustomerFinalExamConclusionInfo", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户总检结论建议", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "027c170f-3023-4e26-aa9a-3cc267288d9b", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "07a247d2-dae1-4b20-9890-5e962ac5428b", "name": "trackLevel", "title": "追踪等级", "type": "java", "classType": "object", "refEntityId": "f7513e76-6dc7-4335-8afc-ae1ceca76b06", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "2574c92f-d7c9-4cf1-bf9b-8767e952833a", "style": "Select", "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"trackLevel": {"$id": "07a247d2-dae1-4b20-9890-5e962ac5428b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "追踪等级", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "11884cf4-8b39-493d-a183-b3c028e6ec8d", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "155177a0-5bfe-4997-9c01-971231f88eb5", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "1db3e0c9-2113-4558-a83a-c5445ad46dd0", "name": "sourceDepartmentOcId", "title": "来源科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "1dbf96e3-742c-4880-9e99-0bc46f657cb3", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "1eff6988-4403-49f6-aff4-533575d33621", "name": "proposalContent", "title": "建议", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "2d82c93e-9aa4-4b45-8c70-f0cef9bfc408", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "2f4b696d-0b2a-4e09-be77-07f73a6178df", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "3abf4d30-2d0f-4036-b1ea-0b083ba4cbdd", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "42449ad4-123c-41d1-92e3-235ed5632079", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "45a8517a-9711-4fda-90b0-51240946c17e", "name": "mergedConclusionOcIds", "title": "被合并总检结论词ocIds", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "4e8b74f6-e93b-448f-8a62-cd0a39e9424d", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "50aba603-af19-46ab-a1fc-1ac5d947973c", "name": "disease<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "疾病解释", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "511adffd-de5c-45ed-b7f1-ec38ed89ff0a", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "51bc7008-7e1a-4ee7-b182-4d470d1a0802", "name": "customerFinalExamConclusionSource", "type": "java", "classType": "object", "refEntityId": "7da99c79-ebb3-4ff9-a0ad-e8503daf97b1", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "59a8bd66-ba21-4254-9acb-57e74b65ec1f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "5c5b4413-c1d5-442e-b139-879a2d3040be", "name": "healthKnowledgeContent", "title": "健康知识", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "5ca0fe51-615f-4172-b24a-932064981421", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "612b7e06-0aff-4c92-a9c6-eda13e3fc551", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "饮食指导", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "6b2791d4-c666-4b25-acc7-fd0691a71ac0", "name": "proposal", "title": "建议词关键字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0d92d857-e3b8-4180-9067-6d4a5999490e", "style": "Select", "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"proposal": {"$id": "6b2791d4-c666-4b25-acc7-fd0691a71ac0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "建议词关键字", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "6c15031a-1cbb-4a25-ae99-410e9b32ce85", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "7354bb3e-b425-4bc6-89a3-6b5853930d06", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "77058fc4-e32b-49dd-93a3-7f0fc68d34f4", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "7a71017b-3a92-483d-a756-505b9cab4cbe", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "8809c6fb-bb02-4505-9317-97e5a4f8cb28", "name": "exerciseGuidanceContent", "title": "运动指导", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "92902da2-6c37-4523-acaf-0f2986f69b39", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "92c4eb27-e052-470e-8cc6-dbc02c5c902a", "name": "baseConclusionOcId", "title": "结论", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "93637cbe-ef83-4291-9476-edf83babda9a", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "是否当面告知", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "96de6f10-3ed4-4d51-87d6-5862c1f718d6", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "a28422aa-5667-4a17-9de1-4609cd61581c", "name": "newAddConclusion", "title": "科室新增", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "a50af37e-d35f-41dc-9236-5d41aa94a7c3", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "a5429352-58a2-41ae-96d6-8ed40f666e5c", "name": "hepatitisTypeConclusion", "title": "是否乙肝报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "afffbd51-0b2c-407c-acc3-4af4578b1bd0", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "b071c191-b6b4-4566-b067-332c294d141c", "name": "examProductName", "title": "来源产品", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "bbbdc0b5-64d6-4392-bdeb-7076ee60e0ba", "name": "beMerged", "title": "科室新增", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "c20a52ae-e400-4055-8ea3-d09623d851ab", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "d11ad72c-2081-4c51-b039-8fe5fdf79954", "name": "conclusionSevereDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "style": "Select", "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"conclusionSevereDegreeLevel": {"$id": "d11ad72c-2081-4c51-b039-8fe5fdf79954", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "重症级别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d5a042b3-aebf-4900-8c48-854d7578dfe9", "name": "departmentReportDisplayOrder", "title": "科室报告顺序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "da932443-66ac-4316-b81c-7a19aa719564", "name": "baseDiagnoseICD10OcId", "title": "对应ICD10", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "db7b64af-3ed7-41aa-9ab0-a44d83a9afad", "name": "conclusionDisplayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "defdb18b-c89c-41b2-a865-50047bee491e", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "df8a0ae0-4996-44dd-8778-9e99e91ffc00", "name": "baseExamItemOcId", "title": "来源基础项目", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"baseExamItemOcId": {"$id": "df8a0ae0-4996-44dd-8778-9e99e91ffc00", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "来源基础项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e46fa086-6b21-4613-8ec2-34d478d8e302", "name": "multiSuggestions", "title": "多建议结论", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "e49a31e6-df98-4e8a-bc18-19a12ede0e37", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "eb7e3ab3-d32e-4227-83e0-2c18fcf24394", "name": "customerFinalExamInfo", "type": "java", "classType": "object", "refEntityId": "46a2c1ee-5e28-410c-ade1-d93c3be2b18e", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "f56eeaf1-1549-4101-bb7b-2d46b4e75c2a", "name": "healthGuidanceContent", "title": "健康指导", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "faaeded0-5cbe-496e-8274-4bd17e0d8b85", "name": "examProductOcId", "title": "来源产品OcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "fc6980f5-be77-44b2-af0c-7a4306266b09", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}, {"id": "ff9702e2-2ad9-4582-a3bd-248c0fec6386", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "014d5c4f-32ff-441c-89d8-9d03c724e886", "valid": true}]}