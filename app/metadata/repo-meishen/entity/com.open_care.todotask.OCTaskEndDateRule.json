{"id": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "className": "com.open_care.todotask.OCTaskEndDateRule", "name": "OCTaskEndDateRule", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "4948215a-8087-479d-9f8b-b1e568b0c854", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "valid": true}, {"id": "6e5f2dba-9276-4ea7-8f03-fd4e9e1f6c9c", "name": "dateBeforeAfter", "title": "日期前或后", "type": "java", "classType": "OCDateBeforeAfter", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c380a4d4e1857855967ef5eb42e813d73ee83ef4", "entityId": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "valid": true}, {"id": "7eeba27e-fcec-4860-83e3-9272b4a6ea57", "name": "jsonSchemaData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "valid": true}, {"id": "a3f42f47-7de4-446d-a4d9-74056b3ea8da", "name": "endTime", "title": "时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "valid": true}, {"id": "a4480f6a-ebfd-42fc-9fff-75e5ddf838d1", "name": "days", "title": "天", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "valid": true}, {"id": "c0dab1e5-fe6e-48f3-af0d-247990162295", "name": "type", "title": "类别", "type": "java", "classType": "OCTaskEndDateRuleEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "09f047453ac87968a3abf7760c9365cfc8a17fac", "entityId": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "valid": true}, {"id": "c17a9c96-44d6-491e-8b8c-6b0bae9f1109", "name": "timerNoticeRules", "type": "java", "classType": "object", "refEntityId": "75522841-e879-444d-b4d4-cc585b0531cd", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "aeec2cce-56b5-4be3-9e83-9f719fbfc53a", "valid": true}]}