{"id": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "className": "com.open_care.medicalMicro.medical.OCBaseExamPartDirection", "name": "OCBaseExamPartDirection", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "检查部位方位", "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "05e29485-cc68-41e3-a49e-a56c48b63de9", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "24fa7cb7-c075-49f0-8d8d-f82a868412b7", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "43f89f88-2f8f-4595-aa65-3fe0dc0b6ab4", "name": "inputCode", "title": "输入码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "589b430b-f727-4a9c-86d8-327e6465d383", "name": "code", "title": "代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "6afb4484-ad07-4699-8460-32cbcc840464", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "72f2c9e8-5523-4709-8f16-f993771cdcaf", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "7b952de9-b991-4d00-b401-909bc404941e", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "7bdd5ab5-588b-4bc8-bcfe-f7a57de16583", "name": "abbreviation", "title": "简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "7c39142a-623a-4f6a-a182-384469aff8c5", "name": "displayOrder", "title": "行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "88a825dc-6b6e-4f7e-8533-13e8f51175d8", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "9a0d0f6c-cad6-420e-acd2-da52e4c30697", "name": "name", "title": "名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "9e12b5d8-7b54-4b5c-9448-897efc219270", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "a8404a7e-9d3f-4de7-ab5f-d13a16a32d37", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "ad0bcf6a-66e7-4281-8583-9d6058b2ca4c", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "b15d4cb9-a2a6-481b-a7f9-749d2a68bff3", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "b25b86b2-443a-4db4-9f77-162f619cd639", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "b9e183fa-b7ee-4267-ad91-b92283d5dbcf", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "c486d7df-6aa1-4896-a8e2-ea91bdd0a988", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "c654d3ee-cf84-4d14-acbd-43a28eb0e3dc", "name": "quantity", "title": "检查部位数量", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "e1ef2612-e64c-4b22-919d-2561b0eb80ea", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}, {"id": "e2481099-f351-46f0-925a-a28051ee34f9", "name": "disable", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "8b90f82f-afaf-46d9-9813-6c966abe2c67", "valid": true}]}