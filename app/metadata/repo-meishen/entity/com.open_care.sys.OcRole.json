{"id": "ceb95c42-a818-427c-aa1f-3e2701a23872", "className": "com.open_care.sys.OcRole", "name": "OcRole", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "角色", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "16b5535f-1830-4968-a22d-4f527566d7e5", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "31c5c531-1536-4658-bd65-3f2095e97808", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "36eaaba3-4c50-4b12-98d7-d8e8d1983354", "name": "medicalRole", "title": "医疗角色", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "4dfbb5b9-7bd1-41dd-a095-c7c5828e66ad", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "54c2c655-7958-45f9-b681-9e1a2c16303b", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "59ac0eef-df92-4117-9b8b-b0808d1f8b87", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "5dfb27d3-479c-4bab-8330-6e75049c8d84", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "62d9bb9d-2300-4ac3-bd8d-4e2eaa1572be", "name": "matchOrgTypes", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "6725edda-5a37-4e82-9464-511be5ca7e58", "name": "url<PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "7bff1196-68d3-48a4-b8e3-906b529557a5", "name": "name", "title": "角色名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["name"], "properties": {"name": {"$id": "7bff1196-68d3-48a4-b8e3-906b529557a5", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "角色名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "82c441ec-f861-4be3-a3f3-6a3bf4f3d8b1", "name": "urlAfterLogin", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "94dec105-ff29-462e-9d8d-bed662e3c8ba", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "998e7856-e894-492c-8927-fb3e7fbbc84d", "name": "businessType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "a67e0bd4-74a3-422a-a2d1-a5c8d386d077", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "a6806ec8-20f9-47b7-9208-ed9bc039f27e", "name": "<PERSON><PERSON><PERSON>", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "a9339242-fa63-4de0-b943-6fece5cf42ea", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "aeb249f0-26cf-4136-8c55-f22ebbbb8792", "name": "tags", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "b13567ea-3812-4b09-9876-eef762ab1bd9", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "bf1d9777-11e2-47a7-ac88-89c292b9f260", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "d84ea6ea-d796-4243-af90-5e5108ec325b", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}, {"id": "e89af525-6f23-452e-b8a1-b4ba00646e4f", "name": "key", "title": "角色编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["key"], "properties": {"key": {"$id": "e89af525-6f23-452e-b8a1-b4ba00646e4f", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "角色编码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "fde5ec53-9653-4fe6-be1b-faa9d5728ca2", "name": "description", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ceb95c42-a818-427c-aa1f-3e2701a23872", "valid": true}]}