{"id": "9ce26207-78d3-4bc0-94ae-5b4182340768", "className": "com.open_care.entity.chronic_disease.OCManagementGoal", "name": "OCManagementGoal", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "04518892-9baa-486b-913f-1bf3d62a991e", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "05dd15f0-7d27-4db0-aba7-cc5c53090ca2", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "1c5b8bb9-3051-4df2-938a-9d57131fc1a7", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "1e7f8603-e370-485e-b7d9-0d6c5ee4df5d", "name": "appointment", "type": "java", "classType": "object", "refEntityId": "10be78ce-f511-4ae4-b752-800aca8d0502", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "24ade35a-8206-4f80-acde-56b57c4bc481", "name": "targetFastingBgRange", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "29ba884a-99d7-4d18-bbe0-8d3fc11dc836", "name": "goalSetDate", "title": "目标设定日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "2c0937c8-5cb6-4f91-978c-34cbe4d7997a", "name": "targetHba1c", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "3881eb16-d4d6-4225-a03f-7a6c53b417c7", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "38a0c7b1-7a57-4086-8701-cced87a72d76", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "45ccf156-8754-415b-9834-008d36319371", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "620596b9-6fe9-4f26-88f5-bbadff4e0b06", "name": "weightGoalKg", "type": "java", "classType": "Float", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "6c2b0d00-f8f8-4f5f-8caf-e0a369eb4e3f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "71e8384c-e0aa-422b-8d8a-b9aeba919408", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "7c8cd933-2768-4cea-8aaf-0bf74a746a2c", "name": "managementPlans", "type": "java", "classType": "object", "refEntityId": "f7460e0c-6c99-471a-80d0-36e80d4898e2", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "7f001116-5571-4dd9-b97b-35f8ebd36e01", "name": "notes", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "8ce73eb2-54ca-48af-bbd6-17fa71555d8c", "name": "isActive", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "a0653407-fa15-400c-a4bd-8b04d2e71b99", "name": "exerciseGoal", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "a3196a44-d218-45ae-bd85-e79e2db9959f", "name": "targetPostprandialBgRange", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "ac93d45c-470e-4f29-b192-679ef70e5142", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "d159e7a4-b950-45d1-b1cd-3e67a5180b77", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "d19b8f4b-e25d-4b9e-8c57-656832f31971", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}, {"id": "dafa5a32-ecb5-4d64-b39a-f1c64904dbf4", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9ce26207-78d3-4bc0-94ae-5b4182340768", "valid": true}]}