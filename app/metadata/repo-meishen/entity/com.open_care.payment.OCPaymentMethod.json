{"id": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "className": "com.open_care.payment.OCPaymentMethod", "name": "OCPaymentMethod", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "order-service", "remoteEntityName": "", "fields": [{"id": "02fc83fa-d1d1-471a-9c0b-3c3809c36721", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "1ad95125-1861-4512-8b14-3a9319f0d5f5", "name": "accountType", "title": "账户类型", "type": "java", "classType": "AccountTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "73912d4212e4bf57c24f72b74f0c9115a2b06d3f", "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "1f9e0bb9-5b90-4c61-ade9-84c8c00a815b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "2e3c67f1-6f05-4a7b-b9ae-968e357382e5", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "36b64cdd-3b42-4097-ba86-4ec18a534685", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "3b936b7e-8699-4acc-abaa-0e1e374926a7", "name": "useNo", "title": "使用顺序", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "403d5bb5-dbad-43ba-9221-2199fafc3b14", "name": "paidPriority", "title": "缴费优先级", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "42110e8beefe9e296fe36544abdb8afc", "name": "printReceipt", "title": "是否打印收据", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "47e985f6-42b6-45c5-86d1-2254062b51c6", "name": "remoteService", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "5630b917-9ec1-4626-bd03-51f20acce387", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "7808c36d-9493-44e4-82db-7ca201fddd90", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "7de1ea10-2b8a-4919-933b-e2f698c49fbb", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "8cb2913c-adb3-4060-bf5d-d60e02c63b04", "name": "methodCode", "title": "支付编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["methodCode"], "properties": {"methodCode": {"$id": "8cb2913c-adb3-4060-bf5d-d60e02c63b04", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付编码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "98c6430c-3eeb-4028-9a0c-0eda8898b7e7", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "9d660b5c-7972-4841-b8dd-9b95a82d419a", "name": "seqno", "title": "支付顺序", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "99", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["seqno"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"seqno": {"$id": "9d660b5c-7972-4841-b8dd-9b95a82d419a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付顺序", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a47cc5ef-1824-4e5f-8311-5ee5f5aa1a24", "name": "methodName", "title": "支付名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["methodName"], "properties": {"methodName": {"$id": "a47cc5ef-1824-4e5f-8311-5ee5f5aa1a24", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "支付名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b2e0368e-134c-4e04-ae6b-f2607898e87a", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "b319aa96-35dc-47d2-b259-770d81d767bd", "name": "refundPriority", "title": "退费优先级", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "bf526759-0ccf-45de-92eb-c2ee1b902d96", "name": "useToclearBills", "title": "是否可用来清账单", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "cbab560f-4320-4266-9ac7-46b50ec1c79d", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "cc9130c1-0cfa-4fde-9c76-ff95f5cff0c5", "name": "remoteMicroEntityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "e4ca75e5-e517-4e3d-9af9-3d077a3bd9bd", "name": "cashPaymentMethod", "title": "是否现金", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "e5af0b73-82f2-4854-aad0-14cdc12f7ac4", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}, {"id": "f9ae75fa-ee35-47f1-937f-78a3db1eaa35", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "b72aabc8-196b-4b40-a5ad-34d73166c3fe", "valid": true}]}