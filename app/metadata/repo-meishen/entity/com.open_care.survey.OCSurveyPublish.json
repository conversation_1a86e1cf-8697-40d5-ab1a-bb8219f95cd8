{"id": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "className": "com.open_care.survey.OCSurveyPublish", "name": "OCSurveyPublish", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "02c1b4d0-2c00-4665-9b4c-364c48a6c12d", "name": "endDate", "title": "结束日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["endDate"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"endDate": {"$id": "02c1b4d0-2c00-4665-9b4c-364c48a6c12d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "结束日期", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "12231643-5ffe-4f3f-92a4-a56cdf3d99e4", "name": "url", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "2d20977b-6fa8-41b8-a8bb-728c9b8c38d4", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "3264921b-0499-46a5-b741-8de1061b6340", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1f53fbba-368d-48a3-a64e-3af484a72444", "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "32ca00af-2758-43b6-b27f-2aaba73de0a9", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "3fd2d3df-002a-44f0-98f5-ee8f4176c02f", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "73396e24-026e-432f-97f3-74d9d9e91e77", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "73f39ab7-dd5d-48ae-b58d-f500ca6a5e8e", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "781d21e3-d91d-4ec1-bb48-cb9a81af8dd4", "name": "title", "title": "发布标题", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["title"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"title": {"$id": "781d21e3-d91d-4ec1-bb48-cb9a81af8dd4", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "发布标题", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "7df68d26-cffb-46ee-824d-5113742ccc32", "name": "canceledUser", "title": "作废人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "8f3dafd5-136b-4635-b68f-faa5541e9ba5", "name": "customerId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "a18601b4-29bc-4170-9b87-7c60307a04ec", "name": "beginDate", "title": "开始日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["beginDate"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"beginDate": {"$id": "a18601b4-29bc-4170-9b87-7c60307a04ec", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "开始日期", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "a8cc48bd-5fdb-4d61-b3fa-6f0b74563fea", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "c07b3bb8-a06c-4eef-afcf-4c77aabce2b7", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "d6082d3c-c2f2-453f-a3e2-08fce752d05e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "dc347586-cee2-46fb-acc7-7dab55eda3e3", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "deec829e-e9a0-4f7b-afc5-fbdfc459cf1e", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0bfab962-6a77-4527-8256-e046e25w3477", "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "e02f0f24-d6c4-4c4c-b59c-770e09f77c50", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "e6308ec4-77f4-494d-a6f0-f4b43b0b9c5c", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "e6308ec4-77f4-494d-a6f0-f4b43b0b9ddd", "name": "survey", "title": "问卷", "type": "java", "classType": "object", "refEntityId": "1a556ac0-e3b4-4b01-83d2-f9937b96f0ba", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["survey"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"survey": {"$id": "e6308ec4-77f4-494d-a6f0-f4b43b0b9ddd", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "问卷", "required": [], "entityName": "<PERSON><PERSON><PERSON><PERSON>", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e97c6008-c1dd-4133-a0ed-b9b963f98927", "name": "surveySession", "type": "java", "classType": "object", "refEntityId": "f4ab4f35-3992-4aea-974d-5949d41f2670", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}, {"id": "f0f755da-8198-4d3a-848d-1c7c910ce193", "name": "canceledReason", "title": "作废原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "d1950faa-1d86-49ec-9f25-ccd54913ed26", "valid": true}]}