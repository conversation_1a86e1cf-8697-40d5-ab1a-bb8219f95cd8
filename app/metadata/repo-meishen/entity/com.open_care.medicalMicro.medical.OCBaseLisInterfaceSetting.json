{"id": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "className": "com.open_care.medicalMicro.medical.OCBaseLisInterfaceSetting", "name": "OCBaseLisInterfaceSetting", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "033c2a50-2938-4adf-ab27-5b6185f6f994", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "0f65d0ae-1edd-4c6b-a941-a9ae2fd4015x", "name": "sameBarcodeCanBePartiallySubmitted", "title": "同一条码号可部分送检", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "defaultValue": "false", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"sameBarcodeCanBePartiallySubmitted": {"$id": "0f65d0ae-1edd-4c6b-a941-a9ae2fd4015x", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "同一条码号可部分送检", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0f65d0ae-1edd-4c6b-a941-a9ae2fd40165", "name": "showApplicantInfo", "title": "显示申请者信息", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "15cef318-d0cb-4801-94bb-55b84c23741a", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "18aa55c6-1236-4a7a-9ca1-87c66e44c061", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "25da3a3f-344b-49d7-937c-7b2fd1b80c66", "name": "showProductName", "title": "显示产品名称", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "3528e94f-f5a3-4ecd-9a76-16ba85b395a2", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "3877bc65-bc82-4ad9-90ea-2a129fa0f015", "name": "showProductItems", "title": "显示产品明细", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "472801fa-d273-48b7-abba-822941169843", "name": "showProductPrice", "title": "显示产品价格", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"showProductPrice": {"$id": "472801fa-d273-48b7-abba-822941169843", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "显示产品价格", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "472801fa-d273-48b7-abba-8229411698a1", "name": "useDefaultApplicant", "title": "使用默认申请者", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"showProductPrice": {"$id": "472801fa-d273-48b7-abba-822941169843", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "显示产品价格", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "56d939b4-c600-4acc-a1ad-18dfdb3d645f", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "66d0e82a-0434-4266-a657-ff37cbb9e81d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "7e77edcb-94e0-4e85-918f-01be5d70064e", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "83625e92-8f75-412e-9ac8-618a43a72a9f", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "899c6807-7bc3-4522-bf27-cf1815f148e2", "name": "showProductCode", "title": "显示产品编码", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "8b580dab-e0d0-4a37-81e4-c52fa240807f", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "94459e29-31d2-468a-ab82-2b52a81afa1e", "name": "showApplyDepartmentName", "title": "显示体检机构名称", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "990c9b99-e391-43db-a18c-73169b2bc6a4", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "9d840d97-b6b1-445c-a10b-674f01a4668a", "name": "applyDepartmentName", "title": "体检机构名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"applyDepartmentName": {"$id": "9d840d97-b6b1-445c-a10b-674f01a4668a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "体检机构名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9d840d97-b6b1-445c-a10b-674f01a4668b", "name": "defaultApplicantName", "title": "默认申请者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"defaultApplicantName": {"$id": "9d840d97-b6b1-445c-a10b-674f01a4668b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "默认申请者", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "abcd9ff8-a31c-4a82-a8d5-8de6fb720d88", "name": "selfJudgmentQualitativeResultFlag", "title": "自行判断定性结果标记", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true", "entityName": "com.open_care.configuration.configuration.AdFieldRule"}], "uniqueItems": false, "verifyRules": [], "defaultValue": "false", "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["selfJudgmentQualitativeResultFlag"], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"selfJudgmentQualitativeResultFlag": {"$id": "abcd9ff8-a31c-4a82-a8d5-8de6fb720d88", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "自行判断定性结果标记", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b0264c74-e290-4624-b096-8148597a2d5d", "name": "normalQualitativeResultValues", "title": "阴性结果值", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"normalQualitativeResultValues": {"$id": "b0264c74-e290-4624-b096-8148597a2d5d", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "阴性结果值", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "b3732817-3e14-46e1-aa6f-558f06f60fb9", "name": "useSubmitInfoForSampleInfo", "title": "送检者信息为采样者信息", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "bc0a6ee1-7dcc-4dcd-af90-2441c0b4a823", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "cd6f1618-5848-4e07-a3f0-1fbff29ed859", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "da6ea1a5-9709-48c6-a6ed-940f8680a9e1", "name": "deliveryOrgOcId", "title": "外送机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c69a5bbf-fac7-4a47-940a-1d841add43gg", "style": "Select", "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["deliveryOrgOcId"], "properties": {"deliveryOrgOcId": {"$id": "da6ea1a5-9709-48c6-a6ed-940f8680a9e1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "外送机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "dc1f74f2-daf0-4ff6-830a-fb60fed0ebf3", "name": "showProductItemCode", "title": "显示产品明细编码", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "efcd65c9-031d-4efd-8073-2bee93f8df1c", "name": "showSampleInfo", "title": "显示采样信息", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}, {"id": "ff8d7584-322f-4a72-ae00-4ad97eaa0b89", "name": "showSubmitInfo", "title": "显示送检信息", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "0fc3ec7c-d6f9-4e8b-85a5-450de5a09ed1", "valid": true}]}