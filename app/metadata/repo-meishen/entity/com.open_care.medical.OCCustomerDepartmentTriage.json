{"id": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "className": "com.open_care.medical.OCCustomerDepartmentTriage", "name": "OCCustomerDepartmentTriage", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "00716de2-9231-46bb-abb4-2273b4c352e9", "name": "triageRegistrationTime", "title": "分诊登记时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "058d608e-f732-4527-940a-5030bd336e41", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "11793a1d-3ff6-44da-aa0d-eda3a8dd0563", "name": "finishExamTime", "title": "完成检查时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "13f12135-59de-4f78-859b-a6e65748ca6a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "1d18f512-4833-4e58-8b67-0eab5154b5cb", "name": "departmentOcId", "title": "科室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "1d259740-0cce-414f-bf97-898b40d1af8c", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "349924cd-e470-4343-acfb-6b6a7241281f", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "3ae015af-3e5a-44dc-91fa-b78cfbf79a89", "name": "triageRoom", "title": "就诊诊室", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "3d5f0fc5-ecaf-4b72-bfd1-21da3f45d7f0", "name": "expertNumber", "title": "是否专家号", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "4dcad16b-7245-4458-9854-4239e332820f", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "5c7d8638-0c51-4369-94ad-d160ce1227f8", "name": "partyRoleOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "6593e8dd-be26-4078-b2d3-dd8e25ef9058", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "66bcd8bb-44db-4695-a258-db5b3faf7f73", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "69a952cf-9d29-4140-9b9b-516d2b92d455", "name": "doctor<PERSON><PERSON><PERSON>d", "title": "检查医生", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "6c5235b0-7a44-4080-a8cd-ae749b30532d", "name": "beginExamTime", "title": "开始检查时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "7faa373d-ce0b-4973-9068-e37ef38d8c2e", "name": "queueNumber", "title": "队列号", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "89b6eda3-0eae-46ec-8381-36be80082e31", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "afac53d3-f110-4757-9687-d73002bf0386", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "c403c9ee-b829-4c1e-ad5f-98b7e8343f28", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "d95be230-a85b-4fc8-9d57-0bd557167f4d", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "e8a72602-59de-49fd-bb18-363b5fc32a46", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "f34f31e3-619b-4b03-9304-b5a030fdab54", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}, {"id": "fff2ee6b-4e4f-4407-81bf-eb67799100bd", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "78c57af4-0bc6-4da1-abab-63336b2ee1b6", "valid": true}]}