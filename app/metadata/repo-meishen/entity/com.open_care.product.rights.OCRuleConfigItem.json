{"id": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "className": "com.open_care.product.rights.OCRuleConfigItem", "name": "OCRuleConfigItem", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1ca7303a-c5e1-443e-befe-677d591eefd9", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "39180ce6-bcc9-4bf6-bcaa-b0cdcb78a54a", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "3b398f81-c034-438d-a92e-46a14c069ed4", "name": "ruleConfig", "type": "java", "classType": "object", "refEntityId": "c3899359-00df-42f2-ad1d-c717000ca057", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "46c142e0-db0e-499f-83c0-d7e9acca6ed9", "name": "sortOrder", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "4a8d2f5e-f6af-4239-992f-6467f9f29483", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "5a994327-df74-4380-89e7-64908936f77d", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "5bc14291-982c-475c-a9d3-a82706b75a5b", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "72801666-51b6-4c27-ace7-93e8ff1348b3", "name": "ruleSet", "type": "java", "classType": "object", "refEntityId": "0af576cf-3688-4881-9e62-c9c604285878", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "79ca8d9a-656d-4bbd-9ef1-5637c1564fef", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "b1904005-2e19-4def-8641-72c00894fc0e", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "b9338870-01d0-454b-b4aa-e0cbad9a06af", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "c4576039-a545-40e3-82fb-35d8677eb853", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "d2e89200-9087-4b9e-beeb-0bd4c3969191", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "d90e7ab7-7a1d-4f9a-aadc-b76cc4e63534", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "ded68b7b-8465-4ee0-9e6b-483afab217c4", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}, {"id": "ebe170a7-ce85-49f5-b422-fc87f835c0d0", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cde434d6-ed5f-4d33-a2a4-5a79f9fad695", "valid": true}]}