{"id": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "className": "com.open_care.jsonObject.OCStorageCertificateRecord", "name": "OCStorageCertificateRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "25166720-3484-4cf9-85a7-3bebdddc4a55", "name": "deliveryMethod", "title": "交付方式", "type": "java", "classType": "DeliveryMethodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c4e81aed9262f4c3d6d5c19ab20f440f15b3a9cf", "entityId": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "valid": true}, {"id": "4da93561-f796-4bf9-94cd-bcaa37b156a4", "name": "storageCertificateNumber", "title": "存储证书编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "valid": true}, {"id": "91011270-c950-46de-9e93-5d924fa65446", "name": "storageDate", "title": "存储日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "valid": true}, {"id": "c3e207a6-9c2d-4e36-8270-8ab4dca44729", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "valid": true}, {"id": "cefd413c-26df-44d6-9556-508ddcdb1f6f", "name": "attachments", "title": "附件", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "valid": true}, {"id": "fd50c66d-b7d5-4db0-9c80-9a03ca67145a", "name": "storageCertificateCompletionDate", "title": "存储证书完成日期", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "fb1b2514-59cc-4e53-8698-5e1b2057e7fc", "valid": true}]}