{"id": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "className": "com.open_care.sys.OCFlowNodeRule", "name": "OCFlowNodeRule", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "06d14fc8-488a-4e58-a5ab-ccb01b67e4cf", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "2589c41d-06b1-47e2-82a0-0b5fae017d22", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "2ab31fe3-b478-4fa4-b869-249b90042864", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "32103aa9-a25a-4763-aa56-16717ffed266", "name": "parallelGatewayKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "34d70679-de53-4fc9-88a6-e75d69fe8c03", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "3d7f6dc2-d80d-462d-8496-2c720cc3b85b", "name": "flowNodeKey", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "43a6d379-e865-40ff-924c-b5c2b21d38d4", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "45cd3868-4e80-43dc-9ef5-9ec5a4d4be2c", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "4b5a8f1b-c318-4b0e-a999-4dd07c9aeb17", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "52677b66-0076-4696-bf9f-6fc821f409eb", "name": "processRule", "type": "java", "classType": "object", "refEntityId": "aeac51cc-b988-4851-9dff-dd8c6ec5db54", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "5656be8b-ed5a-4d0e-a68a-041d8d641bcb", "name": "connectFlowNodes", "type": "java", "classType": "object", "refEntityId": "9d555168-7a0d-490c-bc32-a69ab01ec993", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "5d153876-fc39-4178-a438-e9970614595d", "name": "exclude", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "7e17d073-23d6-4e20-b04f-6fa0cae4fac3", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "7fa3602e-5533-46f1-b565-571e241e4375", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "86766dbc-f3ba-43f7-beef-a19148179e84", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "b021191b-3f10-4d2b-ba4c-ef2cfb73394c", "name": "disconnectFlowNodes", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "ba4e9d9d-3267-4da8-9980-0bf3705901b9", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "cf1c8cf3-5288-4311-a34b-d89c8e76599f", "name": "parallelGateway", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}, {"id": "f28784dd-ecb9-4fa6-879a-4c82097ee91b", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "eb7eca6a-65c2-4d03-ba08-8cced6a8e33c", "valid": true}]}