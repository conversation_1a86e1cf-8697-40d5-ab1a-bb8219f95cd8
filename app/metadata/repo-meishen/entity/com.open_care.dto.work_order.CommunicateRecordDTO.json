{"id": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "className": "com.open_care.dto.work_order.CommunicateRecordDTO", "name": "CommunicateRecordDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "20d259c7-97cb-4609-ba1d-9602bed44e87", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "21c2350c-525a-4c9d-85de-72f3d1110bc9", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "38817017-cce1-4ed3-9611-edb95ed94250", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "3a55f96d-dad0-4edf-9acb-c722c7692866", "name": "communicateFailReason", "title": "未成功原因", "type": "java", "classType": "CommunicateFailReasonEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "8a2e7909bd0a6fced79f62951c56b913169ae861", "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "4ce3fe3d-bd2a-4920-aeee-636c7239dd7f", "name": "remark", "title": "备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "4f182425-fc84-49b0-9bd7-c869379a0ade", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "4ffa5c3b-ec4d-4f10-8244-544fbd54953d", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "5d6f8da7-e381-4576-91e7-136af89b2514", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "8f2a0d82-2a9b-426c-b679-b86860298fa8", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "99d2fc5d-c578-45f0-a2df-f003a7ccb9e8", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "a3b67143-8232-45f5-899b-d78b50b4d7d7", "name": "count", "title": "沟通次数", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "c1a818f2-0510-4d97-bd9d-dd90e6dc0f3d", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "c2b108a2-80c2-4408-a117-76cf9f2380f2", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "ce6b508b-1f07-4dfd-9b53-105d74078447", "name": "workOrder", "title": "关联工单", "type": "java", "classType": "object", "refEntityId": "9c295c0d-519f-4f4f-bb19-173234ecfa62", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "daaa12c2-c92a-4408-b70f-028bba31b502", "name": "communicateResult", "title": "沟通结果", "type": "java", "classType": "CommunicateResultEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "ebb1e108ca39793154abd9d579afe0b26b61672f", "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "dc90a0c5-526c-4f05-ae73-8b40b119ef74", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "eb0a0733-58d2-4c18-9a46-fe522714a969", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}, {"id": "efac0b64-f3ee-48fb-a47e-ece45b86efa9", "name": "expectVisitTime", "title": "期望访问时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4beea7db-9dc2-4554-9bd4-2cae8e673a36", "valid": true}]}