{"id": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "className": "com.open_care.sys.OCDataAuthority", "name": "OCDataAuthority", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0178c307-ecc2-4f46-8122-088c5c0e5a66", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "14ec3c48-dec8-4b6c-aa0c-43a5236a4c12", "name": "authorityRanges", "title": "过滤的entityName 集合", "type": "java", "classType": "object", "refEntityId": "fae543c6-956f-4481-b656-98545b0a80da", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "1d3b491e-0ed1-4300-995e-4feab2945958", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "219fa4bc-cc86-4153-afef-18428626d85d", "name": "remark", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "22625701-8218-4a63-ae36-1154a7d10632", "name": "orgTypes", "title": "过滤条件生效的用户的机构类型", "type": "java", "classType": "OCOrgTypeEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "f46fb9e5df1c2331c89c31d3b61657c12061a797", "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "271a3ecb-3d8a-4092-ab00-f56ca81e3d80", "name": "relationshipType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "34bea7c1-3a27-4dc7-b522-43bbdd4d3bc4", "name": "type", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "3535957c-cfbc-4183-86e0-1f18b00f6dd0", "name": "viewCondition", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "3ec526e8-ff7a-4896-9104-7cb3ece7f7a8", "name": "className", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "4221f411-e143-4e91-83e3-14ceae90b40c", "name": "orgType", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "4f7c3e63-e749-4c90-92bd-8528bf5087a8", "name": "roleIds", "title": "过滤条件生效的用户的角色id", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "55fa2e27-9015-49b2-8c63-363ea45a8df6", "name": "editType", "type": "java", "classType": "DataAuthorityFilterType", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "64a68fbcf28eea5b8581eda3b5221c05121ee728", "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "5aa494dc-9ea4-4f1f-8f51-41e82d3697d2", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "66014de9-5297-4930-b7e6-cfc7a0edea45", "name": "types", "type": "java", "classType": "DataAuthorityType", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "4b5b9c0a884c2e4ad649d6805aa8a2ac19cd1370", "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "7966bbcf-50a6-4064-a41d-28a138c95b3b", "name": "userIds", "title": "过滤条件生效的用户的用户id", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "7d52bbaf-dca1-4d96-918f-21f5238bbe24", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "817f7d10-bc14-4c51-9c85-c41e7962a9a5", "name": "userFieldName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "84b5dcbb-c24d-4fa3-aaa1-931b4033e2b4", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "87f99c82-931c-4a9c-8fff-37e1e4f0b326", "name": "relationshipTypes", "type": "java", "classType": "DataAuthorityOrgRelationType", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "fe59335fab09ae01d0c11611966b9abf841f19cf", "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "8d498c1b-1b33-424e-9ef9-0fa3cc069ec1", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "8f896583-e7f2-4a79-85e0-341a34caf1d3", "name": "is<PERSON>r<PERSON><PERSON><PERSON>", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "96ac0403-be6c-474c-9e61-77fff368ed4d", "name": "editCondition", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "9a2d0c97-e0ef-46c2-ae4b-6eeda19fe200", "name": "usernames", "title": "过滤条件生效的用户的用户username", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "9d3a99ef-31d5-4573-be9f-bf73e4742797", "name": "orgCode", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "a2ec5378-89ec-439c-848c-5608956b001a", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "abae6dea-cf4b-4b4a-ba96-01724b59f460", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "b313e88e-328e-46a9-97f8-113d3587eb9b", "name": "isDefaultRule", "title": "是否是默认规则，如果根据用户规则匹配不到规则，则取默认规则", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "c8481034-96fa-42b6-adac-2aa17698ef48", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "cacdbc61-0da7-4c2b-8b2e-e5e6cfb65c92", "name": "orgIds", "title": "过滤条件生效的用户的机构id", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "cdc19040-d2b4-428f-b42d-421a848dd3ad", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "d0c0b9fc-45e0-4b3e-970d-ba4351932542", "name": "defaultRule", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "d4b9d6d9-f77d-4328-aee6-791eac6a1f04", "name": "viewType", "type": "java", "classType": "DataAuthorityFilterType", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "64a68fbcf28eea5b8581eda3b5221c05121ee728", "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "da088ce3-f520-4954-93d5-91ea00ac5b1c", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "e00f7364-525e-4015-9099-13834aad0416", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "e0acf9ad-8770-4356-be6b-28054e68ee25", "name": "orgFieldName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "e799d40b-2178-450f-bfec-98099fc65213", "name": "roleCodes", "title": "过滤条件生效的用户的角色代码（role.key）", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}, {"id": "ff43fa40-c748-4281-8bc9-45e185a8cca8", "name": "roleTypes", "title": "过滤条件生效的用户的角色类型", "type": "java", "classType": "RoleTypeEnum", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "387a1bbe4cd472f6eba81aaf152d2ee1876475bb", "entityId": "cc911b9e-0c5b-45bb-855b-07733d6c40e0", "valid": true}]}