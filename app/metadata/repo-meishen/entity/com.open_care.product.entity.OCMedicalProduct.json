{"id": "871ce513-f672-435f-bea6-ff2066f37a54", "className": "com.open_care.product.entity.OCMedicalProduct", "name": "OCMedicalProduct", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "诊疗项目", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "022150f7-3b67-45ae-a236-d2f4a6be7368", "name": "printCategoryFlag", "title": "打印时类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f9fc0b3b-aaec-4602-808a-03c6dced4688", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"printCategoryFlag": {"$id": "022150f7-3b67-45ae-a236-d2f4a6be7368", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "打印时类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "0e8962e4-97ad-49ba-bc30-ac9cb1372e5b", "name": "comments", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "0ef99295-d2e7-4033-9b76-4816cc83d247", "name": "tags", "title": "标签", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "1080cb71-483a-44b5-9cf1-46771ca77735", "name": "productTypeEnum", "title": "产品类别", "type": "java", "classType": "ProductTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "a412e1fa5e9b5c0522b9b505403365f9953fdd16", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "173d6ec0-2fca-4b6f-9bbe-781d2db41ac9", "name": "productNo", "title": "产品编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"productNo": {"$id": "173d6ec0-2fca-4b6f-9bbe-781d2db41ac9", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品编码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "17b92a90-d727-45cc-868b-bb1c945db4e7", "name": "appointmentInfo", "title": "预约信息", "type": "java", "classType": "object", "refEntityId": "4b085b22-c12a-42e1-a1b1-e9d2599a306a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "183d2c6b-95dd-4169-9c5e-8bead268d55c", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "1af23bb0-6602-4d6e-8c4e-bda35b6ea945", "name": "canSell", "title": "是否可销售", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "1dbdf3b2-f975-4365-8d57-f9cb7f8e5cc3", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "1f44c65d-3200-48c4-866a-6d2eef969627", "name": "phonetic", "title": "拼音简码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["phonetic"], "properties": {"phonetic": {"$id": "1f44c65d-3200-48c4-866a-6d2eef969627", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "拼音简码", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "1ff7f34f-5944-430c-9404-260643fc343b", "name": "bloodProject", "title": "血液项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "2038e41d-57e9-4f44-ba70-1469f4def312", "name": "disabled", "title": "禁用", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "29808723-f3e9-4471-984b-e317d4268edf", "name": "status", "type": "java", "classType": "OCProductStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "7f4e41d9bf4d856872e61acab81b030d08583b05", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "2bcfaa48-0717-4351-9e95-179c8ae1d65f", "name": "examPartDirection", "title": "检查方位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "2c40a7d1-0196-40f4-bba5-3c3ce084467f", "name": "serviceDuration", "title": "服务时长", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "34798849-f4c4-4c2f-aad7-e48a5ca07955", "name": "englishName", "title": "英文名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "34b28543-9548-4cd4-9caf-6bf74edbdaa3", "name": "sampleType", "title": "标本类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"sampleType": {"$id": "34b28543-9548-4cd4-9caf-6bf74edbdaa3", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "标本类型", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3d957a32-44c7-4172-83e4-9b8f17580a0b", "name": "productName", "title": "项目名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["productName"], "properties": {"productName": {"$id": "3d957a32-44c7-4172-83e4-9b8f17580a0b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "项目名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "3e15919a-4ead-4790-af1f-21e41cb0d4ce", "name": "serviceInfo", "title": "服务信息", "type": "java", "classType": "object", "refEntityId": "a5c5b379-c013-4fa4-baf0-bece06477fdf", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "3e90d58a-1d66-4745-8edb-9e2d8ce4d936", "name": "fromDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "3f8bba84-50ca-4860-9e2e-fdfcc6ff7d05", "name": "medicalProductComponents", "type": "java", "classType": "object", "refEntityId": "f227f42a-798e-404d-8072-ea04719c638b", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "402c02f2-8e67-4393-8319-e72dbad9f6bc", "name": "timeoutInfo", "title": "超时提醒", "type": "java", "classType": "object", "refEntityId": "bcdf5a27-ff91-4b72-8618-506ab447bfb8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "4063450f-8899-4776-866b-3bdc30f670ac", "name": "pacs", "title": "是否对接PACS", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "4494ccb0-9db4-4c89-a69b-9760105e8db7", "name": "brand", "type": "java", "classType": "object", "refEntityId": "f9317ca8-02a7-4f69-8069-f4f0e61e6ef2", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "452f22c6-4af4-4396-8ade-b1d037ebb682", "name": "dispatchAndAcceptanceRule", "title": "供应商接派单规则", "type": "java", "classType": "object", "refEntityId": "ec7a66f5-905d-4601-a120-6afb5fe13d23", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "4a6ea97e-f7f2-43bb-b970-dc0066e7a2b0", "name": "agreeTemplate", "title": "同意书模板", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "5085dae8-d5fc-462f-b915-0b1d46f552b2", "name": "productInspections", "title": "送检机构", "type": "java", "classType": "object", "refEntityId": "478c0a7a-06d3-4395-ab69-e8783515802a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "50979eae-c809-4f53-8878-18d8c0256c91", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "53bbc773-df76-4a91-83df-8216c6e8e5a1", "name": "serviceResources", "title": "服务资源", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "5cb317ad-9f7e-49b5-b4a7-d324c17c8104", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "5d6f26d9-92a0-4617-ad72-ad7223f6ec8b", "name": "hepatitisProductType", "title": "乙肝项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "5fd7280a-64a2-403e-8af8-92ffac6b97b5", "name": "medicalProductType", "title": "项目类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "64c6f195-a588-4ff4-a1d0-e99da758c20d", "name": "productReportUnit", "title": "所属报告单元", "type": "java", "classType": "object", "refEntityId": "1d0847f3-9bf4-43eb-a8b9-17f2ae85c6cd", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "c112906c-e6ab-40ad-8a67-1084093522c4", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "66248ede-9ad1-4b87-aef1-6ea04bd166b0", "name": "examPart", "title": "检查部位", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "6737d2e0-b449-4cb7-b37e-64008f7bb87c", "name": "productType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f415ae72-8dd8-4e80-8daf-34e63ecc2912", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "6edd0956-777c-4201-95d7-14ed0a2c0e5f", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "72a02c50-27fc-4a49-82ec-9ee20d953495", "name": "auditStatus", "title": "审核状态", "type": "java", "classType": "OCProductAuditStatusEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "b46b46e30ec540dffb8a1d2910673c202c96e0a2", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "72fc3487-164b-4929-b06e-120f30e3fea1", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "7367494a-9a0a-47d3-afc6-15c6120bf850", "name": "mallInfo", "title": "商城信息", "type": "java", "classType": "object", "refEntityId": "ac78171b-b49e-4a2a-9739-a7deb58152d8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "747fba4b-bc9e-49c4-aac1-c0cf873db828", "name": "barcode", "title": "是否配置条码", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "79af4a8c-3be8-46d9-b2bb-fc7b2421c583", "name": "productSop", "title": "产品SOP", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "820f6312-7c02-4009-b9fa-366117d5c5b9", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "85baa5f0-e503-4b27-b11a-3fd10bc29e11", "name": "urine", "title": "需憋尿", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "86a109e8-33e5-4b62-b16f-adbfdff4bef5", "name": "<PERSON><PERSON><PERSON>", "title": "产品负责人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "894d5008-cbdc-44f8-addd-ae55a3b368ea", "name": "thruDate", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "8f83056f-6289-40e7-be88-d4166cffde2a", "name": "description", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "90cacc2b-e59e-4a85-ba0c-be5dd6996f0a", "name": "invoicePrintCategory", "title": "打印发票时产品类别", "type": "java", "classType": "object", "refEntityId": "dcc64fe7-6a12-44a3-b254-7add8cbc9e55", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "d1f6730c-1b40-47f4-9292-d5c854dcff8c", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"invoicePrintCategory": {"$id": "90cacc2b-e59e-4a85-ba0c-be5dd6996f0a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "打印发票时产品类别", "required": [], "entityName": "OCInvoicePrintCategory", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "94acb552-2052-4e09-8780-904811b73887", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "9a765ed3-65cc-487d-8f2a-46547fa416a7", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "9c4f9d50-bf53-456e-9227-b421ad2c3bbe", "name": "productMedicalName", "title": "产品医疗名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["productMedicalName"], "properties": {"productMedicalName": {"$id": "9c4f9d50-bf53-456e-9227-b421ad2c3bbe", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品医疗名称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "9d674563-e289-4633-9a10-587a7f231cdd", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "a27b3457-76a5-4bec-92bb-f896f30ff2b7", "name": "preStageId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "a94e9b54-ad85-4303-b8ca-e9014adcd466", "name": "otherSysCode", "title": "其他系统编码", "type": "java", "classType": "object", "refEntityId": "84b0cdeb-49ef-4965-afa0-3825b0a947b6", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "b2d7c6d8-747c-48b5-a437-e06b36f405fe", "name": "meal", "title": "就餐项目", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "b85f9ea9-1c66-4860-b683-47bc3aa71aba", "name": "processTemplateKey", "title": "流程模板", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "1e55cb9d-560c-41c1-81a5-4fc879478bbb", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["processTemplateKey"], "properties": {"processTemplateKey": {"$id": "b85f9ea9-1c66-4860-b683-47bc3aa71aba", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "流程模板", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "be0c5bdf-36ab-4c04-b4d8-ff95f0080f77", "name": "productUpOrDownEnum", "title": "上下架状态", "type": "java", "classType": "OCProductUpOrDownEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "5783903a3025d203419af1adb17c5b58e2779555", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "bfa3ea1e-8226-42a3-89cf-368c782249a1", "name": "mealType", "title": "餐前餐后项目", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "e93ba801-7caf-4a95-b02e-06e9d4fc82dd", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["mealType"], "properties": {"mealType": {"$id": "bfa3ea1e-8226-42a3-89cf-368c782249a1", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "餐前餐后项目", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c3a605f8-805e-4dc9-88dd-90e79a17a255", "name": "productAttributes", "type": "java", "classType": "object", "refEntityId": "05ad013f-9028-48e7-b064-e72934c69b35", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "c3ac8885-ef8e-4ee5-ae61-8af543c8d3a9", "name": "interfaceType", "title": "接口类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "6e1c3a98-4cd4-4ae0-90a3-c0724a70beeb", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"interfaceType": {"$id": "c3ac8885-ef8e-4ee5-ae61-8af543c8d3a9", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "接口类型", "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "c6509d29-802b-438b-a010-30a5470b9b82", "name": "purchaseTips", "title": "购买提示", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "c89c467e-e4c2-4558-8a2e-326624eb966d", "name": "manager", "title": "产品负责人id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "c9854f79-0108-4736-8a08-5549d3cb2165", "name": "showWorkbench", "title": "管家工作台显示", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "c9c2b7ee-134f-48eb-98a0-f169d66a950a", "name": "showOnGuide", "title": "在导检单上显示", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "cb948f7f-5139-4234-a13c-e5f777c0163e", "name": "examOrgs", "title": "送检机构", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "df7798ca-4c88-4b0f-9fb8-f4092ccff1d4", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"examOrgs": {"$id": "cb948f7f-5139-4234-a13c-e5f777c0163e", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "送检机构", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "cfdaf149-44b3-446e-a3d3-e71aac887c17", "name": "processDefinitionKey", "title": "流程定义", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "d1cbf6e5-07b2-b3c7-2880-fcf7320aaeff", "name": "productGuideGroup", "title": "产品导检单分组", "type": "java", "classType": "object", "refEntityId": "cdcc0a88-0f8c-dda6-dd86-081ebaa2cece", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "3fd0c9c0-009d-4333-8f94-30a2c4968907", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "entityName": "com.open_care.jsonschema.JsonSchemaObject", "properties": {"productGuideGroup": {"$id": "d1cbf6e5-07b2-b3c7-2880-fcf7320aaeff", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品导检单分组", "required": [], "entityName": "导检单分组", "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "d5d1055c-1d4d-435e-9f80-66a190ce4e44", "name": "separateReport", "title": "是否单独出具报告", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "d7539cb8-45b8-4d63-b0c6-dc00b1e0e52b", "name": "productIndicatorCategory", "title": "所属健康指示灯类别", "type": "java", "classType": "object", "refEntityId": "d5e1113b-eb2e-479a-af67-1f111379b258", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "56a61dcb-06ff-4ace-b2d4-907c2607e4ce", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"productIndicatorCategory": {"$id": "d7539cb8-45b8-4d63-b0c6-dc00b1e0e52b", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "所属健康指示灯类别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "de3b3db5-4d1c-4a82-8997-db77aa88af91", "name": "organizationProducts", "title": "不同机构的编码", "type": "java", "classType": "object", "refEntityId": "6df08ca0-8a96-45f6-a0e0-bc92eab29141", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "e15149ff-c087-4631-aee4-f402b20a1fa4", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "e18cd386-ced7-4d62-8d9f-ea46218d5ab4", "name": "inspection", "title": "是否外送", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "e364e3cb-967d-4b2b-9b38-ca0bca385715", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "e4a4a960-f8cc-4e19-982a-3a1e4cff7eb4", "name": "productComposition", "title": "产品组合", "type": "java", "classType": "OCProductCompositionEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "fb77728c82b4976215813d01658d30647905b917", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "e4e66251-41f3-4fcc-8398-4cd41da477ab", "name": "abbreviation", "title": "英文简称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Input", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": [], "properties": {"abbreviation": {"$id": "e4e66251-41f3-4fcc-8398-4cd41da477ab", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "英文简称", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "e5e15f69-8ddf-4881-b702-1bbb5ba823d0", "name": "departments", "title": "检查科室", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "referenceId": "da973ffe-91ee-4e34-bf16-d78cf63aa12b", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["departments"], "properties": {"departments": {"$id": "e5e15f69-8ddf-4881-b702-1bbb5ba823d0", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "检查科室", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}, {"id": "ecf1bf87-8c7a-4958-a5a3-c05572135060", "name": "checkType", "title": "检查类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "f0b8c546-5ee5-428e-8221-7830d4d04944", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "edf0bedc-95ed-4bda-9671-bf403cd4d499", "name": "seqno", "title": "产品行序", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "ee54554a-2c2c-4e51-b7e5-1f430ad5360a", "name": "attributeSets", "type": "java", "classType": "object", "refEntityId": "41ca4d69-933e-416d-b1ec-5f500a801a7a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "f2d39d8b-c086-4466-9845-a0dc27706f27", "name": "appointmentPageInfo", "title": "预约页面信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "f457b59a-9657-45b2-9cb7-346b69b22243", "name": "saleInfo", "title": "销售信息", "type": "java", "classType": "object", "refEntityId": "e37a3335-fefc-46d6-86a9-9e6762e61e5a", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "valid": true}, {"id": "f52a2235-6130-48b3-a3d0-67f061716a9a", "name": "productCategory", "title": "产品类别", "type": "java", "classType": "object", "refEntityId": "599add3c-de7c-4d9b-8d39-25c91d559904", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "db0f67be-41e7-4763-b834-849a309dcd4f", "style": "Select", "entityId": "871ce513-f672-435f-bea6-ff2066f37a54", "jsonSchemaData": {"items": [], "rules": [{"type": "required", "value": "true"}], "uniqueItems": false, "verifyRules": [], "jsonSchemaField": {"allOf": [], "anyOf": [], "items": [], "oneOf": [], "required": ["productCategory"], "properties": {"productCategory": {"$id": "f52a2235-6130-48b3-a3d0-67f061716a9a", "type": "standard", "allOf": [], "anyOf": [], "items": [], "oneOf": [], "title": "产品类别", "required": [], "properties": {}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "definitions": {}, "dependencies": {}, "patternProperties": {}, "additionalProperties": {}}}, "valid": true}]}