{"id": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "className": "com.open_care.survey.OCSurveyAnswer", "name": "OCSurveyAnswer", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "065f4c7b-0d2d-42d7-976a-836e0ab9c451", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "0f6066f8-c9fd-4b52-8391-6dbf5411113f", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "123c9206-4992-4ea0-8877-a2cc324f0a1b", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "1c6f919e-1a4b-4fc5-8abf-0e399607011c", "name": "answers", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "26817f2b-cee9-44ba-828b-c5f2a12104d4", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "2f67c127-81b2-4d70-871d-1a2b10b89368", "name": "customerId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "3711ec9d-556c-418c-b85f-6facebfcb438", "name": "userAgent", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "3a5dfcc4-f223-4241-ae5e-e0ce2a13e1d8", "name": "supplierId", "title": "供应商ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "3e6e758d-64cb-49bd-9a44-595eadf17493", "name": "score", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "4605e872-e7e0-41f2-84eb-8cbfb7dd8791", "name": "children", "type": "java", "classType": "object", "refEntityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "48e194dd-da53-4d0b-8377-44a8ca97a9bf", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "4c9a7f70-fb23-4768-9514-b14451380ea2", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "51edc1d3-27c2-4dbc-8c0f-081da68258bd", "name": "survey", "type": "java", "classType": "object", "refEntityId": "1a556ac0-e3b4-4b01-83d2-f9937b96f0ba", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "59c72bdc-e178-42e8-aa5c-7187b70850e8", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "5a375443-ae15-4e36-ba58-56e33f24491c", "name": "submitDate", "title": "提交时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "613605c5-4801-40dc-b111-b53ce09a904e", "name": "variableValue", "title": "问卷变量值id", "type": "java", "classType": "object", "refEntityId": "a9eb06e5-861b-4709-9a68-854bffd50716", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "6797614e-5d58-4514-8626-2a1e4b602817", "name": "nian", "title": "年份", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "85390252-7a0c-4b69-9f0f-c56408e6b124", "name": "questionAnswers", "type": "java", "classType": "object", "refEntityId": "aa19c574-38ec-4b36-bdb7-e243787dd9e9", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "87510ff7-b9bc-4cf7-b79e-2b41cc943164", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "9420922b-5a09-4a43-bb8c-2886a7318f2c", "name": "parent", "type": "java", "classType": "object", "refEntityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "96252043-906c-4641-8394-9efb3e66b037", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "9e305ef5-af7f-4a3b-ba80-45a19b6de232", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "9eec54db-9813-474f-9f3c-5b2165f45d7f", "name": "referer", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "a84a11bf-eff1-4fa7-8f5b-bedcf67afb3d", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "b5451006-e8ff-496a-857f-19a4b79e9aa0", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "b8b2a2b7-de22-404d-b404-6feea127622d", "name": "status", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "e2c81a55-5e64-492e-aa9c-1a537198f85b", "name": "ip", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}, {"id": "e514b81e-9a70-4da3-a1a7-a0ef936496b7", "name": "departmentId", "title": "科室ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "e5e4b5d8-5c0e-4a7f-b717-a635a0444fc4", "valid": true}]}