{"id": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "className": "com.open_care.medicalMicro.customer.queue.OCCustomerQueueRoomGroupInfo", "name": "OCCustomerQueueRoomGroupInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "medical-service", "remoteEntityName": "", "fields": [{"id": "094c1ffb-7211-4738-ac4a-73917cb5e742", "name": "queueCustomerExamProducts", "type": "java", "classType": "object", "refEntityId": "04339745-cc6e-41b7-985c-cb426c53da69", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "0aeeb319-9819-497f-ad29-d517eafb1e4c", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "0ba8dea7-8502-46a9-beb8-4ad3f196df35", "name": "departmentResourceOcId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "12cc85a0-109a-4545-8901-895208d93485", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "1bf802b3-2e08-4f9c-9565-1941c5b488d3", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "210b6538-0207-48c2-a857-2c8ebcc7508e", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "229e8ed5-9898-4715-b7d9-e34239d940ab", "name": "queuedUp", "title": "已完成排队", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "236faed2-bd43-4f99-a552-16cd9dedca86", "name": "completeQueueTime", "title": "完成排队时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "4164abf2-1f08-4725-90a0-3c67042c4a28", "name": "departmentId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "41f3ed09-497f-42d7-b27f-86ef927a0efe", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "5675522d-96dc-45fb-884d-b322875efbf2", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "572a9bf7-c72f-4412-a604-08cfca0f4b63", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "584f75fd-82d3-4de8-844e-3824d7be1ceb", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "5d35169f-da0c-47fc-8a65-53f3a6a6ff54", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "686e9df2-479a-46c1-a412-b01d4da7e47a", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "82b65ce5-f442-4979-b382-af70f0d61719", "name": "customerExamDepartmentId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "86642c06-339d-4185-9424-6b0c57719cf7", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "92d4e827-7935-4cf4-a072-6bca61b77d54", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "968a3fd0-a2c2-4a6f-8453-a7c4e1f4dd2a", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "9bfd0a3c-4e0b-4005-aaa3-5a60b9add937", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "a73cda07-3a15-4eb1-9b7e-20725bd45f79", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "ae2b1228-8f0e-4de3-86ad-a8469e7aef60", "name": "joinQueueTime", "title": "加入队列时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "b0d90c04-72de-4577-b99b-b4a306a9ef58", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "b98de65a-5d31-476d-932c-3e06f6d3bcdc", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "c26073a8-1d39-4102-835e-9875d92f9256", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "c3caa0b2-e0fd-41c7-836a-fee077179db4", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "cb5ebc00-a6d7-4f3b-bf33-f59504111cf8", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "cc6d216d-8f5b-40fc-bdba-e519a2eb9749", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "de167298-5285-488f-ae07-eacd0429ad63", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "e0c04980-0e9f-4ac5-92bb-a4ceb1f68d90", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "ed55e3ff-341f-4b0f-81d6-0eb118ecd684", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "f92deae0-fed9-4c55-89e3-7ce44c86629f", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}, {"id": "ff001f07-200e-4f91-8f02-d77f82c92605", "name": "baseQueueRoomGroupId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "03285d71-1bd0-4799-a034-b8b6d92c2bca", "valid": true}]}