{"id": "231a4781-2abb-419a-9079-467c06dde0cc", "className": "com.open_care.medicalMicro.customer.OCCustomerMedicalExamInfo", "name": "OCCustomerMedicalExamInfo", "standard": true, "authority": false, "server": "open-care-healthcare-crm-service", "valid": true, "title": "客户医疗信息", "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0296c60c-fab4-4126-ad47-069cbf7b6431", "name": "examOperationInfos", "title": "操作信息", "type": "java", "classType": "object", "refEntityId": "317a0c12-1874-46e7-bfa5-a32fd1b9e86a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "04ebf93f-5ea7-4cb7-97f6-807e125ec92f", "name": "queuedUp", "title": "已完成排队", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "185c94a1-c07d-4522-9237-68f38282caf8", "name": "joinQueueTime", "title": "加入队列时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "2ec80067-3d42-497c-acb4-d9ff441e6abc", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "2fb12467-791b-4bc9-bd5f-ec0c2f3446c4", "name": "expedited", "title": "加急", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "4685cc50-2674-4559-8369-12c048b64f21", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "4b89d64c-0537-4386-8c5c-79f094396667", "name": "partyOcId", "title": "客户ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "4defa71a-b7c0-4d80-ab53-e2102130563a", "name": "sex", "title": "性别", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "622aafb6-80c6-418d-8267-14d5614<PERSON><PERSON>f", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "6815c1b6-2ea7-4c79-b662-f664722c516a", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "6817ec17-c985-4ce2-8b88-ac1b3d49b404", "name": "serviceTime", "title": "服务时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "72176a23-952e-4065-80fb-97d53a833847", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "74e944da-dbd1-454b-b40b-3a134e42e1bb", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "793b07b8-02ca-4e86-ae5c-087fbc2bc2c9", "name": "examDepartments", "title": "科室信息", "type": "java", "classType": "object", "refEntityId": "1d5dfa8a-e757-40ee-84d0-1832fa0869bd", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "7ea16204-64ae-49e5-96e7-ef0244c3d187", "name": "ageUnit", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "7fa26113-cd7b-4b72-a198-40a90402539c", "name": "guideRecalled", "title": "导检单已回收", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "8932b234-208e-470a-8d09-d378b9586122", "name": "currentPosition", "title": "目前位置", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "8bcbc43b-ee75-4c09-bcdb-f4ff6d9c6b82", "name": "severeDegreeLevel", "title": "重症级别", "type": "java", "classType": "object", "refEntityId": "9de1cd00-db3e-4f81-81fd-5f47388180dc", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "229b91fa-1b4e-463c-a1b1-3461484a933d", "style": "Select", "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "8c43f60f-df7a-4629-b80f-e9372c83115d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "944e0d53-7207-40e9-b518-55b64fec9ece", "name": "guideRecalledTime", "title": "收单时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "98bf3fd5-0d8b-4cf4-9f85-093643c3b51a", "name": "serviceOrderCode", "title": "订单编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "99c3965a-7016-4f9b-a58e-c50c8b695f8d", "name": "completeQueueTime", "title": "完成排队时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "9db36f09-df66-4524-8bbc-784ea5a6a0af", "name": "status", "title": "状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "style": "Select", "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "a1b37a9f-0ef8-462e-9755-0e391b794aed", "name": "completedPreDinnerProducts", "title": "餐前项目已完成", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "a9639c89-57da-4f63-ac52-c8fcd6152cab", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "acad6359-646c-46ca-b1da-cbd3d0be91eb", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "b190f482-d2e9-4587-bffc-4c3672a70b03", "name": "Eaten", "title": "已就餐", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "b3c1758b-af1f-4ca6-b3c2-4fe58f6f86b9", "name": "physiologicalStatusOcId", "title": "生理期", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "bee0ca89-4ee2-4ecd-85a1-32b01ee7a660", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "c083a7ed-6be3-42d0-afea-3e1d494075f5", "name": "age", "title": "年龄", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "c73b5b42-29a1-4277-94e0-03380537e2a2", "name": "serviceOrgOcId", "title": "服务机构", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "d6c8354f-9303-4d2e-bf60-1cbbf9ace515", "name": "serviceCode", "title": "服务编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "d84df29d-7a0b-4867-a388-26e0872d19b3", "name": "guideRecalledOperator", "title": "收单人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "dbc1af3f-a101-4e8b-ad8b-83ce8f8d8a6e", "name": "examServiceType", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "df856d51-4565-4611-a667-0c2dc49513fa", "name": "customerCode", "title": "客户编码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "f1c7bce1-561d-4b7c-b35e-2c267136c5df", "name": "customerBaseExtraInfo", "type": "java", "classType": "object", "refEntityId": "8caa483c-19ee-44fc-a10c-1b78f7d2b1f4", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "f289915d-4fc1-4206-bcc7-fa3f659739c1", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "f3ba0842-0f7d-4bed-b0dc-cbd4bc9cc352", "name": "partyRoleOcId", "title": "客户身份角色Id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "fc657476-7db3-4758-83ae-21a9086e5ecb", "name": "birthday", "title": "生日", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}, {"id": "ff55072b-d930-4659-91e5-98d6f93b79f9", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "231a4781-2abb-419a-9079-467c06dde0cc", "valid": true}]}