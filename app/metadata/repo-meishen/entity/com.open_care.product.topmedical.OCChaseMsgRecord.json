{"id": "9631dbe8-29fc-4493-bc86-a506990bc475", "className": "com.open_care.product.topmedical.OCChaseMsgRecord", "name": "OCChaseMsgRecord", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "022091aa-ce4c-4220-93e5-c32363f7364b", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "03a555f0-677d-4514-8ab7-c304309c86a0", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "1ba420d3-45a2-47f8-84c2-ee7b49d7b89e", "name": "contactTime", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "2568652f-baeb-438b-8e36-53bd43154fba", "name": "role", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "2c5cf3f5-a01f-4943-a5df-b36dbb2d028e", "name": "name", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "3903b44d-dc32-42f7-9f58-009f1fc2db84", "name": "callStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "427e10e2-b235-41a4-ada0-d0fe0c0afbfa", "name": "mail", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "6ac5c518-92c7-4588-a94f-f6683d187f50", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "7ff7eaca-30e2-45eb-822d-9f2a80f124d4", "name": "phone", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "955ba45d-0ef1-48f7-b151-489b66de299d", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "970c8455-d3ab-4efe-9931-b314b0c51548", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "9a0702bc-617f-49f7-9115-22adff9576e8", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "a095978f-92a1-4090-871e-cabaead3ab82", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "adfe3ca4-2d90-4960-bd6b-e1c903b15fe1", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "b2f21d38-390a-4285-b57e-f4edcf16a420", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "be5c71ff-4e12-4444-bb9f-d2360bd6eaa9", "name": "chaseTask", "type": "java", "classType": "object", "refEntityId": "efc8c828-9ecf-4426-99aa-144927bb9f08", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "cb4b0bc7-8b62-4f7c-b7df-274885ba87f9", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "d0d4bc79-fae1-4b49-914d-2648180228f9", "name": "mailStatus", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "f2b865af-f20e-4fa0-8417-09c1515c8ca3", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}, {"id": "f83a15bb-3dfe-484b-af20-5090a9fcbfc4", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9631dbe8-29fc-4493-bc86-a506990bc475", "valid": true}]}