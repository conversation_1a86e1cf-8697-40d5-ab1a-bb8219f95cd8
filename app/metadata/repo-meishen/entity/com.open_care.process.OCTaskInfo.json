{"id": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "className": "com.open_care.process.OCTaskInfo", "name": "OCTaskInfo", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1186dfb1-f441-452e-9a90-5f37a4fd09ad", "name": "active", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "12032322-8285-4f54-917b-7f5424c6d0b3", "name": "lastUrgeTime", "title": "最后一次催办时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "162aed8e-771e-4644-99e5-5323fe571d5c", "name": "handleStatus", "title": "工单处理状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "1c2052ab-cca9-45d2-b362-b4bfa8236284", "name": "entityName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "363ab34e-04a6-4e65-b3d5-d2bd29c71552", "name": "createdByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "384bd831-26d1-46f2-9474-d0323047d8d9", "name": "assignee", "title": "任务受让人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "4078b01e-d43a-4cb4-a3f6-6fc44946938f", "name": "taskBusinessInfo", "type": "java", "classType": "object", "refEntityId": "6a6d8e6f-7a16-4579-9bad-9f4844dc66fd", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "46b9d52c-681a-4c85-92ba-a9825742b1db", "name": "priority", "title": "优先级", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "4714215c-0e30-495e-931f-c58542c0ca48", "name": "version", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "4c692d40-46b6-40cd-ae33-8befb37175e1", "name": "transferTime", "title": "转办时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "4d399fb3-e60c-4ad6-b05a-3c071c948d9c", "name": "transferUserName", "title": "转办人（转办的对象）名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "53c0f117-7fdb-44b1-a681-f9779621c898", "name": "transferInitiatorName", "title": "转办发起人名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "5530b582-41a7-487b-aa83-25b625b9f096", "name": "businessKey", "title": "流程实例编号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "6b889a67-7323-4239-822f-470bf54c1696", "name": "taskDefinitionKey", "title": "任务定义key", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "bed54d85a83f765c5e66b2f57708566a", "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "6bdb871d-4715-4fe4-88b0-db4871557fff", "name": "isCreated<PERSON>y<PERSON><PERSON><PERSON>", "title": "任务是否其他任务被退回创建的", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "751885fd-2fdd-4353-b9ad-0a5f241464cf", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "7c2ce5a6-36ed-4334-86c8-abdbdf2143b7", "name": "rollbackReasonTypeEnum", "title": "任务回退原因类型", "type": "java", "classType": "ProcessTaskRollbackReasonTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "655806bc84db574acd58c007b95ac6fe066e89a5", "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "85207f8b-f9f8-4ed6-9254-36c99880c1e6", "name": "processInfo", "title": "流程信息", "type": "java", "classType": "object", "refEntityId": "8877778e-704b-4df1-861c-1e4ed3a895b8", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "891a58a6-88d3-43f6-a612-e51544c8225b", "name": "rollbackReason", "title": "任务回退原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "89e470bf-859a-4458-9204-4c326e98f0c4", "name": "transferReason", "title": "转办原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "8b3bcdaa-6c61-4f1e-a997-0ec83e3d5628", "name": "appointmentTime", "title": "预约时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "96afb383-c1ad-4d67-9121-7db49288e196", "name": "transferred", "title": "是否转办", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "9ee7e2ff-a82d-4584-ad45-931278f1ad64", "name": "candidateUsers", "title": "任务候选人", "type": "java", "classType": "String", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "a0c8a6ab-4bd4-4f7d-bd23-411089f1e1e3", "name": "urgeInfoJson", "title": "催办信息", "type": "java", "classType": "object", "refEntityId": "d3d5deaf-5da2-43c5-982b-c502086e4f5a", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "a7bf6308-67a3-4b20-9e92-311f550149d5", "name": "isAssignmentTask", "title": "是否为分派任务", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "ac805a7a-8622-4475-890d-af668973543d", "name": "dynamicFieldData", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "af37ac41-1fa7-410c-9fb5-01bb3d4b0ba5", "name": "taskName", "title": "任务名称(一般都是 提交审核)", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "b883bc52-b30b-4333-b7c5-c8a4cac33ad0", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "babfe68a-61a4-4097-be0d-43ca774a4df4", "name": "attributes", "type": "java", "classType": "Map", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "c131af82-a910-4b27-bae0-b55a7108a3f2", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "cc8989b2-2f59-42e9-a5fc-04aa0d686ade", "name": "updatedByName", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "d39638df-de57-4359-96d3-14849a96917b", "name": "transferUser", "title": "转办人（转办的对象）", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "d5c24e5d-abcd-401d-a7b5-7409ff0c4c03", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "d6494202-2763-47b3-8fae-06d5d40c1390", "name": "taskId", "title": "任务id", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "e28b0272-f1c1-4d8a-b0a6-b7c67253061d", "name": "transferInitiator", "title": "转办发起人", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "ea2228cb-e4fb-4019-9043-882363d75abb", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "ead1d5e0-fcd5-4058-9da6-43d6b2aea6c6", "name": "taskStatus", "title": "任务状态", "type": "java", "classType": "TaskStatusEnum", "refEntityId": "9a815837-52c2-4449-9fdb-c0271fd3c337", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "0935cf914cabda2f7f0724bff2be2cd5c281be39", "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "ed1eaa5a-8c3f-4610-939c-9e1164b45cd8", "name": "taskTime", "title": "任务时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}, {"id": "f0d5bb96-d4eb-4854-81df-c2a4b5f4421b", "name": "taskType", "title": "任务类型", "type": "java", "classType": "ProcessTaskTypeEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "24cc2ccdfcb4c7f92bb645a767b1201937664ace", "entityId": "1b85f852-745f-4de3-b843-bf11a9ef3a68", "valid": true}]}