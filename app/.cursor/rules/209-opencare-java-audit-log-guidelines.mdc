---
description: 
globs: 
alwaysApply: false
---
# 审计日志配置

## 对字段开启审计

- 如果需要对一个普通字段开启审计，只需要在该字段上加上@Audited注解即可
- 如果需要在审计日志中记录字段是否被修改，可以在使用 @Audited(withModifiedFlag = true) 配置，这样会为每个被审计字段生成相应的修改标记

```java
@Data
public class OcCrmCustomer extends OCBase {

    @OcColumn(title = "用户ID")
    private String customerId;

    @Audited                            //不会记录字段是否被修改
    @OcColumn(title = "证件号")
    private String idNumber;

    @Audited(withModifiedFlag = true)   //会记录字段是否被修改
    @OcColumn(title = "用友编号")
    private String yongyouNumber;
}
```

- 对于扩展字段，若需要开启审计日志记录，需要在对应的构造函数处加一个true参数

```java
package com.open_care.app;
public class CustomerAnnotationContext extends SqlAnnotationProcessorContext {
    private Map<Class, List<OcFieldDTO>> extendFieldsResult;
    @Override
    public Map<Class, List<OcFieldDTO>> getExtendFields() {
        extendFieldsResult = new HashMap<>();
        
        // 其他扩展字段
        
        List<OcFieldDTO> serviceProductAppointmentDTOs = new ArrayList<>();
        serviceProductAppointmentDTOs.add(new OcFieldDTO("groupInstance", OCGroupInstance.class, "团次信息"));//团次信息不开启审计日志
        serviceProductAppointmentDTOs.add(new OcFieldDTO("customer", OCCustomer.class, "客户信息", true));	  //客户信息开启审计日志
        extendFieldsResult.put(OCServiceProductAppointment.class, serviceProductAppointmentDTOs);
        return extendFieldsResult;
    }
}
```

- 若存在一对多的实体关系，若需要开启审计日志，双方都需要开启审计日志


