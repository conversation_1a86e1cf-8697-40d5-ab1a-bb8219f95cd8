---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: *.json
alwaysApply: false
---
---
description: 
globs: *.json
alwaysApply: true
---

# OpenCare UI 交互与事件处理规范

<version>1.2.0</version>

## Context
- 当需要定义Open-Care平台UI组件之间的交互和事件处理时应用此规则
- 规范化事件定义、事件触发、事件响应和数据流转
- 确保组件间交互符合Open-Care平台的最佳实践

## Requirements

### 1. 基础概念

#### 1.1 全局对象
- OC: 全局对象，提供了丰富的API用于操作页面组件、处理数据和调用服务
- ocWindow: 当前窗口对象，提供了访问窗口内组件和上下文的方法
- self: 当前组件实例，可以直接调用组件的方法
- payload: 包含事件相关的数据，如表单提交的值、点击事件的参数等


### 2. 事件规范

事件必须通过`events`数组定义，每个事件对象包含以下属性：

- `eventName`: 事件名称，如"onCreate"、"onClick"、"onChange"等
- `actions`: 事件触发时执行的动作数组

```json
"events": [
  {
    "eventName": "onCreate",
    "actions": [
      // 动作定义
    ]
  }
]
```

#### 2. 标准事件类型

##### 2.2.1 生命周期事件
- `onCreate`: 组件创建时触发，常用于初始化数据、设置组件状态
- `onDestroy`: 组件销毁时触发，常用于清理资源、取消订阅

##### 2.2.2 用户交互事件
- `onClick`: 点击组件时触发，常用于按钮点击响应
- `onChange`: 组件值变化时触发，常用于输入框、选择器等
- `onBlur`: 组件失去焦点时触发，常用于输入验证
- `onFocus`: 组件获得焦点时触发，常用于显示提示信息
- `onSelect`: 选择组件选项时触发，常用于下拉选择、日期选择等

##### 2.2.3 表单事件
- `onSubmit`: 表单提交时触发，常用于数据验证和提交
- `onReset`: 表单重置时触发，常用于清除表单数据
- `onValidate`: 表单验证时触发，常用于自定义验证逻辑

##### 2.2.4 自定义事件
- 使用`OC.eventOn`和`OC.eventEmit`进行自定义事件的监听和触发
- 自定义事件名称应使用驼峰命名法，并包含模块名称，如`customerForm.save`

### 3. 动作规范

#### 3.1 动作定义
动作必须通过`actions`数组定义，每个动作对象包含：
- `actionName`: 动作名称
- 其他动作特定的属性

```json
"actions": [
  {
    "actionName": "jsAction",
    "scripts": "// JavaScript代码"
  }
]
```

#### 3.2 标准动作类型

##### 3.2.1 JavaScript动作
- `jsAction`: 执行JavaScript代码，用于复杂逻辑处理

```json
{
  "actionName": "jsAction",
  "scripts": "var form = ocWindow.getComponentByName('customerForm'); var data = form.getFieldsValue(); console.log(data);"
}
```

##### 3.2.2 数据操作动作
- `save`: 保存数据，用于表单提交

```json
{
  "actionName": "save",
  "body": {
    "dataSource": "customerForm"
  },
  "entityDefId": "customerEntity"
}
```

- `query`: 查询数据，用于表格数据加载

```json
{
  "actionName": "query",
  "target": "customerTable",
  "parameters": [
    {
      "name": "name",
      "value": {"ctxType": "component", "ctxValue": "nameInput.value"}
    }
  ]
}
```

- `delete`: 删除数据，用于数据删除操作

```json
{
  "actionName": "delete",
  "entityDefId": "customerEntity",
  "parameters": [
    {
      "name": "ocId",
      "value": {"ctxType": "row", "ctxValue": "id"}
    }
  ]
}
```

- `getOne`: 获取单条数据，用于表单编辑

```json
{
  "actionName": "getOne",
  "target": "customerForm",
  "parameters": [
    {
      "name": "ocId",
      "value": {"ctxType": "win", "ctxValue": "ocId"}
    }
  ],
  "entityDefId": "customerEntity"
}
```

##### 3.2.3 UI操作动作
- `openWindow`: 打开窗口，用于弹出表单、详情页等

```json
{
  "actionName": "openWindow",
  "windowId": "customerForm",
  "mode": "modal",
  "parameters": [
    {
      "name": "ocId",
      "value": {"ctxType": "row", "ctxValue": "id"}
    }
  ]
}
```

- `back`: 返回上一页或关闭当前窗口

```json
{
  "actionName": "back"
}
```

- `showMessage`: 显示消息提示

```json
{
  "actionName": "showMessage",
  "type": "success",
  "content": "操作成功"
}
```

##### 3.2.4 API调用动作
- `restApiCall`: 调用REST API

```json
{
  "actionName": "restApiCall",
  "method": "GET",
  "url": "/api/customer/list",
  "params": {
    "name": {"ctxType": "component", "ctxValue": "nameInput.value"}
  },
  "callback": "handleResponse"
}
```

### 4. JS 代码规范

#### 4.1 禁用特性
```javascript
// 禁止使用setTimeout
setTimeout(() => {}, 0);  // 错误
setImmediate(() => {});   // 正确

// 禁止使用eval和with
eval('var a = 1');       // 错误
with(obj) { ... }        // 错误

// 禁止使用==和!=
if (a == null) { }       // 错误
if (a === null) { }      // 正确
```

#### 4.2 代码格式
```javascript
// 必须使用分号结尾
const name = 'John';

// 必须使用花括号
if (condition) {         // 正确
    return;
}

if (condition) return;   // 错误

// 避免过长的代码行
const result = someVeryLongFunctionName() + anotherLongFunctionName() + 
    yetAnotherLongFunctionName();
```

#### 4.3 变量定义规范

##### 4.3.1 变量声明
```javascript
// 正确：每行声明一个变量
const firstName = 'John';
const lastName = 'Doe';
const age = 30;

// 错误：使用逗号声明多个变量
const firstName = 'John', lastName = 'Doe', age = 30;
```

#### 4.3.2 变量命名
```javascript
// 正确：使用驼峰命名法
const userName = 'John';
const calculateTotal = () => {};

// 错误：使用单字母或无意义的名称
const x = 'John';
const temp = calculateSomething();

// 错误：使用拼音
const yongHuMing = 'John';
```

#### 4.4. 代码格式规范

##### 4.4.1 缩进与空格
```javascript
// 使用2个空格缩进
function calculate() {
  const result = 1 + 2;
  if (result > 0) {
    return result;
  }
}

// 操作符两侧添加空格
const sum = a + b;
const name = firstName + ' ' + lastName;

// 括号内部不添加空格
const items = ['apple', 'banana'];
if (condition) { }
```

#### 4.5. 注释规范

##### 4.5.1 事件注释
```javascript
// Event发送 - 接收方：UserList窗口的onCreate事件
OC.eventEmit('Layout_UserList.refreshData', data);

// Event接收 - 处理用户列表刷新
OC.eventOn('Layout_UserList.refreshData', function(data) {
  // 处理逻辑
});
```

##### 4.5.2 方法注释
```javascript
// 计算用户的年龄段分布
// 参数：users - 用户数组
// 返回：年龄段分布对象 {young: number, middle: number, old: number}
function calculateAgeDistribution(users) {
  // 实现逻辑
}
```

#### 4.6 代码复用规范

##### 4.6.1 事件通信
```javascript
// 定义公共处理方法
function handleUserData(params) {
  if (!params || !params.data) return;
  // 处理逻辑
}

// 不同组件中复用处理逻辑
OC.eventOn('Layout_UserList.processData', handleUserData);
OC.eventOn('Layout_UserDetail.processData', handleUserData);
```

#### 4.7. 组件操作规范

##### 4.7.1 异步操作处理
```javascript
// 错误：直接获取设置后的值
component.setValue('newValue');
const value = component.getValue();  // 可能获取不到最新值

// 正确：在回调中获取值
component.setValue('newValue');
setImmediate(() => {
  const value = component.getValue();
  // 处理逻辑
});
```

##### 4.7.2 Event命名
```javascript
// 简单页面
OC.eventOn('Layout_UserManagement.queryTable', function() {})

// 复杂页面
OC.eventOn('Layout_UserCenter.order.updateHistoryOrders', function() {})

// 多次引用场景
OC.eventOn(`Layout_UserForm.${ocWindow.instanceId}.save`, function() {})
```

##### 4.7.3 日志打印规范
```javascript
// 普通信息
console.log('普通信息');

// 告警信息
console.warning('告警信息');

// 严重错误 - 谨慎使用，避免与平台错误混淆
console.error('严重错误');
```

#### 4.8 注释规范
```css
/* 全局样式注释 */
.className {
  color: red;
}
```

```javascript
// jsAction注释
function handleClick() {
  // 处理点击事件
}
```

#### 4.9 组件状态控制
```javascript
// 正确：提前计算状态值
const canCancel = data.auditStatus === 'DRAFT';
cancelBtn.setVisible(canCancel);

// 错误：直接在设置方法中判断
cancelBtn.setVisible(data.auditStatus === 'DRAFT'); // 避免这种写法
```

#### 4.10 多组件联动控制
```javascript
// 正确：使用事件机制处理多组件联动
OC.eventOn('form.statusChange', function(status) {
  // 统一处理所有相关组件的状态
  submitBtn.setVisible(status === 'DRAFT');
  editBtn.setEnabled(status === 'DRAFT');
  cancelBtn.setVisible(status === 'DRAFT');
});

// 错误：分散处理组件状态
if (status === 'DRAFT') {
  submitBtn.setVisible(true);
  editBtn.setEnabled(true);
  cancelBtn.setVisible(true);
}
```

#### 5. 函数编写规范

##### 5.1 单一职责原则
```javascript
// 正确：函数职责单一
function validateUserData(data) {
  // 只负责数据验证
  return isValidName(data.name) && isValidAge(data.age);
}

function processUserData(data) {
  // 只负责数据处理
  return {
    fullName: `${data.firstName} ${data.lastName}`,
    age: calculateAge(data.birthDate)
  };
}

// 错误：函数职责混杂
function handleUser(data) {
  // 既做验证又做处理，违反单一职责
  if (!data.name || !data.age) return false;
  data.fullName = `${data.firstName} ${data.lastName}`;
  return true;
}
```

##### 5.2 函数命名规范
```javascript
// 正确的函数命名
function calculateTotalAmount() { }
function validateUserInput() { }
function handleSubmitClick() { }

// 错误的函数命名
function calc() { }  // 名称不明确
function doSomething() { }  // 名称无意义
function fn1() { }  // 使用数字命名
```

#### 6. 参数校验规范

##### 6.1 类型检查
```javascript
function processData(params) {
  // 数组类型检查
  if (!Array.isArray(params.data)) {
    console.error('params.data must be an array');
    return;
  }

  // 对象类型检查
  if (typeof params.config !== 'object' || Array.isArray(params.config)) {
    console.error('params.config must be an object');
    return;
  }

  // 基本类型检查
  if (typeof params.name !== 'string') {
    console.error('params.name must be a string');
    return;
  }

  // 函数类型检查
  if (typeof params.callback !== 'function') {
    console.error('params.callback must be a function');
    return;
  }
}
```

##### 6.2 非空检查
```javascript
function validateParams(params) {
  // 数组非空检查
  if (!params.items || !params.items.length) {
    return false;
  }

  // 对象非空检查
  if (!params.config || !Object.keys(params.config).length) {
    return false;
  }

  // 数字类型特殊处理（考虑0值）
  if (typeof params.count !== 'number') {
    return false;
  }

  return true;
}
```

##### 6.3 合法性检查
###### 必填项验证
- **规则名称**：`required`
- **验证内容**：字段不能为空
- **验证规则**：
  ```javascript
  value !== undefined && value !== null && value !== ''
  ```
- **错误提示**：`此字段不能为空`

###### 字符串验证

###### 长度验证
- **规则名称**：`string.length`
- **验证参数**：
  - `min`: 最小长度
  - `max`: 最大长度
- **错误提示**：`长度必须在{min}-{max}个字符之间`

###### 格式验证
- **规则名称**：`string.format`
- **内置格式**：
  | 格式类型 | 正则表达式 | 说明 |
  |---------|------------|------|
  | email | `/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/` | 邮箱格式 |
  | mobile | `/^1[3-9]\d{9}$/` | 手机号码 |
  | telephone | `/^0\d{2,3}-?\d{7,8}$/` | 固定电话 |
  | idcard | `/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/` | 身份证号 |
  | url | `/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([\/\w .-]*)*\/?$/` | URL地址 |
  | zipcode | `/^\d{6}$/` | 邮政编码 |
  | number | `/^\d+$/` | 纯数字 |
  | chinese | `/^[\u4e00-\u9fa5]+$/` | 中文字符 |

###### 数值验证

###### 范围验证
- **规则名称**：`number.range`
- **验证参数**：
  - `min`: 最小值
  - `max`: 最大值
- **错误提示**：`数值必须在{min}-{max}之间`

###### 精度验证
- **规则名称**：`number.precision`
- **验证参数**：
  - `precision`: 小数位数
- **错误提示**：`最多支持{precision}位小数`

###### 日期验证

###### 日期范围
- **规则名称**：`date.range`
- **验证参数**：
  - `min`: 最小日期
  - `max`: 最大日期
- **错误提示**：`日期必须在{min}至{max}之间`

##### 接口参数验证

###### 参数类型验证
- **规则名称**：`paramTypes`
- **支持类型**：
  | 类型 | 验证方法 |
  |------|----------|
  | string | `typeof value === 'string'` |
  | number | `typeof value === 'number' && !isNaN(value)` |
  | boolean | `typeof value === 'boolean'` |
  | object | `typeof value === 'object' && value !== null` |
  | array | `Array.isArray(value)` |

###### 必填参数验证
- **规则名称**：`requiredParams`
- **验证内容**：检查必填参数是否存在且非空
- **错误提示**：`缺少必填参数: {参数名列表}`

##### 特殊参数验证

###### 实体名称验证
- **规则名称**：`entityName`
- **验证规则**：非空字符串
- **错误提示**：`实体名称格式不正确`

##### 验证规则使用

###### 1. 表单验证示例
```javascript
// 表单验证配置
const formRules = {
  username: [
    { type: 'required' },
    { type: 'string.length', min: 3, max: 20 }
  ],
  email: [
    { type: 'required' },
    { type: 'string.format', format: 'email' }
  ],
  age: [
    { type: 'number.range', min: 18, max: 100 }
  ]
};

// 验证调用
const validateForm = (formData) => {
  const validator = new CursorValidator(formRules);
  return validator.validate(formData);
};
```

###### 2. 接口参数验证示例
```javascript
// 接口参数验证配置
const apiRules = {
  required: ['entityName', 'data'],
  types: {
    entityName: 'string',
    data: 'object'
  }
};

// 验证调用
const validateApiParams = (params) => {
  const validator = new CursorApiValidator(apiRules);
  return validator.validate(params);
};
```

#### 7. 工具函数管理

##### 7.1 平台级工具函数
```javascript
// 应添加到平台JS SDK中
function formatDate(date, format) {
  // 日期格式化逻辑
}

function validateEmail(email) {
  // 邮箱验证逻辑
}
```

#### 8 基础API调用方法

##### 8.1 response结构说明

OpenCare平台API响应遵循统一的数据结构：

```javascript
{
  "status": "0",             // 字符串，状态码，"0"表示成功
  "msg": "操作成功",         // 字符串，响应消息
  "data": {}             // 数组或对象，响应数据主体
}
```

##### 8.2. 响应字段说明

###### status
- 类型：字符串
- 说明：状态码，"0"表示成功，"-1"表示失败
- 用法：用于判断请求状态和错误类型

###### msg
- 类型：字符串
- 说明：响应消息，成功或错误描述
- 用法：用于显示操作结果或错误提示

###### data
- 类型：对象
- 说明：响应数据主体

#### 8.3 restGetApiCall
通用GET方法调用REST API

```javascript
// 调用方式
OC.restGetApiCall(
  '/api/getUser', 
  {userId: '123'},
  function(response) {
    console.log('请求成功:', response);
  }, 
  function() {
    console.log('请求完成');
  },
  false  // 不显示loading
);

// 参数:
// - url: string 必须,REST API的url地址
// - uriVariables: any 必须,URL参数
// - responseHandler: function 必须,回调方法 function(response) {}
// - exHandler?: function 可选,执行完回调方法后的回调
// - showLoading?: boolean 可选,默认true,是否显示loading
// 返回: 无
```

#### 8.4 restPostApiCall
通用POST方法调用REST API

```javascript
// 调用方式
OC.restPostApiCall(
  '/api/save/{userId}', 
  {userId: '123'},
  {userName: 'Jack'},
  function(response) {
    console.log('保存成功:', response);
  }, 
  function() {
    console.log('保存完成');
  },
  false  // 不显示loading
);

// 参数:
// - url: string 必须,REST API的url地址
// - uriVariables: any 必须,URL参数
// - requestBody: any 必须,请求体数据
// - responseHandler: function 必须,回调方法 function(response) {}
// - exHandler?: function 可选,执行完回调方法后的回调
// - showLoading?: boolean 可选,默认true,是否显示loading
// 返回: 无
```

#### 8.5 $request
通用请求接口方法

```javascript
// 调用方式
OC.$request({
  url: '/api/get/entity',
  method: 'POST',
  body: {fieldSet: ''},
  params: {id: '123'}
}, function(response) {
  console.log('请求结果:', response);
});

// 参数:
// - config: object 必须,请求配置
//   - url: string 必须,请求地址
//   - method: string 必须,请求方法('GET'|'POST'|'PUT'|'DELETE')
//   - body?: any 请求体数据
//   - params?: object URL参数
// - responseHandler: function 必须,回调方法 function(response) {}
// 返回: 无
```

#### 8.6 GraphQL相关方法

##### execGraphQL
执行GraphQL查询

```javascript
// 调用方式
OC.execGraphQL(
  'query { user(id: "123") { name email } }',
  function(response) {
    console.log('查询结果:', response);
  },
  function() {
    console.log('查询完成');
  }
);

// 参数:
// - gqlBody: string 必须,GraphQL查询语句
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

#### 8.7 最佳实践

##### 错误处理
```javascript
// 1. 基本错误处理
OC.restGetApiCall(
  '/api/getData',
  {},
  function(response) {
    if (response.status !== '0') {
      OC.showMessageDlg('获取数据失败: ' + response.message, 'error');
      return;
    }
    // 处理成功响应
    console.log(response.data);
  },
  function() {
    // 请求完成后的清理工作
  }
);

// 2. 使用try-catch
try {
  OC.$request({
    url: '/api/saveData',
    method: 'POST',
    body: data
  }, function(response) {
    if (!response.success) {
      throw new Error(response.message);
    }
    OC.showMessageDlg('保存成功', 'success');
  });
} catch (error) {
  OC.showMessageDlg('操作失败: ' + error.message, 'error');
}
```

#### 表格查询方法定义

优先使用`OC.queryTable`方法进行标准化的表格查询：

##### queryTable API定义

```javascript
/**
 * 执行query操作，并将数据赋给指定的table组件
 * @param {object} tableIns 将被赋值的table实例
 * @param {array} filters 过滤条件，一般是searchForm中的过滤条件
 * @param {function} dataConverter 可选，对API返回的数据进行重新更改、赋值等操作
 * @param {function} responseHandler 可选，执行API以后的回调函数
 * @param {function} exHandler 可选，执行responseHandler后的回调函数
 */
OC.queryTable(tableIns, filters, dataConverter, responseHandler, exHandler);
```

##### 参数规范

###### 表格实例 (tableIns)
- 必须是对象类型
- 通常通过`ocWindow.getComponentByName('tableId')`获取
- 用于接收查询结果数据

###### 过滤条件 (filters)
- 必须是数组类型
- 通常从搜索表单中获取，如`OC.getSearchFormFilters(formIns)`
- 每个过滤条件包含字段名、字段值和操作符
- 标准格式：

```javascript
[
  {
    fieldName: "name",     // 字段名
    fieldValue: "张三",     // 字段值
    operator: "contains"   // 操作符
  },
  {
    fieldName: "age",
    fieldValue: 18,
    operator: ">="
  }
]
```

##### 数据转换器 (dataConverter)
- 可选参数，函数类型
- 用于对API返回的数据进行转换处理
- 接收API返回的数据作为参数，返回处理后的数据
- 标准格式：

```javascript
function dataConverter(responseData) {
  // 处理数据
  const { data } = responseData;
  const newData = data.map(item => {
    // 转换数据
    return item;
  });
  
  // 更新数据
  responseData.data = newData;
  return responseData;
}
```

##### 响应处理器 (responseHandler)
- 可选参数，函数类型
- 执行API后的回调函数
- 接收API返回的数据作为参数
- 标准格式：

```javascript
function responseHandler(responseData) {
  // 处理响应数据
  console.log('查询结果:', responseData);
}
```

##### 扩展处理器 (exHandler)
- 可选参数，函数类型
- 执行responseHandler后的回调函数
- 不接收参数
- 标准格式：

```javascript
function exHandler() {
  // 执行额外操作
  console.log('查询完成');
}
```

#### 最佳实践

##### 基本查询
```javascript
// 获取表单实例
const formIns = ocWindow.getComponentByName('searchForm');
// 获取过滤条件
const filters = OC.getSearchFormFilters(formIns);
// 获取表格实例
const tableIns = ocWindow.getComponentByName('table01');

// 执行查询
OC.queryTable(tableIns, filters, null, function(response) {
  console.log('查询成功:', response);
}, function() {
  console.log('查询完成');
});
```

##### 使用数据转换器
```javascript
// 获取表单和表格实例
const formIns = ocWindow.getComponentByName('searchForm');
const filters = OC.getSearchFormFilters(formIns);
const tableIns = ocWindow.getComponentByName('table01');

// 定义数据转换器
function dataConverter(responseData) {
  const { data } = responseData;
  const newData = data.map(function(record) {
    // 修改数据
    record.name = 'cover_' + record.name;
    return record;
  });
  responseData.data = newData;
  return responseData;
}

// 执行查询并转换数据
OC.queryTable(tableIns, filters, dataConverter, function(response) {
  console.log('查询并转换成功:', response);
}, function() {
  console.log('查询完成');
});
```

##### 处理查询响应
```javascript
// 获取表单和表格实例
const formIns = ocWindow.getComponentByName('searchForm');
const filters = OC.getSearchFormFilters(formIns);
const tableIns = ocWindow.getComponentByName('table01');

// 设置表格加载状态
tableIns.setLoading(true);

// 执行查询
OC.queryTable(tableIns, filters, null, function(response) {
  if (!response || !response.success) {
    OC.showMessageDlg(response?.message || '查询失败', 'error');
    return;
  }
  
  // 处理空数据情况
  if (response.data.length === 0) {
    OC.showMessageDlg('未查询到符合条件的数据', 'info');
  }
}, function() {
  // 查询完成后隐藏加载状态
  tableIns.setLoading(false);
});
```

#### 错误处理

##### 参数验证
```javascript
/**
 * 验证查询参数
 * @param {object} tableIns 表格实例
 * @param {array} filters 过滤条件
 * @returns {boolean} 是否有效
 */
function validateQueryParams(tableIns, filters) {
  // 验证表格实例
  if (!tableIns || typeof tableIns !== 'object') {
    console.error('表格实例无效');
    return false;
  }
  
  // 验证过滤条件
  if (!Array.isArray(filters)) {
    console.error('过滤条件必须是数组');
    return false;
  }
  
  return true;
}

// 使用验证
if (validateQueryParams(tableIns, filters)) {
  OC.queryTable(tableIns, filters, dataConverter, responseHandler, exHandler);
}
```

##### 异常处理
```javascript
try {
  OC.queryTable(tableIns, filters, function(data) {
    // 数据转换可能出错
    try {
      const processedData = processData(data);
      return processedData;
    } catch (error) {
      console.error('数据处理错误:', error);
      return data; // 返回原始数据
    }
  }, responseHandler, exHandler);
} catch (error) {
  console.error('查询执行错误:', error);
  tableIns.setLoading(false);
  OC.showMessageDlg('查询执行出错', 'error');
}
```

##### 响应错误处理
```javascript
/**
 * 处理查询错误
 * @param {Object} error 错误信息
 */
function handleQueryError(error) {
  console.error('查询出错:', error);
  
  let errorMsg = '查询失败';
  
  if (error) {
    if (error.message) {
      errorMsg = error.message;
    } else if (typeof error === 'string') {
      errorMsg = error;
    }
  }
  
  OC.showMessageDlg(errorMsg, 'error');
  
  // 重置表格状态
  tableIns.setLoading(false);
}
```


##### Loading状态管理
```javascript
// 1. 使用内置loading
OC.restPostApiCall(
  '/api/longOperation',
  {},
  data,
  function(response) {
    console.log('操作完成');
  },
  null,
  true  // 显示loading
);

// 2. 自定义loading
OC.showGlobalLoading();
try {
  await someAsyncOperation();
} finally {
  OC.hideGlobalLoading();
}
```

##### 批量请求处理
```javascript
// 串行请求
async function serialRequests() {
  const result1 = await new Promise(resolve => {
    OC.restGetApiCall('/api/data1', {}, resolve);
  });
  
  const result2 = await new Promise(resolve => {
    OC.restGetApiCall('/api/data2', {}, resolve);
  });
  
  return [result1, result2];
}

// 并行请求
async function parallelRequests() {
  const promises = [
    new Promise(resolve => {
      OC.restGetApiCall('/api/data1', {}, resolve);
    }),
    new Promise(resolve => {
      OC.restGetApiCall('/api/data2', {}, resolve);
    })
  ];
  
  return Promise.all(promises);
}
```

#### 5. JavaScript交互API

##### 系统上下文相关API

###### getSystemContexts
获取当前系统上下文相关数据

```javascript
// 调用方式
const systemContext = OC.getSystemContexts();

// 参数: 无
// 返回: JSON格式的系统上下文数据或null
```

###### getWindowContexts
获取当前window上下文相关数据

```javascript
// 调用方式
const windowContext = OC.getWindowContexts();

// 参数: 无
// 返回: JSON格式的window上下文数据或null
// 注意: 此方法在refWindow中无效,请使用ocWindow.getContexts()替代
```

###### setWindowContexts
设置当前window的上下文

```javascript
// 调用方式
const newCtxs = {ocId: '001'};
OC.setWindowContexts(newCtxs);

// 参数: 
// - contexts: object 必须,新的window上下文
// 返回: 无
// 注意: 仅用于topWindow,不要在refWindow中使用
```

##### 消息提示相关API

###### showNotificationDlg
弹出提示框

```javascript
// 调用方式
OC.showNotificationDlg('警告', 'XXX警告', 'warning', function() {
  console.log('callback function log');
});

// 参数:
// - title: string 提示框标题
// - content: string 提示内容  
// - confirmType: string 提示框类型('info'|'error'|'warning'|'success')
// - callback?: function 点击确定时的回调函数
// 返回: 无
```

###### showMessageDlg
弹出自动消失的提示框

```javascript
// 调用方式
OC.showMessageDlg('操作成功！', 'success');

// 参数:
// - content: string 提示内容
// - type: string 提示框类型('info'|'error'|'warning'|'success')
// 返回: 无
```

###### showConfirmDlg
通用confirm弹框

```javascript
// 调用方式
OC.showConfirmDlg(
  '标题',
  '内容',
  function() {
    console.log('点击确定按钮');
  },
  function() {
    console.log('点击取消按钮');
  }
);

// 参数:
// - title: string 弹框标题
// - content: string 弹框内容
// - onOk: function 确定按钮回调方法
// - onCancel?: function 取消按钮回调方法
// 返回: 无
```

#### 窗口操作相关API

#### showModalDialog
模态框方式打开window页面

```javascript
// 调用方式
OC.showModalDialog(
  'userInfoWindow', 
  {userId: '123'}, 
  null, 
  500,
  '自定义窗口名称',
  false,
  { modalHeight: '500px' }
);

// 参数:
// - windowName: string 必须,打开的window的componentName
// - windowCtx?: any 打开的window的上下文
// - targetHandlingAction?: any 触发该动作的action实例信息
// - modalWidth?: string|number 弹框宽度('20%'|200)
// - windowTitle?: string 弹窗名称
// - showCloseButton?: boolean 是否显示关闭按钮
// - others?: object 其他配置项
//   - showMask?: boolean 是否显示遮罩层
//   - titleCanDrag?: boolean 标题是否可拖拽
//   - canFullScreen?: boolean 是否可全屏
//   - modalHeight?: string 弹框高度
//   - topCanDragOutScreen?: boolean 是否允许拖出上边界
//   - rightCanDragOutScreen?: boolean 是否允许拖出右边界
//   - bottomCanDragOutScreen?: boolean 是否允许拖出下边界
//   - leftCanDragOutScreen?: boolean 是否允许拖出左边界
// 返回: 无
```

#### showDrawerDialog
抽屉方式打开window页面

```javascript
// 调用方式
OC.showDrawerDialog(
  'userInfoWindow',
  {userId: '123'},
  null,
  500,
  '自定义窗口名称',
  false
);

// 参数:
// - windowName: string 必须,打开的window的componentName
// - windowCtx?: any 打开的window的上下文
// - targetHandlingAction?: any 触发该动作的action实例信息
// - modalWidth?: string|number 抽屉宽度
// - windowTitle?: string 抽屉标题
// - showCloseButton?: boolean 是否显示关闭按钮
// 返回: 无
```

#### 数据操作相关API

##### save
通用保存接口

```javascript
// 调用方式
OC.save(
  "OcUser", 
  {name: "Jack", age: 23}, 
  function(response){
    console.log('保存成功', response);
  }, 
  function() {
    console.log('执行完成');
  }
);

// 参数:
// - entityName: string 必须,执行save对应的entityName，默认值：formIns.getEntityName()
// - data: object|null 必须,要保存的数据
// - responseHandler: function 必须,回调方法
// - exHandler?: function 执行完回调方法后的回调
// 返回: 无
```

#### Save接口响应结构
```javascript
{
  "status": "0",
  "data": {
    "data": {                // 保存的实体数据
      "id": "absdsdfwerwfsf", // 实体ID
      "name": "admin",       // 实体属性
      "username": "admin",   // 实体属性
      "contacts": [          // 一对多关系字段
        {
          "type": "1",
          "value": "11111",
          "_rowid": "abcdefg" // 行ID，用于标识数组中的元素
        }
      ]
    },
    "authority": {           // 权限信息
      "_canEdit": true,      // 整体是否可编辑
      "_canDelete": true,    // 整体是否可删除
      "name": {              // 字段级权限
        "_canEdit": true,    // 字段是否可编辑
        "_canAccess": true   // 字段是否可访问
      },
      "username": {
        "_canEdit": true,
        "_canAccess": true
      },
      "contacts": {          // 一对多关系字段权限
        "_canAdd": true,     // 是否可添加子项
        "_canEdit": true,    // 整体是否可编辑
        "_canAccess": true,  // 整体是否可访问
        "data": [            // 子项权限
          {
            "_canEdit": true,
            "_canDelete": true,
            "_rowid": "abcdefg", // 对应data中的_rowid
            "type": {
              "_canEdit": true,
              "_canAccess": true
            },
            "value": {
              "_canEdit": true,
              "_canAccess": true
            }
          }
        ]
      }
    }
  }
}
```


#### query
通用查询接口

```javascript
// 调用方式
OC.query(
  "OcUser",
  [{fieldName: "age", fieldValue: 23, operator: ">=" }],
  {current: 1, pageSize: 10},
  {age: "ascend"},
  {age: "maximum"},
  "{name,sex,ownorg#name}",
  function(response) {
    console.log('查询结果', response);
  },
  function() {
    console.log('执行完成');
  }
);

// 参数:
// - entityName: string 必须,执行query对应的entity
// - filters: array 必须,过滤条件
// - pagination: object 必须,分页信息
// - sorter: object|null 必须,排序信息
// - summary: object|null 必须,是否显示统计信息
// - fieldSet: string|null 必须,要查询的字段名称
// - responseHandler: function 必须,回调方法
// - exHandler?: function 执行完回调方法后的回调
// 返回: 无
```

### Query接口响应结构

```javascript
{
  "status": "0",
  "data": {
    "data": [                // 查询结果数组
      {
        "id": "111111",      // 实体ID
        "name": "admin",     // 实体属性
        "username": "admin", // 实体属性
        "_rowid": "weuirweuriowueriowueroiweu" // 行ID
      }
    ],
    "pagination": {          // 分页信息
      "current": 1,          // 当前页码
      "pageSize": 10,        // 每页记录数
      "total": 20            // 总记录数
    },
    "summary": {             // 聚合统计信息（可选）
      "amount": 10000,       // 数值类型字段的聚合值
      "userName": 20,        // 计数统计
      "price": "错误聚合函数：count1，应该为sum、max、min、avg或者count" // 错误信息
    },
    "authority": {           // 权限信息
      "_canAdd": true,       // 是否可添加记录
      "data": [              // 每条记录的权限
        {
          "_canEdit": true,  // 记录是否可编辑
          "_canDelete": true, // 记录是否可删除
          "_rowid": "weuirweuriowueriowueroiweu", // 对应data中的_rowid
          "name": {          // 字段级权限
            "_canEdit": true,
            "_canAccess": true
          },
          "username": {
            "_canEdit": true,
            "_canAccess": true
          }
        }
      ]
    }
  }
}
```

#### getOne API定义

```javascript
/**
 * 通用查询接口，获取1条数据
 * @param {string} entityName 要查询的entity
 * @param {string} entityInstId 要查询的entity实例的id
 * @param {string} fieldSet 要查询的字段
 * @param {function} responseHandler 回调方法
 * @param {function} exHandler 可选，执行完回调方法后的回调
 */
OC.getOne(entityName, entityInstId, fieldSet, responseHandler, exHandler);
```

##### 参数规范

#### 实体名称 (entityName)
- 必须是字符串类型
- 应与后端实体定义保持一致
- 示例：`"OcUser"`、`"Customer"`、`"Product"`

#### 实体实例ID (entityInstId)
- 必须是字符串类型
- 要查询的实体实例的唯一标识
- 示例：`"001"`、`"a1b2c3d4"`

#### 字段集合 (fieldSet)
- 必须是字符串类型
- 使用大括号包裹，逗号分隔的字段名列表
- 支持嵌套字段，使用#号连接
- 示例：`"{name, sex, age}"`、`"{name, department#name, role#roleName}"`

#### 响应处理器 (responseHandler)
- 必须是函数类型
- 执行API后的回调函数
- 接收API返回的数据作为参数
- 标准格式：

```javascript
function responseHandler(response) {
  // 处理响应数据
  if (response && response.status === '0') {
    const data = response.data?.data;
    // 处理获取的数据
  } else {
    // 处理错误
    const errorMsg = response?.message || '获取数据失败';
    OC.showMessageDlg(errorMsg, 'error');
  }
}
```

#### 扩展处理器 (exHandler)
- 可选参数，函数类型
- 执行responseHandler后的回调函数
- 不接收参数
- 标准格式：

```javascript
function exHandler() {
  // 执行额外操作
  console.log('获取数据完成');
}
```

#### Get接口响应结构

```javascript
{
  "status": "0",
  "data": {
    "data": {                // 实体数据
      "name": "admin",       // 实体属性
      "username": "admin",   // 实体属性
      "contacts": [          // 一对多关系字段
        {
          "type": "1",
          "value": "11111",
          "_rowid": "abcdefg" // 行ID
        }
      ],
      "ownOrg": {            // 一对一关系字段
        "orgName": "财务部",
        "location": "北京"
      }
    },
    "authority": {           // 权限信息
      "_canEdit": true,      // 整体是否可编辑
      "_canDelete": true,    // 整体是否可删除
      "name": {              // 字段级权限
        "_canEdit": true,
        "_canAccess": true
      },
      "username": {
        "_canEdit": true,
        "_canAccess": true
      },
      "contacts": {          // 一对多关系字段权限
        "_canAdd": true,     // 是否可添加子项
        "_canEdit": true,    // 整体是否可编辑
        "_canAccess": true,  // 整体是否可访问
        "data": [            // 子项权限
          {
            "_canEdit": true,
            "_canDelete": true,
            "_rowid": "abcdefg",
            "type": {
              "_canEdit": true,
              "_canAccess": true
            },
            "value": {
              "_canEdit": true,
              "_canAccess": true
            }
          }
        ]
      },
      "ownOrg": {            // 一对一关系字段权限
        "_canEdit": true,    // 字段是否可编辑
        "_canAccess": true,  // 字段是否可访问
        "data": {            // 关联实体权限
          "_canEdit": true,  // 实例是否可编辑
          "_canDelete": true, // 实例是否可删除
          "orgName": {
            "_canEdit": true,
            "_canAccess": true
          },
          "location": {
            "_canEdit": true,
            "_canAccess": true
          }
        }
      }
    }
  }
}
```

#### 最佳实践

##### 基本使用
```javascript
// 获取用户信息
OC.getOne(
  "OcUser",                // 实体名称
  "001",                   // 实体实例ID
  "{name, sex, age}",      // 字段集合
  function(response) {     // 响应处理器
    if (response && response.status === '0') {
      const userData = response.data?.data;
      // 处理用户数据
      console.log('用户数据:', userData);
    } else {
      // 处理错误
      OC.showMessageDlg(response?.message || '获取用户数据失败', 'error');
    }
  },
  function() {             // 扩展处理器
    console.log('获取用户数据完成');
  }
);
```

##### 表单数据加载
```javascript
/**
 * 加载表单数据
 * @param {string} entityId 实体ID
 * @param {string} formName 表单组件名称
 */
function loadFormData(entityId, formName) {
  // 获取表单实例
  const formIns = ocWindow.getComponentByName(formName);
  if (!formIns) {
    OC.showMessageDlg('未找到表单组件', 'error');
    return;
  }
  
  // 显示加载状态
  const loadingIns = ocWindow.getComponentByName('loadingIndicator');
  if (loadingIns) {
    loadingIns.setVisible(true);
  }
  
  // 获取实体数据
  OC.getOne(
    "Customer",                // 实体名称
    entityId,                  // 实体实例ID
    "{name, phone, email, address, status}", // 字段集合
    function(response) {
      if (response && response.status === '0') {
        const data = response.data?.data;
        const authority = response.data?.authority;
        
        if (data) {
          // 设置表单数据和权限
          OC.setFormValues(ocWindow, formName, {
            data: data,
            authority: authority
          });
          
          // 设置窗口标题
          ocWindow.setTitle('编辑客户: ' + data.name);
        } else {
          OC.showMessageDlg('未找到客户数据', 'warning');
        }
      } else {
        OC.showMessageDlg(response?.message || '获取客户数据失败', 'error');
      }
    },
    function() {
      // 隐藏加载状态
      if (loadingIns) {
        loadingIns.setVisible(false);
      }
    }
  );
}
```

##### 关联数据处理
```javascript
/**
 * 加载用户及其部门信息
 * @param {string} userId 用户ID
 */
function loadUserWithDepartment(userId) {
  OC.getOne(
    "OcUser",
    userId,
    "{name, email, phone, department#name, department#code, role#roleName}",
    function(response) {
      if (response && response.status === '0') {
        const userData = response.data?.data;
        
        if (userData) {
          // 处理用户基本信息
          const userInfoIns = ocWindow.getComponentByName('userInfo');
          if (userInfoIns) {
            userInfoIns.setValue(userData.name + ' (' + userData.email + ')');
          }
          
          // 处理部门信息
          const deptInfoIns = ocWindow.getComponentByName('deptInfo');
          if (deptInfoIns && userData.department) {
            deptInfoIns.setValue(userData.department.name + ' (' + userData.department.code + ')');
          }
          
          // 处理角色信息
          const roleInfoIns = ocWindow.getComponentByName('roleInfo');
          if (roleInfoIns && userData.role) {
            roleInfoIns.setValue(userData.role.roleName);
          }
        } else {
          OC.showMessageDlg('未找到用户数据', 'warning');
        }
      } else {
        OC.showMessageDlg(response?.message || '获取用户数据失败', 'error');
      }
    }
  );
}
```

#### 错误处理

##### 参数验证
```javascript
/**
 * 验证getOne参数
 * @param {string} entityName 实体名称
 * @param {string} entityInstId 实体实例ID
 * @param {string} fieldSet 字段集合
 * @returns {boolean} 是否有效
 */
function validateGetOneParams(entityName, entityInstId, fieldSet) {
  // 验证实体名称
  if (!entityName || typeof entityName !== 'string') {
    console.error('实体名称无效');
    return false;
  }
  
  // 验证实体实例ID
  if (!entityInstId || typeof entityInstId !== 'string') {
    console.error('实体实例ID无效');
    return false;
  }
  
  // 验证字段集合
  if (!fieldSet || typeof fieldSet !== 'string') {
    console.error('字段集合无效');
    return false;
  }
  
  // 验证字段集合格式
  if (!fieldSet.startsWith('{') || !fieldSet.endsWith('}')) {
    console.error('字段集合格式错误，应使用大括号包裹');
    return false;
  }
  
  return true;
}

// 使用验证
if (validateGetOneParams(entityName, entityInstId, fieldSet)) {
  OC.getOne(entityName, entityInstId, fieldSet, responseHandler, exHandler);
}
```

##### 响应错误处理
```javascript
/**
 * 处理getOne响应错误
 * @param {Object} response 响应数据
 * @returns {string|null} 错误信息，无错误时返回null
 */
function handleGetOneError(response) {
  if (!response) {
    return '未收到服务器响应';
  }
  
  if (response.status !== '0') {
    return response.message || '获取数据失败';
  }
  
  if (!response.data || !response.data.data) {
    return '返回数据格式错误';
  }
  
  return null;
}

// 使用错误处理
OC.getOne(
  "OcUser",
  "001",
  "{name, sex, age}",
  function(response) {
    const error = handleGetOneError(response);
    if (error) {
      OC.showMessageDlg(error, 'error');
      return;
    }
    
    // 处理正常数据
    const userData = response.data.data;
    // ...
  }
);
```

##### delete
通用删除接口

```javascript
// 调用方式
OC.delete(
  "OcUser",
  ["xxx"],
  function(response) {
    console.log('删除成功', response);
  },
  function() {
    console.log('执行完成');
  }
);

// 参数:
// - entityName: string 必须,要删除的entity
// - entityInstIds: array 必须,要删除的entity实例id数组
// - responseHandler: function 必须,回调方法
// - exHandler?: function 执行完回调方法后的回调
// 返回: 无
```

#### 工具类API

##### encryptByDES
DES加密

```javascript
// 调用方式
const encryptStr = OC.encryptByDES(message, secretKey);

// 参数:
// - message: string 要加密的字符串
// - secretKey: string 秘钥
// 返回: string 加密后的字符串
```

##### getUUID
获取UUID

```javascript
// 调用方式
const uuid = OC.getUUID();

// 参数: 无
// 返回: string UUID字符串
```

##### getAllParams
获取URL中的全部参数

```javascript
// 调用方式
// url: '/api/get/entity?name=Jack&sex=M'
const params = OC.getAllParams();
// 返回: {name: 'Jack', sex: 'M'}

// 参数: 无
// 返回: object URL参数对象
```

#### 消息通知
- `OC.showNotificationDlg(title, content, confirmType, callback)`: 显示通知对话框
  - title: 提示框的标题
  - content: 提示内容
  - confirmType: 提示框类型，可选值："info"|"error"|"warning"|"success"
  - callback: 点击确定时的回调函数（可选）
- `OC.showMessageDlg(content, type)`: 显示消息提示，自动消失，无按钮
  - content: 提示内容
  - type: 提示框类型，可选值："info"|"error"|"warning"|"success"
- `OC.showConfirmDlg(title, content, onOk, onCancel)`: 显示确认对话框
  - title: 弹框标题（必须）
  - content: 弹框内容（必须）
  - onOk: 确定按钮回调方法（必须）
  - onCancel: 取消按钮回调方法（可选）

#### 窗口操作
- `OC.showModalDialog(windowName, windowCtx, targetHandlingAction, modalWidth, windowTitle, showCloseButton, others)`: 显示模态窗口
  - windowName: 打开的window的componentName（必须）
  - windowCtx: 打开的window的上下文（可选）
  - targetHandlingAction: 触发该动作的action实例信息（可选，需要占位时使用null）
  - modalWidth: 弹框宽度，如'20%'或200（可选，需要占位时使用null）
  - windowTitle: 弹窗名称，不配置则显示引用窗口的title（可选，需要占位时使用null）
  - showCloseButton: 是否显示右上角关闭按钮（可选，默认显示）
  - others: 其他配置项（可选），支持：
    - showMask: 是否显示遮罩层（默认true）
    - titleCanDrag: 标题是否可拖拽（默认false）
    - canFullScreen: 是否可全屏（默认false）
    - modalHeight: 弹框高度，如'100px'或'80%'
    - topCanDragOutScreen: 拖拽时是否允许拖出上边界（默认false）
    - rightCanDragOutScreen: 拖拽时是否允许拖出右边界（默认true）
    - bottomCanDragOutScreen: 拖拽时是否允许拖出下边界（默认true）
    - leftCanDragOutScreen: 拖拽时是否允许拖出左边界（默认true）
- `OC.showDrawerDialog(windowName, windowCtx, targetHandlingAction, modalWidth, windowTitle, showCloseButton)`: 显示抽屉式窗口
  - windowName: 打开的window的componentName（必须）
  - windowCtx: 打开的window的上下文（可选）
  - targetHandlingAction: 触发该动作的action实例信息（可选，需要占位时使用null）
  - modalWidth: 抽屉宽度，如'20%'或200（可选）
  - windowTitle: 抽屉标题，不配置则显示引用窗口的title（可选，需要占位时使用null）
  - showCloseButton: 是否显示右上角关闭按钮（可选，默认显示）
- `OC.closeModal(ocWindow)`: 关闭当前模态框
  - ocWindow: 当前模态框所在的window实例
- `OC.closeDrawer(ocWindow)`: 关闭当前抽屉
  - ocWindow: 当前抽屉所在的window实例

#### 数据操作
- `OC.save(entityName, data, responseHandler, exHandler)`: 保存数据
  - entityName: 执行save对应的entityName（必须）
  - data: json格式的数据或null（必须）
  - responseHandler: 回调方法 function(response) {}（必须）
  - exHandler: 执行完回调方法后的回调 function() {}（可选）
- `OC.query(entityName, filters, pagination, sorter, summary, fieldSet, responseHandler, exHandler)`: 查询数据
  - entityName: 执行query对应的entity（必须）
  - filters: 过滤条件数组（必须）
  - pagination: 分页信息，json格式（必须）
  - sorter: 排序信息，json格式或null（必须）
  - summary: 是否显示统计信息，json格式或null（必须）
  - fieldSet: 要查询的字段名称，string或null（必须）
  - responseHandler: 回调方法 function(response) {}（必须）
  - exHandler: 执行完回调方法后的回调 function() {}（可选）
- `OC.delete(entityName, entityInstIds, responseHandler, exHandler)`: 删除数据
  - entityName: 要删除的entity（必须）
  - entityInstIds: 要删除的entity实例id数组（必须）
  - responseHandler: 回调方法 function(response) {}（必须）
  - exHandler: 执行完回调方法后的回调 function() {}（可选）
- `OC.getOne(entityName, entityInstId, fieldSet, responseHandler, exHandler)`: 获取单条数据
  - entityName: 要查询的entity（必须）
  - entityInstId: 要查询的entity实例id（必须）
  - fieldSet: 要查询的字段（必须）
  - responseHandler: 回调方法 function(response) {}（必须）
  - exHandler: 执行完回调方法后的回调 function() {}（可选）

#### 组件操作
- `ocWindow.getComponentByName(name)`: 获取组件实例
- `component.setValue(value)`: 设置组件值
- `component.getValue()`: 获取组件值
- `component.setVisible(visible)`: 设置组件可见性
- `component.setEditable(editable)`: 设置组件可编辑性

### 基础操作API

#### getComponentByName
通过componentName获取组件实例

```javascript
// 调用方式
const componentInstance = ocWindow.getComponentByName('componentName');

// 参数:
// - componentName: string 必须,组件的唯一标识名
// 返回: 组件实例对象或null
```

#### setWindowModifiedStatus
设置当前window中数据的修改状态

```javascript
// 调用方式
ocWindow.setWindowModifiedStatus(true);

// 参数:
// - status: boolean 必须,是否已修改
// 返回: 无
// 说明: 若为true,离开页面会提示是否保存数据
```

#### goBack
执行返回上一页操作

```javascript
// 调用方式
ocWindow.goBack();

// 参数: 无
// 返回: 无
// 说明: 如果当前window是弹框/drawer,则执行关闭操作
```

#### getContexts
获取当前window上下文数据

```javascript
// 调用方式
const windowContext = ocWindow.getContexts();

// 参数: 无
// 返回: JSON格式的window上下文数据或null
// 说明: 此方法可替代OC.getWindowContexts
```

#### close
关闭指定的窗口

```javascript
// 调用方式
ocWindow.close();

// 参数: 无
// 返回: 无
// 说明: 适用于弹框/drawer的关闭
```

### 问卷相关API

#### showPathComponentIdFromRootToCurrent
获取指定组件的ComponentId路径

```javascript
// 调用方式
const componentIds = ocWindow.showPathComponentIdFromRootToCurrent(targetComponentId);

// 参数:
// - targetComponentId: string 必须,指定组件的componentId
// 返回: string[] componentId组成的数组
```

#### showQuestion
显示指定questionId的组件

```javascript
// 调用方式
ocWindow.showQuestion(questionId);

// 参数:
// - questionId: string 必须,指定组件的questionId
// 返回: 无
```

#### hideQuestion
隐藏指定questionId的组件

```javascript
// 调用方式
ocWindow.hideQuestion(questionId);

// 参数:
// - questionId: string 必须,指定组件的questionId
// 返回: 无
```

#### setQuestionVisible
设置指定questionId的组件是否显示

```javascript
// 调用方式
ocWindow.setQuestionVisible(questionId, true);

// 参数:
// - questionId: string 必须,指定组件的questionId
// - visible: boolean 必须,是否显示
// 返回: 无
```

#### getQuestionVisible
获取指定questionId的组件是否显示

```javascript
// 调用方式
const isVisible = ocWindow.getQuestionVisible(questionId);

// 参数:
// - questionId: string 必须,指定组件的questionId
// 返回: boolean 是否显示
```

#### gotoQuestion
跳转到指定targetQuestionId的组件

```javascript
// 调用方式
ocWindow.gotoQuestion(targetQuestionId);

// 参数:
// - targetQuestionId: string 必须,指定组件的questionId
// 返回: 无
```

#### 最佳实践

##### Window生命周期管理
```javascript
// 1. 创建window时设置上下文
const windowContext = ocWindow.getContexts();

// 2. 数据修改时设置状态
ocWindow.setWindowModifiedStatus(true);

// 3. 关闭window前检查数据
if(hasModified) {
  ocWindow.showConfirmDlg('提示', '是否保存修改?', 
    () => {
      // 保存数据
      ocWindow.close();
    },
    () => {
      ocWindow.close();
    }
  );
}
```

##### 组件获取与操作
```javascript
// 1. 获取组件实例
const form = ocWindow.getComponentByName('userForm');
if(!form) {
  console.error('Form not found');
  return;
}

// 2. 操作组件
form.setFieldsValue({
  name: 'test',
  age: 18
});
```

### 组件实例相关方法
#### 1. 基础方法

##### getComponentType
获取当前组件的componentType

```javascript
// 调用方式
const currentComponentType = componentInstance.getComponentType(); 

// 参数: 无
// 返回: string 组件类型
```

##### disable
将组件设置为不可用状态

```javascript
// 调用方式
componentInstance.disable();

// 参数: 无
// 返回: 无
// 注意: 仅对formItem组件生效
```

##### enable
将组件设置为可用状态

```javascript
// 调用方式
componentInstance.enable();

// 参数: 无
// 返回: 无
// 注意: 仅对formItem组件生效
```

##### setVisible
显示/隐藏组件

```javascript
// 调用方式
componentInstance.setVisible(false);  // 隐藏

// 参数:
// - visible: boolean 是否显示
// 返回: 无
```

##### isVisible
获取组件的显示状态

```javascript
// 调用方式
const currentComponentIsVisible = componentInstance.isVisible();

// 参数: 无
// 返回: boolean 是否显示
```

#### 样式相关方法

##### getStyle
获取组件的css配置信息

```javascript
// 调用方式
const currentComponentStyle = componentInstance.getStyle();

// 参数: 无
// 返回: JSON格式的css数据
```

##### setStyle
设置组件的css配置信息

```javascript
// 调用方式
const styles = '{"color":"red", "background-color":"blue"}';
componentInstance.setStyle(styles);

// 参数:
// - styles: string JSON格式的css字符串
// 返回: 无
```

#### 值操作相关方法

##### getValue
获取组件的value值

```javascript
// 调用方式
const value = componentInstance.getValue();

// 参数: 无
// 返回值类型根据组件类型不同而不同:
// Form: JSON object {name: "Jack", age: 23,...}
// Calendar: Array [{id: "123", ...}...]
// Table: Array [{id: "123", ...}...]
// CheckboxGroup: Array ['string1', 'string2', ...]
// DatePicker: String '2019-01-18'
// RangePicker: Array ['2018-12-01', '2018-12-31']
// Input: String 'hello'
// Radio: String|Number|Boolean 'string'|123|true
// RichEditor: String '<p>hello</p>'
// Select: String|Number|Boolean|Array 'string'|123|true|['string1', 'string2', ...]
// TextArea: String 'hello'
// Upload: String|Array 'url'|['url1', 'url2', ...]
// TimePicker: String '14:15:16'
// Checkbox: Boolean true|false
// TableForm: Array [{id: "123", ...}...]
// SubForm: Array|JSON [{name: "Jack", age: 23, ...}]|{name: "Jack", age: 23...}
// ColorPicker: String 'rgba(1, 2, 2, 1)'
// SearchInput: String 'Jack'
```

#### setValue
设置组件的value值

```javascript
// 调用方式
componentInstance.setValue('string');

// 参数:
// - value: any 根据组件类型不同而不同:
// DatePicker: String '2019-01-18'
// RangePicker: Array ['2018-12-01', '2018-12-31']
// Input: String 'hello'
// Select: String|Number|Boolean|Array 'string'|123|true|['string1', 'string2', ...]
// TextArea: String 'hello'
// Upload: String|Array 'url'|['url1', 'url2', ...]
// TimePicker: String '14:15:16'
// Checkbox: Boolean true|false
// Cascader: Array ['01', '0101', '010103']
// SubForm: Array|JSON [{name:'Jack','_rowid':'xxx'}]|{name:'Jack', age:23}
// ColorPicker: String '#333'|'rgb(123, 2123, 220)'
// SearchInput: String 'Jack'
// 返回: 无
```

#### 最佳实践

##### 组件类型判断
```javascript
// 获取组件类型后再进行相应操作
const type = componentInstance.getComponentType();
if(type === 'Form') {
  // Form组件相关操作
} else if(type === 'Table') {
  // Table组件相关操作
}
```

##### 组件状态管理
```javascript
// 根据条件控制组件状态
if(hasPermission) {
  componentInstance.enable();
} else {
  componentInstance.disable();
}

// 显示/隐藏组件
componentInstance.setVisible(shouldShow);
```

##### 值操作
```javascript
// 获取组件值
const value = componentInstance.getValue();
if(value) {
  // 处理值
}

// 设置组件值
try {
  componentInstance.setValue(newValue);
} catch(e) {
  console.error('设置值失败:', e);
}
```

#### Form组件方法

##### getFieldsValue
获取Form组件的value值

```javascript
// 调用方式
const formValues = formIns.getFieldsValue();

// 参数: 无
// 返回: Json格式的value数据
```

#### validateFields
校验当前Form所有组件

```javascript
// 调用方式
formIns.validateFields(function(errors, values) {
  if(errors) {
    return;
  }
  console.log(values);
});

// 参数:
// - callback: function(errors, values) 回调函数
//   - errors: 错误信息,没有错误为false
//   - values: 当前Form的value值
// 返回: 无
```

##### resetFields
重置当前Form所有组件的值到defaultValue

```javascript
// 调用方式
formIns.resetFields();

// 参数: 无
// 返回: 无
// 说明: 如果没有defaultValue则重置为空
```

##### clean
清空当前Form所有组件的值

```javascript
// 调用方式
formIns.clean();

// 参数: 无
// 返回: 无
// 说明: defaultValue也会被清除
```

##### setFieldsValue
给当前Form组件赋值

```javascript
// 调用方式
const values = {name: 'Jack', sex: 'M'};
formIns.setFieldsValue(values);

// 参数:
// - values: object 必须,要设置的值
// 返回: 无
```

##### getFieldSet
返回当前form需要展示的字段名称集合

```javascript
// 调用方式
const fieldSet = formIns.getFieldSet();
// 返回值示例: "{name,sex,birthday,role:{id,roleName}}"

// 参数: 无
// 返回: string 字段集合字符串
```

##### getAuthority
返回当前form的权限信息

```javascript
// 调用方式
const authority = formIns.getAuthority();

// 参数: 无
// 返回: object 权限信息对象
// 返回示例:
/*
{
  "_canEdit": true,
  "_canDelete": true,
  "sex": {
    "_canEdit": false,
    "_canAccess": false
  },
  "birthday": {
    "_canEdit": false,
    "_canAccess": true
  }
}
*/
```

#### FormItem共有方法

##### setFormItemLabel
设置FormItem的label

```javascript
// 调用方式
formItem.setFormItemLabel('testSetLabel');

// 参数:
// - label: string 必须,要设置的label
// 返回: 无
```

##### setEditable
设置FormItem是否可编辑

```javascript
// 调用方式
formItem.setEditable(true);

// 参数:
// - editable: boolean 必须,是否可编辑
// 返回: 无
```

##### setReadOnlyStyle
设置只读时的显示类型

```javascript
// 调用方式
formItem.setReadOnlyStyle('label');

// 参数:
// - readonlyComponentStyle: string 必须,显示类型('BasedOnForm'|'label'|'disable')
// 返回: 无
```

#### Button组件方法

##### enableLoadingState
将按钮设置为loading状态

```javascript
// 调用方式
btnIns.enableLoadingState();

// 参数: 无
// 返回: 无
```

##### disableLoadingState
将按钮设置为非loading状态

```javascript
// 调用方式
btnIns.disableLoadingState();

// 参数: 无
// 返回: 无
```

##### setTitle
设置按钮显示文本

```javascript
// 调用方式
btnIns.setTitle('提交中...');

// 参数:
// - title: string 必须,按钮文本
// 返回: 无
```

##### setBadgeContent
设置按钮badge显示内容

```javascript
// 调用方式
btnIns.setBadgeContent(10);

// 参数:
// - content: number|string 必须,badge内容
// 返回: 无
// 注意: 按钮需配置显示badge才生效
```

##### setType
设置按钮类型

```javascript
// 调用方式
btnIns.setType('link');

// 参数:
// - type: string 必须,按钮类型('default'|'primary'|'ghost'|'warning'|'icon'|'text'|'link')
// 返回: 无
```

#### CheckboxGroup组件方法

##### selectAll
全选所有选项

```javascript
// 调用方式
checkboxgroupIns.selectAll();

// 参数: 无
// 返回: 无
```

##### deSelectAll
取消全部选择

```javascript
// 调用方式
checkboxgroupIns.deSelectAll();

// 参数: 无
// 返回: 无
```

#### 最佳实践

##### Form操作
```javascript
// 1. 表单验证
formIns.validateFields((errors, values) => {
  if(errors) {
    console.error('表单验证失败:', errors);
    return;
  }
  // 处理表单数据
  console.log('表单数据:', values);
});

// 2. 表单赋值
formIns.setFieldsValue({
  name: 'Jack',
  age: 25,
  department: 'IT'
});

// 3. 表单重置
formIns.resetFields();
```

##### Button操作
```javascript
// 1. 异步操作按钮状态管理
btnIns.enableLoadingState();
btnIns.setTitle('提交中...');

try {
  await submitData();
  btnIns.setTitle('提交成功');
} catch(e) {
  btnIns.setTitle('提交失败');
} finally {
  btnIns.disableLoadingState();
}

// 2. 动态改变按钮类型
if(isDisabled) {
  btnIns.setType('ghost');
} else {
  btnIns.setType('primary');
}
```

##### CheckboxGroup操作
```javascript
// 根据条件全选/取消全选
if(shouldSelectAll) {
  checkboxgroupIns.selectAll();
} else {
  checkboxgroupIns.deSelectAll();
}
```

#### Label组件方法

##### getContent
获取content的值

```javascript
// 调用方式
const labelContentString = labelIns.getContent();

// 参数: 无
// 返回: String 标签内容
```

##### setValue
设置content的值

```javascript
// 调用方式
labelIns.setValue('新标签内容');

// 参数:
// - value: string 必须,要设置的内容
// 返回: 无
```

#### Radio组件方法

##### chooseRadio
设置RadioGroup的选中项

```javascript
// 调用方式
radioIns.chooseRadio(true);

// 参数:
// - radioValue: String|Boolean|Number 必须,要选中的值
// 返回: 无
```

#### Select组件方法

##### setValueOfFirstOption
选中第一个选项

```javascript
// 调用方式
selectIns.setValueOfFirstOption();

// 参数: 无
// 返回: 无
```

##### refreshOptions
刷新选项数据

```javascript
// 调用方式
selectIns.refreshOptions();

// 参数: 无
// 返回: 无
```

##### selectedAll
选中所有选项

```javascript
// 调用方式
selectIns.selectedAll();

// 参数: 无
// 返回: 无
```

#### Select/Radio/CheckboxGroup共有方法

##### setOptions
设置选项

```javascript
// 调用方式
const options = [{label: '老师', value: 'teacher'}, {label: '学生', value: 'student'}];
componentIns.setOptions(options);

// 参数:
// - options: Array<{label: string, value: string|number|boolean}> 必须
// 返回: 无
```

##### getOptions
获取选项

```javascript
// 调用方式
const options = componentIns.getOptions();

// 参数: 无
// 返回: Array<{label: string, value: string|number|boolean}>
```

##### removeOption
删除一个选项

```javascript
// 调用方式
componentIns.removeOption('001');

// 参数:
// - optionKey: string 必须,要删除的option的value值
// 返回: 无
```

##### removeOptions
删除多个选项

```javascript
// 调用方式
componentIns.removeOptions(['001', '002']);

// 参数:
// - optionKeys: string[] 必须,要删除的options的value值数组
// 返回: 无
```

##### addOption
添加一个选项

```javascript
// 调用方式
componentIns.addOption('001', '经理');

// 参数:
// - optionKey: string 必须,要增加的option的value值
// - optionLabel: string 必须,要增加的option的label值
// 返回: 无
```

##### addOptions
添加多个选项

```javascript
// 调用方式
const newOptions = [
  {label: 'newLabel1', value: 'newValue1'},
  {label: 'newLabel2', value: 'newValue2'}
];
componentIns.addOptions(newOptions);

// 参数:
// - options: Array<{label: string, value: string|number|boolean}> 必须
// 返回: 无
```

##### getSelectedOption
获取选中项的option(仅适用于单选)

```javascript
// 调用方式
const selectedOption = componentIns.getSelectedOption();

// 参数: 无
// 返回: {label: string, value: string|number|boolean}
```

##### getSelectedLabel
获取选中项的label(仅适用于单选)

```javascript
// 调用方式
const labelStr = componentIns.getSelectedLabel();

// 参数: 无
// 返回: string
```

#### 最佳实践

##### Label操作
```javascript
// 动态更新标签内容
const content = labelIns.getContent();
if(needUpdate) {
  labelIns.setValue(`${content}(已更新)`);
}
```

##### Radio操作
```javascript
// 根据条件选择Radio
if(userType === 'teacher') {
  radioIns.chooseRadio('teacher');
}
```

##### Select操作
```javascript
// 1. 动态更新选项
const newOptions = await fetchOptions();
selectIns.setOptions(newOptions);

// 2. 获取选中值
const selectedOption = selectIns.getSelectedOption();
if(selectedOption) {
  console.log('选中值:', selectedOption.value);
  console.log('选中文本:', selectedOption.label);
}

// 3. 添加/删除选项
selectIns.addOption('003', '新选项');
selectIns.removeOption('001');
```

#### Table组件方法

##### getPagination
获取分页信息

```javascript
// 调用方式
const pagination = tableIns.getPagination();

// 参数: 无
// 返回: object
// 返回示例:
/*
{
    "current": 1,                 
    "pageSize": 10,              
    "pageSizeOptions": [10, 20, 30, 40],
    "position": "bottom",
    "showQuickJumper": true,
    "showSizeChanger": false
}
*/
```

##### expandRows/collapseRows
展开/折叠树形结构的所有节点

```javascript
// 调用方式
tableIns.expandRows();   // 展开所有
tableIns.collapseRows(); // 折叠所有

// 参数: 无
// 返回: 无
```

##### getTableHeaderForExport
获取导出的字段明细

```javascript
// 调用方式
const tableHeaderForExport = tableIns.getTableHeaderForExport();

// 参数: 无
// 返回: Array
// 返回示例:
/*
[
    {
        fieldName: "name",
        title: "姓名"
    },
    {
        fieldName: "sex",
        title: "性别"
    }
]
*/
```

##### setOperatorColumnVisible
设置操作列是否显示

```javascript
// 调用方式
tableIns.setOperatorColumnVisible(true); // 显示操作列
tableIns.setOperatorColumnVisible(false); // 隐藏操作列

// 参数:
// - visible: boolean 必须,是否显示
// 返回: 无
```

##### setRowSelectionType
设置表格行的可选择类型

```javascript
// 调用方式
tableIns.setRowSelectionType('checkbox'); // 可多选
tableIns.setRowSelectionType('radio');    // 可单选
tableIns.setRowSelectionType(null);       // 不可选

// 参数:
// - type: string 必须,选择类型('checkbox'|'radio'|null)
// 返回: 无
```

##### update
更新当前table的数据

```javascript
// 调用方式
tableIns.update();

// 参数: 无
// 返回: 无
// 说明: 会重新请求接口获取数据
```

##### setLoading
设置table加载状态

```javascript
// 调用方式
tableIns.setLoading(true);  // 显示loading
tableIns.setLoading(false); // 隐藏loading

// 参数:
// - loading: boolean 必须,是否显示loading
// 返回: 无
```

##### staticAddRow/staticDeleteRow/staticUpdateRow
静态操作表格数据

```javascript
// 调用方式
// 添加行
const data = {name: 'Jack', _rowid: '123'};
tableIns.staticAddRow(data);

// 删除行
tableIns.staticDeleteRow('123');

// 更新行
const updateData = {name: 'Jack', _rowid: '123'};
tableIns.staticUpdateRow(updateData);

// 参数:
// - data: object 必须,行数据(必须包含_rowid)
// - _rowid: string 必须,行ID
// 返回: 无
// 说明: 这些操作不会触发后端请求
```

#### Tabs组件方法

##### selectTab
设置选中的tab页签

```javascript
// 调用方式
tabsIns.selectTab('tab01');

// 参数:
// - activeKey: string 必须,要选中的tab的key
// 返回: 无
```

##### getPaneCount/getActiveKey
获取页签数量和当前激活的页签

```javascript
// 调用方式
const count = tabsIns.getPaneCount();    // 获取页签数量
const activeKey = tabsIns.getActiveKey(); // 获取当前激活的页签key

// 参数: 无
// 返回: number/string
```

##### removePanel/removePanelAndTriggerEvent
删除页签

```javascript
// 调用方式
tabsIns.removePanel('001');                    // 仅删除
tabsIns.removePanelAndTriggerEvent('001');     // 删除并触发事件

// 参数:
// - key: string 必须,要删除的面板key
// 返回: 无
```

##### setAddButtonVisible/getAddButtonVisible
设置/获取新增按钮的显示状态

```javascript
// 调用方式
tabsIns.setAddButtonVisible(false);  // 设置显示状态
const visible = tabsIns.getAddButtonVisible(); // 获取显示状态

// 参数:
// - visible: boolean 必须,是否显示
// 返回: boolean
```

##### setType/getType
设置/获取Tabs的显示类型

```javascript
// 调用方式
tabsIns.setType('card');  // 设置类型
const type = tabsIns.getType(); // 获取类型

// 参数:
// - type: string 必须,类型('line'|'card'|'borderRadius'|'editable-card')
// 返回: string
```

#### 最佳实践

##### Table数据操作
```javascript
// 1. 静态数据操作
const newRow = {name: 'Jack', age: 25, _rowid: 'new_1'};
tableIns.staticAddRow(newRow);

// 2. 动态更新数据
tableIns.setLoading(true);
try {
  await tableIns.update();
} finally {
  tableIns.setLoading(false);
}

// 3. 导出数据
const headers = tableIns.getTableHeaderForExport();
exportTableData(headers);
```

##### Tabs操作
```javascript
// 1. 页签管理
if(tabsIns.getPaneCount() < maxTabs) {
  tabsIns.setAddButtonVisible(true);
} else {
  tabsIns.setAddButtonVisible(false);
}

// 2. 动态切换页签
const currentKey = tabsIns.getActiveKey();
if(needSwitch) {
  tabsIns.selectTab(targetKey);
}

// 3. 关闭页签
tabsIns.removePanelAndTriggerEvent(tabKey);
```

#### TableForm组件独有用法

##### 数据操作方法

##### setValueByDataIndex
设置指定行中的指定字段的值

```javascript
// 调用方式
const rowId = payload._rowid;
tableFormIns.setValueByDataIndex(rowId, 'name', 'Jack');

// 参数:
// - rowId: string 必须,需要更新的行的rowId
// - dataIndexName: string 必须,需要更新的列的dataIndex
// - value: any 必须,要设置的值
// 返回: 无
```

##### updateValueOptionData
更新指定行指定字段的下拉选项

```javascript
// 调用方式
const data = [{label: '老师', value: 'teacher'}, {label: '学生', value: 'student'}];
tableFormIns.updateValueOptionData('_rowid_xxx', 'role', data);

// 参数:
// - rowId: string 必须,指定行的_rowid
// - dataIndexName: string 必须,指定字段的名称
// - data: Array<{label: string, value: string}> 必须,选项数据
// 返回: 无
```

##### setCustomColumnsPrecisionByRowIdAndDataIndex
设置指定行的InputNumber组件的精度

```javascript
// 调用方式
const ds = tableFormIns.getDataSource();
const precisions = [
  {
    _rowid: ds[0]._rowid,
    testNumber: 0
  },
  {
    _rowid: ds[1]._rowid,
    testNumber: 1
  }
];
tableFormIns.setCustomColumnsPrecisionByRowIdAndDataIndex(precisions);

// 参数:
// - newPrecisionData: Array<{_rowid: string, [key: string]: number}> 必须,指定行的精度配置
// 返回: 无
```

##### showAddButton
设置是否展示新增按钮

```javascript
// 调用方式
tableFormIns.showAddButton(true);

// 参数:
// - visible: boolean 必须,是否显示
// 返回: 无
```

#### Table、TableForm共有方法

#### hasSelectAllRecords
是否已全选

```javascript
// 调用方式
const hasSelectAllRecords = tableFormIns.hasSelectAllRecords();

// 参数: 无
// 返回: boolean true/false
```

##### getSelectedRowKeys
获取选中的行key

```javascript
// 调用方式
const selectedRowKeys = tableFormIns.getSelectedRowKeys();

// 参数: 无
// 返回: string[] 选中行的_rowid数组
// 返回示例: ['256', '2656']
```

##### getAuthorityData
获取权限信息

```javascript
// 调用方式
const authorityData = tableFormIns.getAuthorityData();

// 参数: 无
// 返回: array 权限信息数组
// 返回示例:
/*
[
  {
    "name": {
      "_canEdit": true,
      "_canAccess": false,
    },
    "_canEdit": true,
    "_canDelete": false,
    "_rowid": "256"
  }
]
*/
```

##### setStyleConfigScript
设置行的内容显示规则的js脚本

```javascript
// 调用方式
tableFormIns.setStyleConfigScript('if(record.age === 27) { return 1;} return 0;');

// 参数:
// - script: string 必须,js脚本字符串
// 返回: 无
```

##### setStyleHilites
设置行的内容高亮显示

```javascript
// 调用方式
const hilites = `[
  {
    fieldName: ['age', 'name'],
    textColor: '#FF0000',
    cellBackgroundColor: '#FFFF00',
    textBackgroundColor: '#FFFF00',
    id: '1'
  }
]`;
tableFormIns.setStyleHilites(hilites);

// 参数:
// - hilites: string 必须,高亮配置JSON字符串
// 返回: 无
```

##### getColumnsVisible
获取所有列的显示状态

```javascript
// 调用方式
const columnsVisible = tableIns.getColumnsVisible();

// 参数: 无
// 返回: object 字段名称和显示状态组成的json
// 返回示例: {name: true, age: false}
```

##### setColumnsVisible
设置所有列的显示状态

```javascript
// 调用方式
const columnsVisible = {name: true, age: false};
tableIns.setColumnsVisible(columnsVisible);

// 参数:
// - columnsVisible: object 字段名称和显示状态组成的json
// 返回: 无
```

##### updateColumnsVisible
设置需要更新的列的显示状态

```javascript
// 调用方式
const columnsVisible = {name: true, age: false};
tableIns.updateColumnsVisible(columnsVisible);

// 参数:
// - columnsVisible: object 字段名称和显示状态组成的json
// 返回: 无
```

##### getColumnsTitle
获取所有列的title

```javascript
// 调用方式
const columnsTitle = tableIns.getColumnsTitle();

// 参数: 无
// 返回: object
// 返回示例: { age: '年龄', name: '姓名', 'person#certNo': '证件号码'}
```

##### setColumnsTitleByDataIndex
设置指定列的title

```javascript
// 调用方式
const columnsTitle = { age: '年龄', name: '姓名', 'person#certNo': '证件号码'};
tableIns.setColumnsTitleByDataIndex(columnsTitle);

// 参数:
// - columnsTitle: object 列标题配置
// 返回: 无
```

##### getColumnsOptions
获取所有列的options

```javascript
// 调用方式
const columnsOptions = tableIns.getColumnsOptions();

// 参数: 无
// 返回: object
// 返回示例: { age: [{label: '女', value: 'F'}, {label: '男', value: 'M'}], name: []}
```

##### setColumnsOptionsByDataIndex
设置指定列的options

```javascript
// 调用方式
const columnsOptions = {age: [{label: '女2', value: 'F'}, {label: '男2', value: 'M'}]};
tableIns.setColumnsOptionsByDataIndex(columnsOptions);

// 参数:
// - columnsOptions: object 列选项配置
// 返回: 无
```

#### 行操作相关方法

##### setActiveRow
设置选中的行

```javascript
// 调用方式
const record = {_rowid: '101'};
tableIns.setActiveRow(record);

// 参数:
// - record: object 必须包含_rowid字段
// 返回: 无
```

##### isSelectAll
是否全选

```javascript
// 调用方式
const isSelectAll = tableIns.isSelectAll();

// 参数: 无
// 返回: boolean true/false
```

#### 自定义列操作方法

##### setCustomColumnsOptionsByRowIdAndDataIndex
设置指定行的指定字段的options

```javascript
// 调用方式
const customOptions = [
  {
    _rowid: '001',
    sex: [{'男': 'M'}, {'女': 'F'}],
    role: [{'经理': '002'}, {'部长': '001'}]
  },
  {
    _rowid: '002',
    sex: [{'男': 'M'}, {'女': 'F'}, {'不限': 'U'}],
    role: [{'主任': '003'}, {'部长': '001'}]
  }
];
tableIns.setCustomColumnsOptionsByRowIdAndDataIndex(customOptions);

// 参数:
// - customOptions: Array<{_rowid: string, [key: string]: Array}>
// 返回: 无
```

##### getCustomColumnsOptionsByRowIdAndDataIndex
获取当前组件所有行的指定行的指定字段的options

```javascript
// 调用方式
const allCustomColumnsOptions = tableIns.getCustomColumnsOptionsByRowIdAndDataIndex();

// 参数: 无
// 返回: Array<{_rowid: string, [key: string]: Array}>
```

##### setColumnEditable
指定某列是否可编辑

```javascript
// 调用方式
tableIns.setColumnEditable('sex', false);

// 参数:
// - dataIndex: string 列名
// - enableFlag: boolean 是否可编辑
// 返回: 无
```

##### setOperationColumnWidth
设置操作列的宽度

```javascript
// 调用方式
tableIns.setOperationColumnWidth('100px');
// 或
tableIns.setOperationColumnWidth(100);

// 参数:
// - columnWidth: string|number 宽度值
// 返回: 无
```

#### 最佳实践

##### 列显示控制
```javascript
// 1. 获取当前列显示状态
const currentVisible = tableIns.getColumnsVisible();

// 2. 更新指定列显示状态
const newVisible = {
  ...currentVisible,
  age: false,  // 隐藏年龄列
  name: true   // 显示姓名列
};
tableIns.updateColumnsVisible(newVisible);
```

##### 自定义列选项
```javascript
// 1. 设置行级别的列选项
const customOptions = [
  {
    _rowid: '001',
    department: [
      {label: '研发部', value: 'dev'},
      {label: '测试部', value: 'test'}
    ]
  }
];
tableIns.setCustomColumnsOptionsByRowIdAndDataIndex(customOptions);

// 2. 设置列编辑权限
tableIns.setColumnEditable('salary', false); // 禁止编辑薪资列
```


#### 最佳实践

##### 数据操作
```javascript
// 1. 更新单个字段值
const rowId = selectedRow._rowid;
tableFormIns.setValueByDataIndex(rowId, 'name', 'Jack');

// 2. 更新下拉选项
const options = await fetchOptions();
tableFormIns.updateValueOptionData(rowId, 'role', options);

// 3. 设置数字精度
const precisions = rows.map(row => ({
  _rowid: row._rowid,
  amount: 2 // 保留2位小数
}));
tableFormIns.setCustomColumnsPrecisionByRowIdAndDataIndex(precisions);
```

##### 权限和样式控制
```javascript
// 1. 检查权限
const authority = tableFormIns.getAuthorityData();
const canEdit = authority.some(auth => 
  auth._rowid === rowId && auth._canEdit
);

// 2. 设置条件样式
tableFormIns.setStyleConfigScript(`
  if(record.status === 'error') {
    return 1; // 使用样式1
  }
  return 0; // 使用默认样式
`);

// 3. 设置高亮规则
const highlightRules = `[
  {
    fieldName: ['amount'],
    textColor: '#FF0000',
    cellBackgroundColor: '#FFFF00',
    id: 'warning'
  }
]`;
tableFormIns.setStyleHilites(highlightRules);
```

#### 选项操作方法

##### setOptions
设置选项

```javascript
// 调用方式
const options = [{label: '老师', value: 'teacher'}, {label: '学生', value: 'student'}];
componentIns.setOptions(options);

// 参数:
// - options: Array<{label: string, value: string|number|boolean}> 必须
// 返回: 无
```

##### getOptions
获取选项

```javascript
// 调用方式
const options = componentIns.getOptions();

// 参数: 无
// 返回: Array<{label: string, value: string|number|boolean}>
// 返回示例: [{label: '老师', value: 'teacher'}, {label: '学生', value: 'student'}]
```

##### removeOption
删除一个选项

```javascript
// 调用方式
componentIns.removeOption('001');

// 参数:
// - optionKey: string 必须,要删除的option的value值
// 返回: 无
```

##### removeOptions
删除多个选项

```javascript
// 调用方式
componentIns.removeOptions(['001', '002']);

// 参数:
// - optionKeys: string[] 必须,要删除的options的value值数组
// 返回: 无
```

##### addOption
添加一个选项

```javascript
// 调用方式
componentIns.addOption('001', '经理');

// 参数:
// - optionKey: string 必须,要增加的option的value值
// - optionLabel: string 必须,要增加的option的label值
// 返回: 无
```

##### addOptions
添加多个选项

```javascript
// 调用方式
const newOptions = [
  {label: 'newLabel1', value: 'newValue1'},
  {label: 'newLabel2', value: 'newValue2'}
];
componentIns.addOptions(newOptions);

// 参数:
// - options: Array<{label: string, value: string|number|boolean}> 必须
// 返回: 无
```

#### 选中值操作方法

##### getSelectedOption
获取选中项的option(仅适用于单选)

```javascript
// 调用方式
const selectedOption = componentIns.getSelectedOption();

// 参数: 无
// 返回: {label: string, value: string|number|boolean}
// 返回示例: {label: '老师', value: 'teacher'}
```

##### getSelectedLabel
获取选中项的label(仅适用于单选)

```javascript
// 调用方式
const labelStr = componentIns.getSelectedLabel();

// 参数: 无
// 返回: string
// 返回示例: '老师'
```

#### 组件特定方法

##### Cascader特有方法
获取级联选择器的选项数据

```javascript
// 调用方式
const options = cascaderIns.getOptions();

// 参数: 无
// 返回: array
// 返回示例:
/*
[
  {
    "label": "北京市",
    "value": "01",
    "children": [
      {
        "label": "海淀区",
        "value": "0101"
      }
    ]
  }
]
*/
```

#### 最佳实践

#### 选项管理
```javascript
// 1. 初始化选项
const initOptions = [
  {label: '选项1', value: '1'},
  {label: '选项2', value: '2'}
];
componentIns.setOptions(initOptions);

// 2. 动态添加选项
componentIns.addOption('3', '选项3');

// 3. 批量更新选项
const newOptions = await fetchOptions();
componentIns.setOptions(newOptions);
```

##### 选中值处理
```javascript
// 1. 获取选中值(单选)
const selected = componentIns.getSelectedOption();
if(selected) {
  console.log(`选中: ${selected.label} - ${selected.value}`);
}

// 2. 删除选项时处理选中值
const currentSelected = componentIns.getSelectedOption();
if(currentSelected && currentSelected.value === valueToRemove) {
  // 清除选中值或选择其他选项
  componentIns.setValue(null);
}
```

##### 级联选择器操作
```javascript
// 1. 获取完整选项数据
const cascaderOptions = cascaderIns.getOptions();

// 2. 动态更新选项
const newCascaderOptions = transformData(rawData);
cascaderIns.setOptions(newCascaderOptions);
```

#### 数据结构规范

##### 基础选项结构
```javascript
{
  label: string,    // 显示文本
  value: string|number|boolean, // 选项值
  disabled?: boolean // 是否禁用
}
```

##### 级联选项结构
```javascript
{
  label: string,    // 显示文本
  value: string,    // 选项值
  disabled?: boolean, // 是否禁用
  children?: Array<{  // 子选项
    label: string,
    value: string,
    children?: Array<...>
  }>
}
```
#### Card组件方法

##### setTitle
设置标题

```javascript
// 调用方式
cardIns.setTitle('联系人');

// 参数:
// - title: string 必须,要设置的标题
// 返回: 无
```

##### setRefWindow
设置引用窗口

```javascript
// 调用方式
cardIns.setRefWindow('Layout1');

// 参数:
// - windowComponentName: string 必须,要设置的窗口
// 返回: 无
```

##### removeRefWindow
移除引用窗口

```javascript
// 调用方式
cardIns.removeRefWindow();

// 参数: 无
// 返回: 无
```

#### Cascader组件方法

##### getOptions
获取级联选择器的选项数据

```javascript
// 调用方式
const options = cascaderIns.getOptions();

// 参数: 无
// 返回: array
// 返回示例:
/*
[
  {
    "label": "北京市",
    "value": "01",
    "children": [
      {
        "label": "海淀区",
        "value": "0101"
      }
    ]
  }
]
*/
```

#### ComplexSelect组件方法

##### setOptionsWithSearchId
设置新的下拉选项

```javascript
// 调用方式
const options = [
  { 
    label: '我是Jack', 
    value: '1',
    name: 'Jack',
    sex: '男',
    age: '27',
    mobile: '13434562345'
  }
];
cIns.setOptionsWithSearchId(options);

// 参数:
// - options: array 选项数组,包含详细信息
// 返回: 无
```

#### 最佳实践

##### Card操作
```javascript
// 1. 动态设置标题
if(needUpdateTitle) {
  cardIns.setTitle('新标题');
}

// 2. 引用窗口管理
cardIns.setRefWindow('newWindow');
// 不需要时移除
cardIns.removeRefWindow();
```

#### Cascader操作
```javascript
// 1. 获取级联数据
const cascaderOptions = cascaderIns.getOptions();

// 2. 数据处理
const processedOptions = cascaderOptions.map(option => ({
  ...option,
  label: `${option.label}(${option.value})`
}));

// 3. 更新选项
cascaderIns.setOptions(processedOptions);
```

#### ComplexSelect操作
```javascript
// 1. 准备选项数据
const complexOptions = [
  {
    label: '张三(开发)',
    value: '1',
    name: '张三',
    department: '开发部',
    role: '开发工程师'
  }
];

// 2. 设置选项
cIns.setOptionsWithSearchId(complexOptions);
```

#### 数据结构规范

#### Card配置
```javascript
{
  title: string,           // 卡片标题
  refWindow?: string      // 引用窗口名称
}
```

#### Cascader选项结构
```javascript
{
  label: string,          // 显示文本
  value: string,          // 选项值
  disabled?: boolean,     // 是否禁用
  children?: Array<{      // 子选项
    label: string,
    value: string,
    children?: Array<...>
  }>
}
```

#### ComplexSelect选项结构
```javascript
{
  label: string,          // 显示文本
  value: string,          // 选项值
  [key: string]: any     // 其他自定义字段
}
```

#### 消息通知相关API

##### sendMessageByMsgConfig
通过设置指定的消息配置发送消息

```javascript
// 调用方式
OC.sendMessageByMsgConfig(
  '001', 
  ['01','02'], 
  {message: '创建流程成功'}, 
  function(response) {
    console.log('发送成功:', response);
  }, 
  function() {
    console.log('发送完成');
  }
);

// 参数:
// - msgConfigKey: string 必须,消息配置key
// - userIds: string[] 必须,接收用户ID数组
// - data: object 必须,消息模板数据
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

##### sendSystemMsg
发送系统消息

```javascript
// 调用方式
OC.sendSystemMsg(
  '002', 
  ['01','02'], 
  {message: '创建流程成功'}, 
  function(response) {
    console.log('发送成功:', response);
  }
);

// 参数:
// - templateKey: string 必须,消息模板key
// - userIds: string[] 必须,接收用户ID数组
// - data: object 必须,消息模板数据
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

##### sendSMSMsg
发送短信消息

```javascript
// 调用方式
OC.sendSMSMsg(
  '002', 
  ['13088888888'], 
  {message: '验证码:1234'}, 
  function(response) {
    console.log('发送成功:', response);
  }
);

// 参数:
// - templateKey: string 必须,消息模板key
// - phones: string[] 必须,接收手机号数组
// - data: object 必须,消息模板数据
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

##### sendEmailMsg
发送邮件消息

```javascript
// 调用方式
OC.sendEmailMsg(
  '002', 
  ['<EMAIL>'], 
  {message: '会议通知'}, 
  function(response) {
    console.log('发送成功:', response);
  }
);

// 参数:
// - templateKey: string 必须,消息模板key
// - emails: string[] 必须,接收邮箱数组
// - data: object 必须,消息模板数据
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

#### 文件操作相关API

##### downloadFile
下载文件

```javascript
// 调用方式
OC.downloadFile(
  '/source/xxx.png', 
  function(response) {
    console.log('下载成功:', response);
  }, 
  function() {
    console.log('下载完成');
  }
);

// 参数:
// - filePathName: string 必须,文件路径
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

##### showFileViewer
显示文件预览

```javascript
// 调用方式
fileViewerIns.showFileViewer({
  fileType: 'image',
  filePath: 'http://open-care/images/1.png',
  showInDrawer: true
});

// 参数:
// - config: object 必须,预览配置
//   - fileType: string 必须,文件类型('image'|'pdf')
//   - filePath: string 必须,文件路径
//   - showInDrawer?: boolean 是否在抽屉中显示
// 返回: 无
```

##### hideFileViewer
隐藏文件预览

```javascript
// 调用方式
fileViewerIns.hideFileViewer();

// 参数: 无
// 返回: 无
```

#### 最佳实践

##### 消息发送
```javascript
// 1. 系统消息
function sendNotification(users, message) {
  OC.sendSystemMsg(
    'NOTIFICATION_TEMPLATE',
    users,
    { content: message },
    function(response) {
      if (response.success) {
        OC.showMessageDlg('通知发送成功', 'success');
      } else {
        OC.showMessageDlg('通知发送失败', 'error');
      }
    }
  );
}

// 2. 多渠道消息
function sendMultiChannelMsg(users, phones, emails, message) {
  // 系统消息
  OC.sendSystemMsg('TEMPLATE_KEY', users, { message });
  
  // 短信通知
  if (phones.length > 0) {
    OC.sendSMSMsg('SMS_TEMPLATE', phones, { message });
  }
  
  // 邮件通知
  if (emails.length > 0) {
    OC.sendEmailMsg('EMAIL_TEMPLATE', emails, { message });
  }
}
```

##### 文件处理
```javascript
// 1. 文件下载
function downloadWithProgress(filePath) {
  OC.showGlobalLoading();
  OC.downloadFile(
    filePath,
    function(response) {
      if (response.success) {
        OC.showMessageDlg('下载成功', 'success');
      } else {
        OC.showMessageDlg('下载失败', 'error');
      }
    },
    function() {
      OC.hideGlobalLoading();
    }
  );
}

// 2. 文件预览
function previewFile(file) {
  const config = {
    fileType: file.type,
    filePath: file.url,
    showInDrawer: file.size > 1024 * 1024 // 大文件使用抽屉展示
  };
  
  try {
    fileViewerIns.showFileViewer(config);
  } catch (error) {
    OC.showMessageDlg('文件预览失败', 'error');
  }
}
```

##### 注意事项

##### 消息发送注意事项
```javascript
// 1. 批量发送限制
const MAX_BATCH_SIZE = 100;

function sendBatchMessages(users) {
  // 分批发送
  for (let i = 0; i < users.length; i += MAX_BATCH_SIZE) {
    const batch = users.slice(i, i + MAX_BATCH_SIZE);
    OC.sendSystemMsg('TEMPLATE_KEY', batch, { message: 'batch notification' });
  }
}

// 2. 错误处理
function sendWithRetry(msgConfig, retryTimes = 3) {
  let attempts = 0;
  
  function send() {
    attempts++;
    OC.sendMessageByMsgConfig(
      msgConfig.key,
      msgConfig.users,
      msgConfig.data,
      function(response) {
        if (!response.success && attempts < retryTimes) {
          setTimeout(send, 1000 * attempts);
        }
      }
    );
  }
  
  send();
}
```

##### 文件操作注意事项
```javascript
// 1. 文件大小限制
const MAX_PREVIEW_SIZE = 10 * 1024 * 1024; // 10MB

function safePreviewFile(file) {
  if (file.size > MAX_PREVIEW_SIZE) {
    OC.showMessageDlg('文件过大,请下载后查看', 'warning');
    return;
  }
  
  fileViewerIns.showFileViewer({
    fileType: file.type,
    filePath: file.url
  });
}

// 2. 文件类型检查
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];

function validateAndPreview(file) {
  if (!ALLOWED_TYPES.includes(file.type)) {
    OC.showMessageDlg('不支持的文件类型', 'error');
    return;
  }
  
  fileViewerIns.showFileViewer({
    fileType: file.type.split('/')[0],
    filePath: file.url
  });
}
```

#### 工作流相关API

##### startProcessByKey
通过流程定义key启动流程

```javascript
// 调用方式
const entities = [
  {
    "entityName": "com.open_care.sys.OcUser",
    "entityInstId": {
      "ctxType": "cmp",
      "ctxValue": "input1"
    }
  }
];
const variables = [
  {
    "name": "aaaaa",
    "value": {
      "ctxType": "cmp",
      "ctxValue": "input1"
    }
  }
];
OC.startProcessByKey(
  "processKey", 
  entities, 
  variables, 
  function(response) {
    console.log('流程启动成功');
  }
);

// 参数:
// - processDefinitionKey: string 必须,流程定义id
// - entities: array 必须,entityName和entity实例信息
// - variables: array 必须,流程变量
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

##### completeTask
完成任务

```javascript
// 调用方式
const variables = [
  {
    "name": "approveResult",
    "value": {
      "ctxType": "cmp",
      "ctxValue": "agree"
    }
  }
];
OC.completeTask(
  "taskId", 
  variables, 
  function(response) {
    console.log('任务完成');
  }
);

// 参数:
// - taskId: string 必须,任务id
// - variables: array 必须,任务变量
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

##### claimTask
申领任务

```javascript
// 调用方式
OC.claimTask(
  "taskId", 
  "userId", 
  function(response) {
    console.log('任务申领成功');
  }
);

// 参数:
// - taskId: string 必须,任务id
// - userId: string 必须,用户id
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

##### getTaskAuditHistory
获取任务执行的历史数据

```javascript
// 调用方式
OC.getTaskAuditHistory(
  "processInstId", 
  function(response) {
    console.log('历史数据:', response);
  }
);

// 参数:
// - processInstId: string 必须,流程实例id
// - responseHandler: function 必须,回调方法
// - exHandler?: function 可选,执行完回调方法后的回调
// 返回: 无
```

#### UI操作相关API

##### setHeaderVisible
设置头部是否显示

```javascript
// 调用方式
OC.setHeaderVisible(false); // 隐藏头部

// 参数:
// - visible: boolean 必须,是否显示
// 返回: 无
```

##### setBreadcrumbVisible
设置面包屑是否显示

```javascript
// 调用方式
OC.setBreadcrumbVisible(false); // 隐藏面包屑

// 参数:
// - visible: boolean 必须,是否显示
// 返回: 无
```

##### setHeaderBackground
设置头部背景

```javascript
// 调用方式
OC.setHeaderBackground('red');
OC.setHeaderBackground('red url(http://www.xxx.com/xxx.jpg)');
OC.setHeaderBackground('url(http://www.xxx.com/xxx.jpg) no-repeat');

// 参数:
// - background: string 必须,背景样式
// 返回: 无
```

##### setSiderVisible
设置侧边栏是否显示

```javascript
// 调用方式
OC.setSiderVisible(false);

// 参数:
// - visible: boolean 必须,是否显示
// 返回: 无
```

##### setNavMenuVisible
设置导航菜单是否显示

```javascript
// 调用方式
OC.setNavMenuVisible(false);

// 参数:
// - visible: boolean 必须,是否显示
// 返回: 无
```

##### setLayoutHeadHeight
设置头部高度

```javascript
// 调用方式
OC.setLayoutHeadHeight('200px');

// 参数:
// - height: string 必须,高度值
// 返回: 无
```

#### 最佳实践

##### 工作流操作
```javascript
// 1. 启动流程
function startApprovalProcess(data) {
  const entities = [
    {
      entityName: "ApprovalForm",
      entityInstId: data.formId
    }
  ];
  
  const variables = [
    {
      name: "initiator",
      value: data.userId
    }
  ];
  
  OC.startProcessByKey(
    "approval_process",
    entities,
    variables,
    function(response) {
      if (response.success) {
        OC.showMessageDlg('流程启动成功', 'success');
      } else {
        OC.showMessageDlg('流程启动失败', 'error');
      }
    }
  );
}

// 2. 任务处理
function handleTask(taskId, action) {
  const variables = [
    {
      name: "action",
      value: action
    },
    {
      name: "handleTime",
      value: new Date().toISOString()
    }
  ];
  
  OC.completeTask(
    taskId,
    variables,
    function(response) {
      if (response.success) {
        OC.showMessageDlg('任务处理成功', 'success');
      } else {
        OC.showMessageDlg('任务处理失败', 'error');
      }
    }
  );
}
```

##### UI布局控制
```javascript
// 1. 自定义布局
function customizeLayout(config) {
  // 控制头部
  OC.setHeaderVisible(config.showHeader);
  if (config.showHeader) {
    OC.setHeaderBackground(config.headerBackground);
    OC.setLayoutHeadHeight(config.headerHeight);
  }
  
  // 控制导航
  OC.setNavMenuVisible(config.showNav);
  OC.setSiderVisible(config.showSider);
  
  // 控制面包屑
  OC.setBreadcrumbVisible(config.showBreadcrumb);
}

// 2. 响应式布局
function responsiveLayout() {
  const width = window.innerWidth;
  if (width < 768) {
    // 移动端布局
    OC.setNavMenuVisible(false);
    OC.setSiderVisible(false);
    OC.setHeaderVisible(true);
    OC.setLayoutHeadHeight('60px');
  } else {
    // PC端布局
    OC.setNavMenuVisible(true);
    OC.setSiderVisible(true);
    OC.setHeaderVisible(true);
    OC.setLayoutHeadHeight('80px');
  }
}
```

#### 注意事项

##### 工作流操作注意事项
```javascript
// 1. 流程变量验证
function validateProcessVariables(variables) {
  if (!Array.isArray(variables)) {
    throw new Error('variables must be array');
  }
  
  variables.forEach(variable => {
    if (!variable.name || !variable.value) {
      throw new Error('variable must have name and value');
    }
  });
}

// 2. 任务状态检查
function checkTaskStatus(taskId) {
  return new Promise((resolve, reject) => {
    OC.getTaskAuditHistory(
      taskId,
      function(response) {
        if (response.status === 'completed') {
          reject(new Error('task already completed'));
        } else {
          resolve();
        }
      }
    );
  });
}
```

##### UI操作注意事项
```javascript
// 1. 性能优化
let layoutUpdateTimer;
function debounceLayoutUpdate(config) {
  if (layoutUpdateTimer) {
    clearTimeout(layoutUpdateTimer);
  }
  
  layoutUpdateTimer = setTimeout(() => {
    customizeLayout(config);
  }, 200);
}

// 2. 状态记录
const layoutState = {
  headerVisible: true,
  siderVisible: true,
  navMenuVisible: true
};

function updateLayout(key, value) {
  layoutState[key] = value;
  switch(key) {
    case 'headerVisible':
      OC.setHeaderVisible(value);
      break;
    case 'siderVisible':
      OC.setSiderVisible(value);
      break;
    case 'navMenuVisible':
      OC.setNavMenuVisible(value);
      break;
  }
}
```

### 事件相关API

#### eventEmit
发出自定义事件

```javascript
// 调用方式
// 1. 简单事件
OC.eventEmit('customer.service.center.updateOrderList', 'alkdjf-lkajdfk0-kadi-3498vdjd');

// 2. 带参数事件
const params = {
  ocId: '123',
  type: 'update'
};
OC.eventEmit('customer.service.center.updateCustomer', params);

// 参数:
// - eventName: string 必须,事件名称(建议使用点号分隔的命名空间)
// - params: any 事件参数
// 返回: 无
```

#### eventOn
接收自定义事件

```javascript
// 调用方式
// 1. 简单事件监听
OC.eventOn('customer.service.center.updateOrderList', function(orderId) {
  console.log('收到订单更新:', orderId);
});

// 2. 带参数事件监听
OC.eventOn('customer.service.center.updateCustomer', function(params) {
  console.log('收到客户更新:', params.ocId, params.type);
});

// 参数:
// - eventName: string 必须,要监听的事件名称
// - callback: function(params) 必须,事件回调函数
// 返回: 无
```

### 2. 数据操作API

#### updateDataSource
更新指定窗口中指定组件的数据

```javascript
// 调用方式
OC.updateDataSource(
  ocWindow, 
  "Table01", 
  {
    data: [{name: "Jack", age: 23}],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 100
    }
  }
);

// 参数:
// - window: window实例 必须
// - componentName: string 必须,组件的componentName
// - data: object 必须,要更新的数据
// 返回: 无
```

#### getDataSource
获取指定窗口中指定组件的数据

```javascript
// 调用方式
const dataSource = OC.getDataSource(ocWindow, "Table01");

// 参数:
// - window: window实例 必须
// - componentName: string 必须,组件的componentName
// 返回: object 组件当前数据
```

#### setFormValues
给指定form赋值

```javascript
// 调用方式
OC.setFormValues(
  ocWindow, 
  'userForm', 
  {
    data: {
      name: 'Jack',
      age: 25
    },
    authority: {
      _canEdit: true,
      name: {
        _canEdit: false
      }
    }
  }
);

// 参数:
// - window: window实例 必须
// - formComponentName: string 必须,form组件名称
// - allValues: object 必须,要设置的值和权限信息
// 返回: 无
```

#### manipulateFormFieldsValue
格式化表单值

```javascript
// 调用方式
const fieldValues = {
  name: "", 
  age: 23, 
  sex: "M", 
  role: undefined
};
const newFieldValues = OC.manipulateFormFieldsValue(fieldValues);
// 结果: {name: null, age: 23, sex: "M", role: null}

// 参数:
// - fieldValues: object 必须,要格式化的表单值
// 返回: object 格式化后的值
```

#### 最佳实践

##### 事件处理
```javascript
// 1. 事件命名规范
const EVENT_NAMES = {
  CUSTOMER: {
    UPDATE: 'customer.update',
    DELETE: 'customer.delete'
  },
  ORDER: {
    CREATE: 'order.create',
    STATUS_CHANGE: 'order.status.change'
  }
};

// 2. 事件处理封装
class CustomerEventHandler {
  constructor() {
    this.initEventListeners();
  }

  initEventListeners() {
    // 监听客户更新事件
    OC.eventOn(EVENT_NAMES.CUSTOMER.UPDATE, this.handleCustomerUpdate);
    // 监听客户删除事件
    OC.eventOn(EVENT_NAMES.CUSTOMER.DELETE, this.handleCustomerDelete);
  }

  handleCustomerUpdate = (params) => {
    try {
      // 处理客户更新
      this.updateCustomerInfo(params);
      // 通知相关组件
      OC.eventEmit(EVENT_NAMES.ORDER.STATUS_CHANGE, {
        customerId: params.id,
        type: 'customer_update'
      });
    } catch (error) {
      console.error('处理客户更新失败:', error);
    }
  }

  handleCustomerDelete = (customerId) => {
    // 处理客户删除
  }
}
```

##### 数据操作
```javascript
// 1. 表格数据更新
function updateTableData(tableIns, newData) {
  try {
    // 更新前先显示loading
    tableIns.setLoading(true);
    
    // 更新数据
    OC.updateDataSource(ocWindow, tableIns.componentName, {
      data: newData,
      pagination: {
        current: 1,
        pageSize: 10,
        total: newData.length
      }
    });
  } catch (error) {
    OC.showMessageDlg('数据更新失败', 'error');
  } finally {
    tableIns.setLoading(false);
  }
}

// 2. 表单数据处理
function handleFormData(formIns) {
  // 获取表单数据
  const values = formIns.getFieldsValue();
  
  // 格式化数据
  const formattedValues = OC.manipulateFormFieldsValue(values);
  
  // 设置表单数据
  OC.setFormValues(ocWindow, formIns.componentName, {
    data: formattedValues,
    authority: {
      _canEdit: true,
      // 某些字段不可编辑
      salary: {
        _canEdit: false
      }
    }
  });
}
```

##### 注意事项

###### 事件处理注意事项
```javascript
// 1. 事件解绑
class ComponentWithEvents {
  componentDidMount() {
    // 绑定事件
    this.eventHandlers = [
      {
        name: 'event1',
        handler: this.handleEvent1
      },
      {
        name: 'event2',
        handler: this.handleEvent2
      }
    ];
    
    this.eventHandlers.forEach(({name, handler}) => {
      OC.eventOn(name, handler);
    });
  }
  
  componentWillUnmount() {
    // 组件卸载时解绑事件
    this.eventHandlers.forEach(({name, handler}) => {
      // 假设有eventOff方法
      OC.eventOff(name, handler);
    });
  }
}

// 2. 事件性能优化
function debounceEvent(eventName, wait = 300) {
  let timer = null;
  return function(params) {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      OC.eventEmit(eventName, params);
    }, wait);
  };
}
```

#### 数据操作注意事项

```javascript
// 1. 数据验证
function validateData(data) {
  if (!data || typeof data !== 'object') {
    throw new Error('Invalid data format');
  }
  
  if (Array.isArray(data.data)) {
    data.data.forEach(item => {
      if (!item._rowid) {
        throw new Error('Missing _rowid in data item');
      }
    });
  }
  
  return true;
}

// 2. 大数据处理
function handleLargeDataUpdate(tableIns, largeData) {
  // 分批处理数据
  const BATCH_SIZE = 100;
  const totalBatches = Math.ceil(largeData.length / BATCH_SIZE);
  
  let currentBatch = 0;
  
  function updateNextBatch() {
    if (currentBatch >= totalBatches) {
      tableIns.setLoading(false);
      return;
    }
    
    const start = currentBatch * BATCH_SIZE;
    const end = Math.min(start + BATCH_SIZE, largeData.length);
    const batchData = largeData.slice(start, end);
    
    OC.updateDataSource(ocWindow, tableIns.componentName, {
      data: batchData,
      pagination: {
        current: currentBatch + 1,
        pageSize: BATCH_SIZE,
        total: largeData.length
      }
    });
    
    currentBatch++;
    setTimeout(updateNextBatch, 100);
  }
  
  tableIns.setLoading(true);
  updateNextBatch();
}
```

#### JSONPath工具方法

##### jpQuery
在obj中匹配路径表达式查找元素

```javascript
// 调用方式
var cities = [
  { name: "London", "population": 8615246 },
  { name: "Berlin", "population": 3517424 },
  { name: "Madrid", "population": 3165235 },
  { name: "Rome", "population": 2870528 }
];
OC.jpQuery(cities, '$..name', 3);  // [ "London", "Berlin", "Madrid"]

// 参数:
// - obj: Object|Array 必须,要查询的对象
// - pathExpression: string 必须,JSONPath表达式
// - count?: number 可选,返回的最大数量
// 返回: array 匹配的元素数组
```

##### jpValue
返回匹配路径表达式的第一个元素的值

```javascript
// 调用方式
var cities = [
  { name: "London", "population": 8615246 },
  { name: "Berlin", "population": 3517424 }
];
OC.jpValue(cities, '$..[?(@.name===Berlin)]');  
// { name: "Berlin", "population": 3517424 }

// 参数:
// - obj: Object|Array 必须,要查询的对象
// - pathExpression: string 必须,JSONPath表达式
// 返回: any 匹配的第一个元素
```

##### jpParent
返回第一个匹配元素的父元素

```javascript
// 调用方式
var cities = [
  { name: "Berlin", "population": 3517424,
    children:[
      { name: "Mitte", "population": 8297},
      { name: "Lichtenberg", "population": 268466}
    ]
  }
];
OC.jpParent(cities, '$..[?(@.name===Lichtenberg)]');
// [{name: "Mitte"}, {name: "Lichtenberg"}]

// 参数:
// - obj: Object|Array 必须,要查询的对象
// - pathExpression: string 必须,JSONPath表达式
// 返回: any 父元素
```

##### jpApply
在每个匹配元素上运行提供的函数

```javascript
// 调用方式
var cities = [
  { name: "Madrid", "population": 3165235 },
  { name: "Rome", "population": 2870528 }
];
OC.jpApply(cities, '$..[?(@.population<3500000)]', 
  function(value) { return value.toUpperCase() });

// 参数:
// - obj: Object|Array 必须,要处理的对象
// - pathExpression: string 必须,JSONPath表达式
// - fn: function 必须,处理函数
// 返回: Object|Array 处理后的对象副本
```

#### 拼音工具方法

##### getFullChars
将汉字转成拼音

```javascript
// 调用方式
const pinyinStr = OC.Pinyin.getFullChars('你好');
// 你好=>NiHao  你好Abc => NiHaoAbc haha => haha 

// 参数:
// - charts: string 必须,要转换的汉字字符串
// 返回: string 转换后的拼音字符串
```

#### getCamelChars
获取汉字字符串的首字母

```javascript
// 调用方式
const pinyinStr = OC.Pinyin.getCamelChars('你好');
// 你好=>NH  你好Abc => NHAbc  haha => haha

// 参数:
// - charts: string 必须,要转换的汉字字符串
// 返回: string 首字母字符串
```

#### 加密工具方法

##### encryptByDES
DES加密

```javascript
// 调用方式
const encryptStr = OC.encryptByDES(message, secretKey);

// 参数:
// - message: string 必须,要加密的字符串
// - secretKey: string 必须,密钥
// 返回: string 加密后的字符串
```

#### getUUID
获取UUID

```javascript
// 调用方式
const uuid = OC.getUUID();

// 参数: 无
// 返回: string UUID字符串
```

### 14. 系统功能API

#### 登录相关

```javascript
// 退出登录
OC.logout();
```

#### 剪贴板操作

```javascript
// 设置剪贴板内容
OC.setClipboardContent('要复制的内容');

// 获取剪贴板内容
const content = OC.getClipboardContent();
```

#### 全局Loading控制

```javascript
// 显示全局loading
OC.showGlobalLoading();

// 隐藏全局loading
OC.hideGlobalLoading();
```

#### 全局消息控制

```javascript
// 关闭全局请求消息
OC.closeGlobalRequestMsg();

// 打开全局请求消息
OC.openGlobalRequestMsg();
```

#### 最佳实践

##### JSONPath操作
```javascript
// 1. 数据查询
function findUsersByAge(users, maxAge) {
  return OC.jpQuery(users, `$..[?(@.age <= ${maxAge})]`);
}

// 2. 数据转换
function upperCaseNames(users) {
  return OC.jpApply(users, '$..name', value => value.toUpperCase());
}

// 3. 数据验证
function validateNestedData(data) {
  const missingFields = OC.jpQuery(data, '$..[?(@.required && !@.value)]');
  return missingFields.length === 0;
}
```

##### 拼音处理
```javascript
// 1. 搜索优化
function createSearchIndex(names) {
  return names.map(name => ({
    original: name,
    pinyin: OC.Pinyin.getFullChars(name),
    shortPinyin: OC.Pinyin.getCamelChars(name)
  }));
}

// 2. 排序优化
function sortByPinyin(items) {
  return items.sort((a, b) => {
    const pinyinA = OC.Pinyin.getFullChars(a.name);
    const pinyinB = OC.Pinyin.getFullChars(b.name);
    return pinyinA.localeCompare(pinyinB);
  });
}
```

##### 系统功能使用
```javascript
// 1. Loading状态管理
async function loadData() {
  OC.showGlobalLoading();
  try {
    await fetchData();
  } finally {
    OC.hideGlobalLoading();
  }
}

// 2. 剪贴板操作
function copyToClipboard(text) {
  try {
    OC.setClipboardContent(text);
    OC.showMessageDlg('复制成功', 'success');
  } catch (error) {
    OC.showMessageDlg('复制失败', 'error');
  }
}
```

#### 注意事项

##### JSONPath使用注意事项
```javascript
// 1. 性能优化
function optimizedQuery(data, path) {
  // 对于大数据集,限制返回数量
  const limit = 100;
  return OC.jpQuery(data, path, limit);
}

// 2. 错误处理
function safeJPQuery(data, path) {
  try {
    return OC.jpQuery(data, path);
  } catch (error) {
    console.error('JSONPath查询错误:', error);
    return [];
  }
}
```

##### 加密和安全注意事项
```javascript
// 1. 密钥管理
const KEY_CONFIG = {
  DES_KEY: 'your-des-key',
  // 其他加密配置
};
```

// 2. 敏感数据处理
```javascript
function encryptSensitiveData(data) {
  return Object.keys(data).reduce((acc, key) => {
    acc[key] = key.includes('password') ? 
      OC.encryptByDES(data[key], KEY_CONFIG.DES_KEY) : 
      data[key];
    return acc;
  }, {});
}
```

### 14. 事件传播与数据流转

#### 组件间数据传递
- 父子组件通信：通过属性传递数据，子组件通过事件通知父组件
- 兄弟组件通信：通过共同的父组件或事件机制传递数据
- 全局状态共享：通过上下文或事件机制共享数据

#### 数据流向规范
- 遵循单向数据流原则，从父组件流向子组件
- 子组件通过事件通知父组件数据变化
- 避免复杂的双向数据绑定

### 15. 错误处理与异常捕获

#### API调用错误处理
```javascript
OC.restGetApiCall('/api/customer/list', {}, function(response) {
  if (response.status !== '0') {
    OC.showMessageDlg('获取数据失败: ' + response.message, 'error');
    return;
  }
  // 处理成功响应
  console.log(response.data);
});
```

#### 异常捕获
```javascript
try {
  var data = JSON.parse(jsonString);
  processData(data);
} catch (error) {
  console.error('解析JSON失败', error);
  OC.showMessageDlg('数据格式错误', 'error');
}
```

### 16. 组件特定交互规范

#### Form组件
- 使用`getFieldsValue()`获取表单数据
- 使用`setFieldsValue()`设置表单数据
- 使用`validateFields()`进行表单验证
- 使用`resetFields()`重置表单数据

#### Table组件
- 使用`setDataSource()`设置表格数据
- 使用`getSelectedRows()`获取选中行
- 使用`refresh()`刷新表格数据
- 支持行选择、排序、筛选等交互

#### Modal/Drawer组件
- 使用`showModal()`显示模态框
- 使用`closeModal()`关闭模态框
- 使用`showDrawer()`显示抽屉
- 使用`closeDrawer()`关闭抽屉

### 17. 最佳实践

#### 1. 事件命名规范
- 使用驼峰命名法命名自定义事件
- 使用点号分隔模块和操作
- 事件名称应具有描述性

#### 2. 事件处理代码组织
- 将复杂的事件处理逻辑封装为函数
- 使用有意义的变量名和函数名
- 添加适当的注释说明

#### 3. 错误处理
- 添加错误处理逻辑
- 使用try-catch捕获异常
- 提供友好的错误提示
- 记录错误日志

#### 4. 性能优化
- 避免频繁触发事件
- 使用防抖和节流
- 避免不必要的组件重渲染
- 合理使用缓存

#### 5. 安全性
- 避免不安全的操作
- 验证用户输入
- 避免暴露敏感信息
- 使用适当的权限控制

### JS状态处理健壮性规则

#### 上下文
- 当处理来自外部系统的状态数据时使用此规则
- 适用于接收和处理第三方系统推送状态的场景
- 特别适用于系统集成和API通信场景

#### 要求
- 必须使用白名单方式验证所有外部状态
- 必须为未知状态提供明确的处理方案
- 必须记录和监控异常状态
- 必须实现降级策略处理异常状态
- 必须使用常量或枚举定义所有有效状态
- 必须实现状态变更的监控机制

#### 示例

<example>
// 良好示例：使用白名单和异常处理
const VALID_AGENT_STATES = Object.freeze({
  READY: 'Ready',
  BUSY: 'Busy',
  OFFLINE: 'Offline',
  RINGING: 'Ringing'
});

function handleAgentState(state) {
  // 验证状态是否在白名单中
  if (!Object.values(VALID_AGENT_STATES).includes(state)) {
    console.warn(`收到未知状态: ${state}`);
    StateMonitor.logUnknownState(state);
    return handleDefaultState(); // 降级策略
  }
  
  // 正常处理有效状态
  switch(state) {
    case VALID_AGENT_STATES.READY:
      return enableNewOrderButton();
    case VALID_AGENT_STATES.BUSY:
      return disableNewOrderButton();
    // 其他状态处理...
    default:
      return maintainCurrentState();
  }
}
</example>

<example type="invalid">
// 不良示例：直接使用未验证的状态
function handleAgentState(state) {
  // 直接使用状态，没有验证
  if (state === 'Ready') {
    enableNewOrderButton();
  } else if (state === 'Busy') {
    disableNewOrderButton();
  }
  // 未处理未知状态
}
</example>

<example>
// 良好示例：实现状态监控和告警
class StateMonitor {
  static unknownStates = new Map();
  
  static logUnknownState(state) {
    const count = this.unknownStates.get(state) || 0;
    this.unknownStates.set(state, count + 1);
    
    if (count + 1 > 5) {
      notifyAdministrator(`频繁收到未知状态: ${state}`);
    }
  }
  
  static getUnknownStatesReport() {
    return Array.from(this.unknownStates.entries())
      .map(([state, count]) => `${state}: ${count}次`);
  }
}
</example>

<example type="invalid">
// 不良示例：无状态监控
function processState(state) {
  try {
    // 处理状态...
  } catch (e) {
    console.error(e); // 仅记录错误，无统计和告警
  }
}
</example>
```

<critical>
- 禁止在组件操作方法中包含复杂逻辑
- 禁止使用硬编码值，必须使用常量
- 必须遵循函数单一职责原则
- 必须使用对象作为函数参数
- 必须进行完整的参数类型和非空检查
- 必须使用事件机制处理多组件联动
- 工具函数必须添加到平台SDK中
- 禁止使用setTimeout
- 禁止使用单字母变量名
- 禁止使用eval()和with()
- 禁止使用==和!=比较
- 禁止省略花括号{}
- 必须使用分号结尾
- 必须为事件和方法添加注释
- 必须遵循变量命名规范
- 必须正确处理组件的异步操作
- 事件必须使用正确的eventName
- 动作必须使用正确的actionName和必要属性
- 上下文引用必须使用正确的格式
- 组件引用必须使用正确的组件ID
- 复杂交互逻辑应使用jsAction实现
- 事件处理代码应遵循单一职责原则
- 自定义事件名称应遵循命名规范
</critical>