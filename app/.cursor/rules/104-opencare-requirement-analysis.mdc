---
description: VALIDATE when REVIEWING_REQUIREMENTS to ENSURE_COMPLETENESS
globs: **/*requirement*.{md,mdx,doc,docx}
alwaysApply: false
---

# 需求分析文档生成及评审规则

## 上下文
- 在进行需求分析文档编写时应用
- 在需求文档评审时作为检查清单使用
- 适用于所有业务功能需求的分析和评审

## 需求文档格式要求
-- 没有内容的章节需删除
-- 有Bullet后的文字后可以不带标点符号，其余情况必须要带标点符号
-- 注意文档内名词、名词结构统一
-- 标题前后增加空行
-- 段落前后增加空行
-- 如果两个章节有关联，则在每个章节里都要有说明类似于：“xx章节和yy章节合并起来应对zzz问题，本节描述了xx，关于yy，参见章节'yyyy'”
-- 针对接口描述，需清楚的描述提供者及调用者
-- 文档中的表格行高度需保持统一
-- 文档内字体大小保持统一
-- 图片、表格需居中

## 需求文档结构要求

### 1. 功能点整体描述
- 明确阐述功能的具体业务意义
- 说明功能依赖的输入数据来源
- 列举功能产生的输出数据

### 2. 业务上下文
- 描述功能涉及的业务系统上下文
- 列举相关参考文档清单

### 3. 流程说明
- 简单流程：使用文字说明
- 复杂流程：必须提供流程图
- 流程图要求：清晰展示各步骤、判断条件和流转路径

### 4. 用例分析
- 提供用例图
- 明确各角色与功能的交互关系

### 5. 状态流转
- 描述功能涉及实体的所有可能状态
- 说明状态间的转换条件和触发时机

### 6. 业务规则
- 必填项规则
- 边界值限制
- 业务逻辑规则
- 错误提示信息规范
- CRUD 操作规则详细说明

### 7. 页面原型设计
#### 7.1 原型图要求
- 提供清晰的页面原型图
- 标注交互要点和关键说明

#### 7.2 表单组件规范
- 组件类型及属性说明
- 可见性和编辑规则
- 校验规则
- 默认值设置
- 具体组件要求：
  - 文本/数值：长度限制、精度要求
  - 选择类：单/多选、数据源、展示内容、排序
  - 上传类：文件数量、类型限制
  - 日期类：格式规范

#### 7.3 表格组件规范
- 数据源说明
- 列配置要求
- 过滤规则
- 排序规则
- 操作按钮规则
- 事件处理说明
- 分页规则

#### 7.4 按钮组件规范
- 可见性规则
- 可用性规则
- 触发动作说明

### 8. 数据模型
- 相关数据模型清单
- 主要字段说明

### 9. 权限设计
- 操作用户角色定义
- 操作权限说明
- 数据权限说明

### 10. 影响分析
- 关联功能影响说明
- 外部系统影响说明
- 接口变更文档要求

### 11. 用户体验说明
- 重点操作说明
- 易用性考虑
- 特殊场景处理

### 12. 性能要求
- 并发用户数预估
- 数据量预估（初始/增长）
- 接口性能指标
- 性能测试要求

### 13. 异常处理
- 异常场景列举
- 处理方案说明

### 14. 数据处理
- 补录场景说明
- 数据修复方案
- 数据一致性校验规则

## 示例

<example>
### 功能点整体描述
用户注册功能：
- 业务意义：支持新用户自助注册系统账号
- 输入数据：用户基本信息（手机号、密码等）
- 输出数据：用户账号信息、验证状态

### 业务规则
手机号验证规则：
- 必填项：手机号、验证码
- 格式校验：11位数字，以1开头
- 错误提示：请输入正确的手机号码
- 验证码有效期：5分钟
</example>

<example type="invalid">
### 功能点整体描述
实现用户注册功能

### 业务规则
验证手机号
</example>

## 评审检查要点
1. 文档是否完整覆盖所有必需章节
2. 各项说明是否清晰具体
3. 是否包含必要的图示说明
4. 规则描述是否明确且可执行
5. 是否考虑了异常场景处理
6. 是否明确了性能指标要求