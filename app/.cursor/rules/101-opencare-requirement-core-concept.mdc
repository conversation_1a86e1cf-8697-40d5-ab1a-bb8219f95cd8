---
description:  用于生成具体业务需求文档的核心模型规则
globs: *.md,*.docx
alwaysApply: false
---
# 企业业务核心模型规则集

## 规则用途
本规则用于基于企业业务核心系统的核心数据模型,生成具体的业务需求文档。规则集中定义了各业务实体的具体属性、关系及约束条件。

## 核心业务实体规则

### 1. 产品模型规则

#### 1.1 产品分类规则
- 支持多层级分类树结构(至少5层)
- 每个产品必须属于一个主分类
- 支持产品多分类(一个产品可以属于多个分类)
- 支持分类属性继承
- 分类需包含:编码、名称、层级、上级分类、状态等属性

#### 1.2 产品属性规则
- 基本属性
  * 产品编码(唯一标识)
  * 产品名称(中文/英文)
  * 产品简介
  * 产品详情
  * 产品标签
  * 上架状态
  * 创建时间
  * 更新时间
  
- 技术属性
  * 度量单位(支持单位换算)
  * 规格参数(支持自定义)
  * 功能描述
  * 品质描述
  * 使用说明
  * 注意事项
  
- 供应属性
  * 供应商信息
  * 采购价格
  * 销售价格(支持多级价格)
  * 库存信息
  * 供货周期
  * 质保信息

#### 1.3 产品组合规则
- 支持多层级产品组合(套餐)
- 支持按数量组合
- 支持按比例组合
- 支持必选和可选组合
- 支持组合产品的价格规则
- 支持组合产品的库存规则
- 支持组合产品的交付规则

#### 1.4 产品具化规则
- 产品模板定义
  * 基础属性模板
  * 规格参数模板
  * 价格模板
  * 组合规则模板
  
- 产品变体生成
  * 支持批量生成
  * 支持规格组合
  * 支持价格调整
  * 支持库存分配
  
#### 1.5 产品版本管理规则
- 支持版本号管理
- 支持变更历史记录
- 支持版本回退
- 支持版本比对
- 支持版本状态管理

### 2. 客户模型规则

#### 2.1 客户基础信息规则
- 法人客户
  * 企业名称
  * 统一社会信用代码
  * 注册地址
  * 经营地址
  * 法定代表人
  * 联系人
  * 联系方式
  * 经营范围
  * 企业规模
  * 行业分类
  
- 个人客户
  * 姓名
  * 证件类型
  * 证件号码
  * 性别
  * 出生日期
  * 联系方式
  * 居住地址
  * 职业信息
  * 教育程度
  * 家庭状况

#### 2.2 客户关系规则
- 企业关系
  * 母子公司关系
  * 分支机构关系
  * 关联企业关系
  * 供应商关系
  * 合作伙伴关系
  
- 个人关系
  * 雇主关系
  * 家庭关系
  * 紧急联系人关系
  
- 混合关系
  * 企业法人关系
  * 企业股东关系
  * 企业员工关系

#### 2.3 客户分级分组规则
- 客户等级
  * 支持多维度评级
  * 支持自动升降级
  * 支持等级权益配置
  * 支持等级有效期管理
  
- 客户分组
  * 支持多维度分组
  * 支持组织架构分组
  * 支持区域分组
  * 支持标签分组
  * 支持自定义分组

#### 2.4 客户账户规则
- 账户类型
  * 基本账户
  * 保证金账户
  * 积分账户
  * 返利账户
  * 虚拟钱包账户
  
- 账户管理
  * 支持多币种
  * 支持账户冻结/解冻
  * 支持交易限额设置
  * 支持账户余额管理
  * 支持交易流水记录
  * 支持对账功能

### 3. 订单模型规则

#### 3.1 订单结构规则
- 总订单属性
  * 订单编号
  * 订单类型
  * 订单状态
  * 订单金额
  * 支付状态
  * 创建时间
  * 更新时间
  
- 子订单属性
  * 子订单编号
  * 关联总订单号
  * 子订单类型
  * 子订单状态
  * 子订单金额
  * 执行状态
  
- 订单项属性
  * 商品信息
  * 数量
  * 单价
  * 折扣信息
  * 优惠金额
  * 实付金额

#### 3.2 订单操作规则
- 订单创建
  * 支持在线下单
  * 支持批量下单
  * 支持预约下单
  * 支持代客下单
  
- 订单修改
  * 支持修改数量
  * 支持修改价格
  * 支持修改优惠
  * 支持修改收货信息
  
- 订单取消
  * 支持整单取消
  * 支持部分取消
  * 支持自动取消
  * 支持取消原因记录
  
- 订单退换
  * 支持整单退货
  * 支持部分退货
  * 支持换货处理
  * 支持退换原因记录

#### 3.3 套餐使用规则
- 使用方式
  * 支持整体使用
  * 支持分次使用
  * 支持指定使用
  * 支持预约使用
  
- 使用记录
  * 记录使用时间
  * 记录使用项目
  * 记录使用数量
  * 记录剩余额度
  
- 使用限制
  * 支持使用期限设置
  * 支持使用次数限制
  * 支持使用金额限制
  * 支持使用区域限制

#### 3.4 支付场景规则
- 直接销售
  * 支持直接收款
  * 支持分期付款
  * 支持定金支付
  * 支持尾款支付
  
- 代收付
  * 支持多级代收
  * 支持佣金计算
  * 支持结算周期设置
  * 支持代收限额设置
  
- 转卖
  * 支持价格调整
  * 支持库存管理
  * 支持佣金分成
  * 支持结算管理

#### 3.5 订单拆分规则
- 拆分条件
  * 按商品类型拆分
  * 按配送方式拆分
  * 按库存地拆分
  * 按销售方拆分
  
- 拆分处理
  * 支持自动拆分
  * 支持手动拆分
  * 支持拆分规则配置
  * 支持拆分后关联管理

### 4. 销售策略规则

#### 4.1 销售规则配置
- 价格规则
  * 支持常规价格
  * 支持会员价格
  * 支持渠道价格
  * 支持特殊价格
  * 支持阶梯价格
  
- 折扣规则
  * 支持商品折扣
  * 支持品类折扣
  * 支持会员折扣
  * 支持渠道折扣
  * 支持组合折扣
  
- 优惠规则
  * 支持满减优惠
  * 支持满赠优惠
  * 支持抵扣优惠
  * 支持积分优惠
  * 支持优惠叠加

#### 4.2 促销方式规则
- 促销类型
  * 限时促销
  * 限量促销
  * 套装促销
  * 组合促销
  * 预售促销
  
- 促销工具
  * 优惠券
  * 折扣券
  * 代金券
  * 积分
  * 返利
  
- 促销限制
  * 时间限制
  * 数量限制
  * 地区限制
  * 渠道限制
  * 客户限制

#### 4.3 客户关联规则
- 等级权益
  * 价格权益
  * 服务权益
  * 积分权益
  * 活动权益
  
- 分组权益
  * 专享价格
  * 专享服务
  * 专享活动
  * 专享权益

#### 4.4 合同协议规则
- 合同类型
  * 框架合同
  * 销售合同
  * 服务合同
  * 代理合同
  
- 合同管理
  * 合同创建
  * 合同审批
  * 合同变更
  * 合同终止
  
- 协议管理
  * 协议创建
  * 协议审批
  * 协议变更
  * 协议终止

### 5. 服务流程规则

#### 5.1 流程模板规则
- 模板定义
  * 节点定义
  * 路径定义
  * 规则定义
  * 表单定义
  
- 模板管理
  * 版本管理
  * 状态管理
  * 权限管理
  * 变更管理

#### 5.2 资源配置规则
- 资源类型
  * 人力资源
  * 设备资源
  * 场地资源
  * 物料资源
  
- 资源管理
  * 资源分配
  * 资源调度
  * 资源监控
  * 资源统计

#### 5.3 预约管理规则
- 预约方式
  * 在线预约
  * 电话预约
  * 现场预约
  * 代客预约
  
- 预约处理
  * 预约确认
  * 预约变更
  * 预约取消
  * 预约提醒

#### 5.4 服务履约规则
- 履约管理
  * 履约分配
  * 履约确认
  * 履约变更
  * 履约取消
  
- 质量管理
  * 服务标准
  * 质量检查
  * 满意度评价
  * 投诉处理

## 业务场景生成规则

### 1. 销售场景规则

#### 1.1 基础要素规则
- 客户信息
  * 基本信息
  * 联系方式
  * 账户信息
  * 信用信息
  
- 产品选择
  * 产品信息
  * 规格选择
  * 数量选择
  * 价格确认
  
- 定价策略
  * 价格体系
  * 折扣方案
  * 优惠政策
  * 结算方式
  
- 支付方式
  * 支付渠道
  * 支付方式
  * 支付限额
  * 支付安全

#### 1.2 销售模式规则
- 直销模式
  * 线上销售
  * 线下销售
  * 电话销售
  * 上门销售
  
- 代销模式
  * 渠道代销
  * 平台代销
  * 个人代销
  * 联营代销

#### 1.3 促销活动规则
- 活动类型
  * 折扣活动
  * 满减活动
  * 赠品活动
  * 组合活动
  
- 活动管理
  * 活动创建
  * 活动审核
  * 活动执行
  * 活动评估

#### 1.4 订单生命周期规则
- 订单创建
  * 信息录入
  * 库存检查
  * 价格计算
  * 订单确认
  
- 订单执行
  * 支付处理
  * 履约管理
  * 物流配送
  * 完成确认
  
- 订单变更
  * 修改管理
  * 取消管理
  * 退换管理
  * 售后管理

### 2. 服务场景规则

#### 2.1 基础要素规则
- 预约信息
  * 客户信息
  * 服务项目
  * 预约时间
  * 预约地点
  
- 资源配置
  * 人员安排
  * 设备配置
  * 场地安排
  * 物料准备
  
- 服务流程
  * 接待流程
  * 服务流程
  * 结算流程
  * 评价流程
  
- 服务评价
  * 满意度评价
  * 服务反馈
  * 投诉处理
  * 改进建议

#### 2.2 服务模式规则
- 标准服务
  * 服务内容
  * 服务标准
  * 服务价格
  * 服务流程
  
- 定制服务
  * 需求分析
  * 方案设计
  * 价格协商
  * 合同签订
  
- 组合服务
  * 服务组合
  * 价格组合
  * 资源组合
  * 流程组合

#### 2.3 服务质量规则
- 质量标准
  * 服务规范
  * 操作规范
  * 管理规范
  * 评价标准
  
- 质量控制
  * 过程监控
  * 质量检查
  * 问题处理
  * 持续改进

#### 2.4 服务补偿规则
- 补偿类型
  * 延时补偿
  * 质量补偿
  * 态度补偿
  * 其他补偿
  
- 补偿标准
  * 补偿条件
  * 补偿方式
  * 补偿金额
  * 处理流程

### 3. 结算场景规则

#### 3.1 支付方式规则
- 线上支付
  * 网银支付
  * 支付宝
  * 微信支付
  * 银联支付
  
- 线下支付
  * 现金支付
  * 刷卡支付
  * 支票支付
  * 转账支付
  
- 其他支付
  * 积分支付
  * 储值支付
  * 优惠券支付
  * 混合支付

#### 3.2 分期付款规则
- 分期方案
  * 期数设置
  * 首付比例
  * 利息计算
  * 还款计划
  
- 分期管理
  * 审核管理
  * 放款管理
  * 还款管理
  * 逾期管理

#### 3.3 退款规则
- 退款类型
  * 订单退款
  * 部分退款
  * 预付退款
  * 差额退款
  
- 退款流程
  * 申请处理
  * 审核确认
  * 退款执行
  * 结果通知

#### 3.4 发票规则
- 发票类型
  * 增值税普票
  * 增值税专票
  * 电子发票
  * 其他发票
  
- 发票管理
  * 开票申请
  * 开票审核
  * 发票开具
  * 发票寄送

## 文档生成规则

### 1. 需求分类规则

#### 1.1 业务域分类
- 销售域
  * 客户管理
  * 产品管理
  * 订单管理
  * 价格管理
  
- 服务域
  * 预约管理
  * 服务管理
  * 评价管理
  * 投诉管理
  
- 结算域
  * 支付管理
  * 退款管理
  * 发票管理
  * 对账管理

#### 1.2 功能模块分类
- 基础模块
  * 客户管理
  * 产品管理
  * 订单管理
  * 账户管理
  
- 业务模块
  * 销售管理
  * 服务管理
  * 结算管理
  * 统计分析
  
- 支撑模块
  * 系统管理
  * 权限管理
  * 日志管理
  * 报表管理

#### 1.3 优先级分类
- 核心功能
  * 必须实现
  * 高优先级
  * 基础依赖
  
- 扩展功能
  * 可选实现
  * 低优先级
  * 独立功能

### 2. 需求描述规则

#### 2.1 内容要素
- 业务背景
  * 业务场景
  * 业务目标
  * 业务价值
  * 业务规模
  
- 功能描述
  * 功能定义
  * 功能范围
  * 功能特点
  * 功能限制
  
- 业务规则
  * 处理规则
  * 计算规则
  * 校验规则
  * 控制规则
  
- 操作流程
  * 操作步骤
  * 操作角色
  * 操作界面
  * 操作提示

#### 2.2 描述规范
- 术语定义
  * 术语解释
  * 使用场景
  * 相关说明
  
- 场景示例
  * 典型场景
  * 特殊场景
  * 异常场景
  
- 关联关系
  * 上下游关系
  * 依赖关系
  * 制约关系

### 3. 验收标准规则

#### 3.1 功能验收
- 功能完整性
  * 功能覆盖
  * 场景覆盖
  * 规则覆盖
  
- 功能正确性
  * 处理正确
  * 计算准确
  * 结果一致

#### 3.2 性能指标
- 响应时间
  * 页面加载
  * 数据处理
  * 交易处理
  
- 并发能力
  * 用户数量
  * 交易数量
  * 数据量

#### 3.3 数据要求
- 数据准确性
  * 数据完整
  * 数据一致
  * 数据及时
  
- 数据安全性
  * 访问控制
  * 数据加密
  * 操作审计

#### 3.4 异常处理
- 异常识别
  * 异常类型
  * 异常原因
  * 异常影响
  
- 处理方案
  * 处理流程
  * 处理规则
  * 处理结果

## 注意事项
1. 需求文档需遵循业务核心模型的基本原则
2. 保持需求描述的完整性和一致性
3. 考虑业务场景的多样性和复杂性
4. 关注不同业务实体间的关联关系
5. 预留系统的扩展性和灵活性
6. 确保规则的可执行性和可验证性
7. 注重规则的可维护性和可扩展性
8. 保证规则覆盖所有关键业务场景
9. 关注规则之间的依赖和冲突关系
10. 定期评估和更新规则的有效性 