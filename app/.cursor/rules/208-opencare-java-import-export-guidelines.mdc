---
description: 
globs: 
alwaysApply: false

---

```
name: "output/input-guidelines"
description: "导入/导出代码编写规范"
pattern: "**/*.java"
```

# 导入代码编写规范

## 一、导包路径规范

* ExcelImportService：com.open_care.service.imports.ExcelImportService
* ExcelUtils：com.open_care.utils.ExcelUtils
* BackgroundTaskService：com.open_care.service.imports.BackgroundTaskService
* OCBackgroundTasks：com.open_care.app.OCBackgroundTasks
* OCBackgroundTasksRepository：com.open_care.repository.OCBackgroundTasksRepository
* AsyncExcelImportService：com.open_care.service.imports.AsyncExcelImportService



## 二、导入代码编写步骤

### 1. 创建XxxImportDTO

* 根据需要导入的字段创建XxxImportDTO，放在open-care-app-server/src/main/java/com/open_care/dto路径下

* 非空字段需要加上@NotBlank注解

```java
// 模板示例，请替换其中的xxx为具体值
@Data
public class XxxImportDTO {
    @NotBlank(message = "文件id不允许为空，请上传文件后导入")
    public String fileId;
    
   //其他字段
   ...
}
```

### 2.定义接口

* 在open-care-app-server/src/main/java/com/open_care/controller对应子包下创建controller。
* 在controller中注入xxxImportService，并调用xxxImportService.xxxImport方法。

* 请求方法加上`@PostMapping("/XxxImport")`注解。

```java
// 模板示例，请替换其中的xxx为具体值
@Autowired
private XxxImportService XxxImportService;

@PostMapping("/XxxImport")
public OcResponse XxxImport(@RequestBody XxxImportDTO importDTO) {
    Preconditions.checkArgument(StrUtil.isNotBlank(importDTO.fileId), "请上传文件后导入");
    return xxxImportService.xxxImport(importDTO);
}
```



### 3. 创建XxxImportService

* 放在open-care-app-server/src/main/java/com/open_care/service/imports路径下。
* 加上`@Service`注解，继承`ExcelImportService`类
* 实现`xxxImport`方法

```java
// 模板示例，请替换其中的xxx为具体值
@Autowired
private AsyncImportFastTrackService asyncImportXxxService;

public static final Log log = LogFactory.get();

@Autowired
private ExcelUtils excelUtils;

@Transactional
public OcResponse xxxImport(XxxImportDTO importDTO) {
    log.info("import xxx start. file path is {}", webServerUrl + importDTO.fileId);

    // 读取文件
    String filePath = downloadFile(importDTO);
    List<Map<String, Object>> xxxData = ExcelUtils.readSheetFromFilePath(filePath);

    // 记录后台任务的开始时间
    OCBackgroundTasks backgroundTasks = persistBackgroundTask("xxxxx", filePath);

    // 异步调用执行导入逻辑
    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
        @Override
        public void afterCommit() {
            asyncImportXxxkService.xxxImport(xxxData, backgroundTasks.getOcId(), importDTO);
        }
    });

    return ResponseUtil.getResponseSuccess("导入开始");
}
```



### 4. 创建AsyncImportXxxService

* 放在open-care-app-server/src/main/java/com/open_care/service/imports路径下

* 加上`@Service`注解实现`AsyncExcelImportService`接口
* 实现`xxxImport`方法

```java
// 模板示例，请替换其中的xxx为具体值
@Service
public class AsyncImportXXXService extends AsyncExcelImportService {
    @Autowired
    private BackgroundTaskService backgroundTaskService;

    @Autowired
    private OCBackgroundTasksRepository backgroundTasksRepository;

    @Autowired
    private XxxService xxxService;

    public static final Log log = LogFactory.get();

    @Transactional(rollbackFor = Exception.class)
    @Async
    public void xxxImport(List<Map<String, Object>> importData, String backgroundTaskId, XXXImportDTO importDTO) {
        OCBackgroundTasks backgroundTasks = backgroundTasksRepository.getById(backgroundTaskId);
        
        ImportContextDTO contextDTO = initXXXImportContext(importDTO);

        try {
            List<Boolean> result = importData.stream()
                    .map(row -> processSingleRecord(row, backgroundTask, contextDTO))
                    .collect(Collectors.toList());

            parseResultAndSendNotifyMessage(importData, result, backgroundTask);

        } catch (Exception e) {
            log.error("XXX导入失败。error:", e);
            backgroundTaskService.updateBackgroundTask(backgroundTasks, "导入失败：" + e.getMessage(), ERROR);
        }
    }

    // 初始化方法（需填充业务逻辑）
    private ImportContextDTO initXXXImportContext(XXXImportDTO importDTO) {
        ImportContextDTO context = new ImportContextDTO();
     // 示例：importContextDTO.setProduct(productAppService.getEntity(OCServiceProduct.class, importDTO.getProductId(), StrUtil.EMPTY));
        return context;
    }

    // 单条记录处理（将单条原始数据转换为业务对象，执行校验后保存至数据库，并处理可能的异常。）
    private Boolean processSingleRecord(Map<String, Object> row, 
                                      XXXBackgroundTask task,
                                      ImportContextDTO context) {
        Map<String, Object> transformedRowData = row.keySet().stream().collect(
        Collectors.toMap(v -> MapUtil.getMappingColumnName(v, fastTrackLaneInfoColumn), v -> removeSpecialChar(row.get(v)))
        );
        //后续操作
        ...
    }
```



## 三、导出代码编写步骤

### 1. 创建XxxExportDTO

* 根据需要导出的字段创建XxxExportDTO，放在open-care-app-server/src/main/java/com/open_care/dto路径下

```java
// 模板示例，请替换其中的xxx为具体值
@Getter
@Setter
public class XxxExportDTO implements IBaseDTO {

    @OcColumn(title = "xxx")
    private String xxx;
    
    //其他字段
    ...
}
```



### 1.定义接口

* 在open-care-app-server/src/main/java/com/open_care/controller对应子包下创建controller。
* 在controller中注入xxxExportService，并调用xxxExportService.exportXxx方法。

* 请求方法加上`@PostMapping(value = "/export/xxx")`注解。

```java
// 模板示例，请替换其中的xxx为具体值
@Autowired
XxxExportService xxxExportService;

@PostMapping(value = "/export/xxx")
public OcResponse exportXxx(@RequestBody QueryRequestDTO queryRequestDTO) {
    return ResponseUtil.getResponseSuccess(MapUtil.of("file",
                    xxxExportService.exportXxx(queryRequestDTO)
            )
    );
}
```



### 2. 创建XxxExportService

* 放在open-care-app-server/src/main/java/com/open_care/service/export路径下。
* 加上`@Service`注解
* 实现`exportXxx`方法

```JAVA
// 模板示例，请替换其中的xxx为具体值
@Service
public class XxxExportService {
    @Autowired
    FastdfsService fastdfsService;
    
    // 替换实际数据服务
    @Autowired
    XXXDataService dataService;
    
    @Autowired
    ExcelUtils excelUtils;

    public String exportXxx(QueryRequestDTO queryRequestDTO) {
        List<XxxExportDTO> xxxDTOS = queryExportData(queryRequestDTO);

        return excelUtils.fillExcelDataWithTemplate(
                xxxDTOS,
                "xxx",
                "template/fillTemplate/xxx.xlsx"
        );
    }
    
    private List<TravelDTO> queryExportData(QueryRequestDTO queryRequestDTO) {
        QueryResponseDTO<XxxDTO> xxxDTOQueryResponseDTO =
                dataService.query(queryRequestDTO);

        return xxxDTOQueryResponseDTO.getData();
    }
}
```

