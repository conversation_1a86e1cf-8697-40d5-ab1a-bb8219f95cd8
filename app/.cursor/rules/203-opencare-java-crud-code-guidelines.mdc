---
description: 
globs: 
alwaysApply: false
---
---
name: "crud-code-guidelines"
description: "CRUD代码编写规则"
pattern: "**/*.java"

---

# CRUD代码编写规则

> 项目jdk版本为17版本


### 实体/DTO类定义
- 字段上都必须加上@OcColumn(title = "")注解
- 字段如果有默认值，必须添加e @lombok.Builder.Default 注解,使用简称
- 类上必须有 `@com.open_care.annotation.OcClass(title = "")` 注解，title 为类的描述，使用简称
- 类上必须有以下为注解，使用注解时使用简称
  - `lombok.experimental.FieldNameConstants`
  - `lombok.Data`
  - `lombok.NoArgsConstructor`
  - `lombok.AllArgsConstructor` 如果没有定义属性则去掉
  - `lombok.experimental.SuperBuilder`
  - `lombok.EqualsAndHashCode(callSuper = true)` 如果没有父类则去掉
- 日期字段需额外加`@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")`,`@com.google.gson.annotations.JsonAdapter(OCDateTypeAdapter.class)`
- 时间字段需额外加`@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")`,`@com.google.gson.annotations.JsonAdapter(OCDateTimeTypeAdapter.class)`
- 实体和DTO对应的属性应该保持名称一致

##### 实体类定义规则
- 实体放置位置：
  - app微服务实体放在`open-care-app-entity/src/main/java/com/open_care`路径
  - 其他微服务实体放在`open-care-app-entity-micro-annotated/src/main/java/com/open_care`路径
- 实体命名和注解规则：
  - 实体字段必须加`@OcColumn(title = "")`注解
  - 需要创建表的实体命名必须以OC开头、必须直接或者间接继承`com.open_care.sys.OCBase`基类
  - 不需要创建表/希望以json格式存储的类必须以Json结尾，实现 `com.open_care.core.IBaseJsonObject.IBaseJsonObject`接口

##### DTO定义规则
- DTO放在`open-care-app-server/src/main/java/com/open_care/dto`路径
- DTO 必须以DTO结尾
- 如果DTO对应的实体继承 `com.open_care.sys.OCBase` 则继承 `com.open_care.dto.BaseEntityDTO`
- 所有DTO都必须直接或者间接实现com.open_care.dto.IBaseDTO接口

#### 枚举定义规则
- 枚举以Enum结尾
- 枚举必须实现 `com.open_care.core.IShowLabelEnum`


```java
import com.open_care.dto.IBaseDTO;
@Data
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
public class TravelInfoDemoDTO implements IBaseDTO {
    @OcColumn(title = "ocId")
    String ocId;
    
    //其他字段
}

```

## Controller规则

- 自定义请求Controller放在`open-care-app-server/src/main/java/com/open_care/controller`路径
- 与前端交互必须为DTO而非实体，返回值必须为`OCResponse`对象
- 类必须加`@RestController`、`@RequestMapping`注解（BaseController除外）
- 路径参数必须加`@PathVariable`注解
- 请求体参数必须加`@RequestBody`注解
- 业务逻辑禁止写在Controller里

## Service规则

- Service放在`open-care-app-server/src/main/java/com/open_care/service/xxx`路径
- Service编写具体业务逻辑
- Service必须注入对应实体的CrudService
- Service必须编写save、query、get、delete方法

```java
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.common.delete.DeleteParamRequest;
import com.open_care.api.common.dto.crud.GetRequestDTO;
import com.open_care.api.common.dto.crud.QueryRequestDTO;
import com.open_care.api.common.dto.crud.SaveRequestDTO;
import com.open_care.api.common.edit.FieldData;
import com.open_care.dto.QueryResponseDTO;
import com.open_care.dto.travelInfo.TravelInfoDemoDTO;
import com.open_care.enums.DbOperator;
import com.open_care.util.GraphqlUtil;
import com.open_care.util.GsonUtils;
import com.open_care.util.ResponseUtil;
import com.open_care.utils.BeanUtils;
import com.open_care.utils.CrudUtils;
import com.open_care.utils.LambdaUtils;
import com.open_care.utils.QueryUtils;
import com.open_care.service.crud.EditDataResponse;
import org.springframework.transaction.annotation.Transactional;
@Service
public class CustomerInfoService {
    @Autowired
    private CustomerInfoCrudService customerInfoCrudService;
}
```

### CrudService类

- app微服务实体的CrudService继承`AbstractAppLocalEntityCrudService`
- 其他微服务实体的CrudService继承`AbstractAppMicroEntityCrudService`
- 必须重写(override)`getAppMicroService()`和`entityClass()`这两个方法
- CrudService实现数据层交互，不编写业务逻辑

```java
import com.open_care.service.crud.AbstractAppLocalEntityCrudService;
import com.open_care.service.microServices.microServicesImpl.ApplicationService;
@Service
@Getter
public class CustomerInfoCrudService extends AbstractAppLocalEntityCrudService<OCCustomerInfo> {

    @Autowired
    private ApplicationService applicationService;

    @Override
    public ApplicationService getAppMicroService() {
        return applicationService;
    }

    @Override
    public Class<OCCustomerInfo> entityClass() {
        return OCCustomerInfo.class;
    }
}
```

### Save方法

- 当dto中的ocId为空，表示插入数据。dto中的ocId不为空，表示修改数据

 - 入参必须是SaveRequestDTO
 - 根据需求增加相关的校验函数
 - 将DTO变换为Entity，调用CrudService的save方法进行保存

```java
 public OcResponse<CustomerInfoDTO> saveCustomerInfo(SaveRequestDTO saveRequestDTO) {
        Map saveRequestDTOMap = GraphqlUtil.recursiveMapWithPoundSign(saveRequestDTO.getData());

        CustomerInfoDTO customerInfoDTO = GsonUtils.mapToObject(saveRequestDTOMap, CustomerInfoDTO.class);

        // 校验必填字段
        validateRequiredFields(customerInfoDTO);
        
        // 校验证件号码唯一性
        validateIdNumberUnique(customerInfoDTO);

        // 转换为实体并保存
        OCCustomerInfo entity = convertToEntity(customerInfoDTO);
     
      //保存数据
      OCCustomerInfo saveEntity = customerInfoCrudService.save(entity);
     
      // 将返回的实体转换为DTO
      CustomerInfoDTO saveCustomerInfoDTO = convertToDTO(saveEntity);
     
        return ResponseUtil.getResponseSuccess(saveCustomerInfoDTO, "保存成功");
    }
```

### Query方法

 - 入参必须是QueryRequestDTO
 - 调用CrudService的query进行查询，并对返回的QueryResponseDTO<T>进行处理，将其中的实体变换为DTO

```java
  public OcResponse<QueryResponseDTO<CustomerInfoDTO>> queryCustomerInfo(QueryRequestDTO queryRequestDTO) {
        queryRequestDTO.setEntityName(OCCustomerInfo.class.getName());
      
      //查询数据
    QueryResponseDTO<CustomerInfoDTO> queryResponseDTO = customerInfoCrudService.query(queryRequestDTO, this::convertDTO);
        
        return CrudUtils.packageAsOCResponse(queryResponseDTO);
    }
```

### Get方法

 - 入参必须是GetRequestDTO getRequestDTO, String id
 - 调用CrudService的方法Get获取实体，然后将实体变换为DTO

```java
  public OcResponse<EditDataResponse<CustomerInfoDTO>> getCustomerInfo(GetRequestDTO getRequestDTO, String id) {
      
        //查询数据
        OCCustomerInfo entity = customerInfoCrudService.get(id, getRequestDTO.getEagerProperties());
    
        //转换为DTO返回
        return CrudUtils.packageAsEditOCResponse(convertDTO(entity));
    }
```

### 批量保存方法

- 入参必须是 List<SaveRequestDTO>
- 需要将 DTO 列表转换为 Entity 列表
- 返回保存后的 DTO 列表

```java
@Transactional(rollbackFor = Exception.class)
public List<CustomerInfoDTO> batchSaveCustomerInfo(BatchSaveDTO batchSaveDTO) {
    List<CustomerInfoDTO> dtoList = batchSaveDTO.getData().stream()
            .map(iter->GsonUtils.mapToObject(CrudUtils.recursiveMapWithPoundSign(iter.getData()), CustomerInfoDTO.class))
            .collect(Collectors.toList());
    
    //TODO 其他操作
    
    // 转换为实体并保存
    List<OCCustomerInfo> entityList = travelInfoCrudService.saveAll(dtoList.stream()
            .map(this::convertToEntity)
            .collect(Collectors.toList()));
    
    // 转换为DTO并返回
    return CrudUtils.packageAsOCResponse(entityList.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList()));
}
```

### Delete方法

 - 入参必须是DeleteParamRequest deleteParamRequest

```java
@Transactional(rollbackFor = Exception.class)
public OcResponse deleteCustomerInfo(DeleteParamRequest deleteParamRequest) {
    
    //删除数据 
    deleteParamRequest.getEntityInstIds()
        .foreach(customerInfoCrudService::deleteById);
    
    return ResponseUtil.getResponseSuccess("删除行程成功");
}
```

## BaseController规则

- BaseController在com.open_care.controller包下
- Service类需要在BaseController中注入
- Service中的save，delete，query，get，batchSave方法需要在init方法中插入到对应的map集合

```java
public class BaseController extends AppBaseController {
    //其他注入的service
    
    @Autowired
    CustomerInfoService customerInfoService;
    
    @PostConstruct
    public void init() {
      //其他代码
        
        saveOperationMap.put(CustomerInfoDTO.class.getName(), customerInfoService::saveCustomerInfo);
        queryOperationMap.put(CustomerInfoDTO.class.getName(), customerInfoService::queryCustomerInfo);
        getOperationMap.put(CustomerInfoDTO.class.getName(), customerInfoService::getCustomerInfo);
        deleteOperationMap.put(CustomerInfoDTO.class.getName(), customerInfoService::deleteCustomerInfo);
    }
}

```


## 数据校验规范

- 必填字段校验应单独封装为 validateRequiredFields 方法
- 唯一性校验应单独封装为 validateXXXUnique 方法
- 校验方法应在 save 方法中调用
- 校验失败应抛出明确的异常信息new IllegalArgumentException("")

```java
private void validateRequiredFields(CustomerInfoDTO dto){
    if(dto == null){
        throw new IllegalArgumentException("参数为空");
    }
    //其他字段校验
}
```

## DTO 与 Entity 转换规范

- 每个 Service 应提供 convertToDTO 和 convertToEntity 方法
- 转换方法应处理空值情况
- 推荐使用 BeanUtils.copyProperties 进行属性复制
- 复杂属性转换需单独处理

```java
public CustomerInfoDTO convertToDTO(OCCustomerInfo entity) {
    if (entity == null) {
        return null;
    }
    CustomerInfoDTO dto = new CustomerInfoDTO();
    BeanUtils.copyProperties(entity, dto);
    // 处理复杂属性
    return dto;
}

public OCCustomerInfo convertToEntity(CustomerInfoDTO dto) {
    if (dto == null) {
        return null;
    }
    OCCustomerInfo entity = new OCCustomerInfo();
    BeanUtils.copyProperties(dto, entity);
    // 处理复杂属性
    return entity;
}
```


## 查询构建器使用规范

- 查询使用 QueryRequestDTO 构建查询条件
- 查询构建器应指定实体类名和过滤条件
- 过滤条件使用com.open_care.enums.DbOperator枚举类

```java
    EQ("equal", "100", "等于"),
    NE("notEqual", "200", "不等于"),
    GT("greater", "300", "大于"),
    GTE("greaterOrEqual", "400", "大于等于"),
    LS("less", "500", "小于"),
    LSE("lessOrEqual", "600", "小于等于"),
    IN("in", "700", "包含"),
    NOT_IN("notIn", "800", "不包含"),
    LIKE("like", "900", "模糊查询"),
    LEFT_LIKE("leftLike", "1000", "左模糊查询"),
    RIGHT_LIKE("rightLike", "1100", "右模糊查询"),
    IS_NULL("isNull", "1200", "为空"),
    NOT_NULL("isNotNull", "1300", "不为空"),
    BETWEEN("between", "1400", "范围"),
    IS_NOT("isNot", "1500", "不是(true/false)");
```

```java
List<FieldData> fieldData = new ArrayList<>();
fieldData.add(QueryUtils.fieldData(LambdaUtils.fieldName(OCCustomerInfo::getOcId),
    DbOperator.EQ,
    ocCustomerInfo.getOcId()));
QueryRequestDTO queryRequestDTO = new QueryRequestDTO();
queryRequestDTO.setFilters(fieldData);
queryRequestDTO.setIgnoreAuthorityCheck(true);
OCCustomerInfo queryData = travelInfoCrudService.query(queryRequestDTO).getData();
```

## 常用类

### QueryResponseDTO

```java
public class QueryResponseDTO<T> {
    List<T> data;
    Page pagination;
    List<Map<String, Object>> authority;
    Map<String, Number> summary;
    public List<T> getData() {
        return this.data;
    }
}
```

## 通过MapStruct处理Entity与DTO的变换规则

### 适用范围

适用于 `com.open_care.mapper` 及其所有子包下的 Mapper 接口代码自动生成。

### 1. 文件与接口命名

- 文件名与接口名均为 `{业务对象}Mapper.java`，如 `EnterpriseCustomerMapper.java`。
- 包名需与业务领域一致，放于 `com.open_care.mapper.{子包}`。

---

### 2. 注解与结构

- 必须添加 `@Mapper` 注解，推荐配置如下：

    ```java
    @Mapper(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true),
        uses = {其他Mapper.class} // 如有需要
    )
    ```

- 必须定义 `INSTANCE` 静态字段：

    ```java
    {接口名} INSTANCE = Mappers.getMapper({接口名}.class);
    ```

---

### 3. 方法规范

- 必须包含 DTO 与 Entity 之间的双向转换方法：

    ```java
    @Mapping(target = "xxx", ignore = true) // 如有特殊字段
    目标类型 toEntity(源DTO类型 dto);

    @Mapping(target = "xxx", ignore = true) // 如有特殊字段
    源DTO类型 toDTO(目标类型 entity);
    ```

- 如有特殊转换逻辑，需用 `@AfterMapping` 或 `@BeforeMapping` 注解的 default 方法补充。

---

### 4. 依赖与辅助

- 如需避免循环依赖，可引入 `CycleAvoidingMappingContext`，并在方法参数中添加 `@Context CycleAvoidingMappingContext context`。
- 如需调用其他 Mapper，需在 `@Mapper` 注解的 `uses` 属性中声明。

---

### 5. 典型模板

```java
package com.open_care.mapper.{子包};

import com.open_care.{entity包}.{Entity};
import com.open_care.dto.{dto包}.{DTO};
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(disableBuilder = true),
    uses = {其他Mapper.class} // 如有需要
)
public interface {Entity}Mapper {
    {Entity}Mapper INSTANCE = Mappers.getMapper({Entity}Mapper.class);

    @Mapping(target = "xxx", ignore = true) // 如有特殊字段
    {Entity} toEntity({DTO} dto);

    @Mapping(target = "xxx", ignore = true) // 如有特殊字段
    {DTO} toDTO({Entity} entity);

    // 如有特殊处理
    @AfterMapping
    default {DTO} afterConvertToDTO({Entity} entity, @MappingTarget {DTO} dto) {
        // 自定义处理
        return dto;
    }
}
```
