---
description: 根据需求描述，生成需求大纲文档
globs: 
alwaysApply: false
---
---
name: Requirement Analysis Template
description: 需求分析和文档结构化模板规则
globs: ["**/requirements/*.md", "**/docs/requirements/*.md"]
alwaysApply: false
---

# 需求分析规则

当进行需求分析和文档编写时，请严格遵循以下结构和检查清单进行梳理：

## 文档结构

### 1. 需求概述
- 需求背景
- 目标用户
- 预期收益
- 关键功能点

### 2. 详细说明

#### 2.1 整体描述
- [ ] 用简洁清晰的语言描述需求内容
- [ ] 明确需求的边界和范围
- [ ] 说明与现有系统的关系

#### 2.2 业务流程
- [ ] 使用流程图描述主要业务流程
- [ ] 标注流程中的关键节点和判断条件
- [ ] 说明各个环节的输入输出

#### 2.3 用例/场景
- [ ] 描述典型用例（至少3个）
- [ ] 每个用例包含：
  * 前置条件
  * 操作步骤
  * 预期结果
  * 验收标准

#### 2.4 功能模块
- [ ] 模块名称和功能概述
- [ ] 页面原型或界面交互说明
- [ ] 详细的业务规则和校验规则
- [ ] 数据结构和字段说明

#### 2.5 角色权限
- [ ] 列举涉及的所有用户角色
- [ ] 详细的权限矩阵：
  * 页面访问权限
  * 操作权限
  * 数据范围权限
  * 审批权限（如适用）

#### 2.6 异常处理
- [ ] 列举可能的异常场景
- [ ] 每个异常场景的处理流程
- [ ] 错误提示和用户引导

#### 2.7 待确认事项
- [ ] 列出所有待确认的问题
- [ ] 标注优先级和影响范围
- [ ] 记录需要与哪些相关方确认

## 验证清单

在完成需求文档后，请确保：
1. 所有章节都已完整填写
2. 业务流程图清晰完整
3. 用例覆盖了主要场景
4. 异常处理机制完备
5. 权限设计合理
6. 待确认事项已标注清楚

## 输出格式
- 使用 Markdown 格式
- 图表推荐使用 mermaid 语法
- 保持层级结构清晰
- 使用检查框标注完成状态