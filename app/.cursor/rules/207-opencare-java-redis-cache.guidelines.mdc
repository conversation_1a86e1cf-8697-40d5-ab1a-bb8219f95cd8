---
description: 
globs: 
alwaysApply: false
---

```
name: "redis-cache-guidelines"
description: "Redis缓存代码编写规范"
pattern: "**/*.java"
```



# Redis缓存注解使用规则

## @CacheConfig

* @CacheConfig作用于类，使用`cacheNames`属性指定“缓存空间”。
* 命名规范：类名前缀+"Cache"。

```java
//模板示例
@CacheConfig(cacheNames = "dataAuthorityBOCache")
public class DataAuthorityBOService {...}

@CacheConfig(cacheNames = "sysSettingCache")
public class SysSettingService {...}
```



## @Cacheable

* @Cacheable作用于查询方法，需要指定key（String）属性。
* 命名规范：方法后缀+参数。

```java
//模板示例
@Cacheable(key = "'byId:' + #id")
public OCSysSetting getById(String id) {...}

@Cacheable(key = "'byParamName:' + #paramName")
public OCSysSetting getByParamName(String paramName) {...}
```



## @Cacheput

* @Cacheput作用于添加、更新方法，需要指定key（String）、unless（String）属性。
* 命名规范：key：需要更新的缓存名称+#result.ocId，unless："#result == null"

```java
//模板示例
@Caching(
        put = {
                @CachePut(
                        key = "'byId:' + #result.ocId",
                        unless = "#result == null"
                ),
                @CachePut(
                        key = "'byParamName:' + #result.paramName",
                        unless = "#result == null"
                )
        }
)
public OCSysSetting save(OCSysSetting sysSetting) {...}
```



## @CacheEvict

* @CacheEvict作用于删除方法，需要指定key（String）、unless（String）属性。
* 命名规范：key：需要删除的缓存数据

```java
//模板示例
@Caching(
        evict = {
                @CacheEvict(
                        key = "'byId:' + #id"
                ),
                @CacheEvict(
                        key = "'queryList:OCMedicalDepartmnet'"
                )
        }
)
public OCMedicalDepartment deleteById(String id) {...}
```



## @Caching

* Caching注解用于在同一个方法上组合使用Cacheable 、CacheEvict、CachePut的情况。

```java
//模板示例
@Caching(
        evict = {
                @CacheEvict(
                        key = "'byId:' + #id"
                ),
                @CacheEvict(
                        key = "'queryList:OCMedicalDepartmnet'"
                )
        }
)
public OCMedicalDepartment deleteById(String id) {...}

@Caching(
        put = {
                @CachePut(
                        key = "'byId:' + #result.ocId",
                        unless = "#result == null"
                ),
                @CachePut(
                        key = "'byParamName:' + #result.paramName",
                        unless = "#result == null"
                )
        }
)
public OCSysSetting save(OCSysSetting sysSetting) {...}
```





# Redis缓存代码生成步骤

1. 在open-care-app-server/src/main/java/com/open_care/service下创建`XxxCacheService`类
2. 加上`@Service`，`@CacheConfig`

3. 使用`@Autowired`注入上下文中的业务Service或CrudService类
4. 为每一个需要缓存的方法创建一个新的缓存方法并在缓存方法上加上对应注解。
5. 通过调用注入业务Service或CrudService类的方法完成缓存。





# 注意事项

* 缓存key可以使用参数或者返回值
* Cacheable 的key不能与返回值相关
* 使用缓存需要保证一致性：
  * 更新数据库后需要将对应的缓存更新或者使其失效。
  * key的表达式应该合理，对于同一个方法，就算参数不完全一致，只要根据key的表达式构造出的key相同，那么就会返回这个key对应的缓存，如果key不合理，会导致查询到错误的数据。

