---
description: TRANSFORM when ANALYZING_UI_REQUIREMENTS to CREATE_OPENCARE_UI_SPECIFICATIONS
globs: docs/requirements/**/*.md
---

# Open-Care UI需求分析与转换规则

<version>1.0.0</version>

## Context
- 当需要将业务需求转换为Open-Care平台的UI规格说明时应用此规则
- 用于分析UI需求并生成符合Open-Care平台规范的UI组件规格
- 适用于从需求文档到UI JSON文件的转换过程

## Requirements

### 需求分析步骤
- 识别UI窗口的主要功能和目的
- 确定所需的数据实体和字段
- 识别UI组件类型和层次结构
- 确定组件间的交互逻辑和事件处理
- 分析数据流和业务规则

### 窗口类型识别
- 查询窗口：主要包含查询条件和结果展示，通常使用Form+Table组合
- 表单窗口：主要用于数据录入和编辑，以Form为主
- 详情窗口：主要用于数据展示，可使用Form（只读）或其他展示组件
- 复合窗口：包含多个功能区域，可能包含多个Form和Table

### 组件映射规则
- 文本输入：使用Input组件，type="text"
- 数字输入：使用Input组件，type="number"
- 日期选择：使用DatePicker组件
- 时间选择：使用TimePicker组件
- 下拉选择：使用Select组件
- 单选框：使用Radio组件
- 复选框：使用Checkbox组件
- 文本区域：使用TextArea组件
- 数据表格：使用Table组件
- 按钮：使用Button组件

### 布局规则
- 使用Row和Col组件进行栅格布局
- 表单项通常使用4列布局（displayColNum="4"）
- 表单标签通常占6格（labelCol={"span": 6}）
- 表单控件通常占18格（wrapperCol={"span": 18}）
- 操作按钮通常放在窗口底部或顶部

### 数据绑定规则
- 每个表单组件必须绑定到特定的数据字段（fieldId）
- 表单必须绑定到特定的数据实体（entityDefId）
- 表格必须绑定到特定的数据实体（entityDefId）
- 下拉选择等需要选项的组件必须定义options数组

### 事件处理规则
- 窗口加载时通常需要定义onCreate事件
- 按钮点击通常需要定义onClick事件
- 表单提交通常使用save动作
- 数据查询通常使用query动作
- 窗口打开通常使用openWindow动作
- 复杂逻辑通常使用jsAction动作

### 命名规范
- 组件ID使用驼峰命名法，如userInfoForm
- 组件名称应具有描述性，反映其功能
- 相关组件应使用一致的前缀，如userForm, userTable
- 临时或辅助组件可使用数字后缀，如Row1, Col1

## Examples

<example>
需求描述转换示例：

需求描述：
```
创建一个客户信息查询窗口，包含以下功能：
1. 查询条件：客户姓名、手机号码、客户类型（个人/企业）
2. 查询结果表格显示：客户ID、姓名、手机号码、客户类型、创建时间
3. 操作按钮：查询、重置、新增客户
4. 表格行操作：查看详情、编辑
```

转换为UI规格：
```json
{
  "title": "客户信息查询",
  "type": "pc",
  "componentType": "Window",
  "componentChildren": [
    {
      "componentType": "Form",
      "entityDefId": "customerEntity",
      "searchForm": true,
      "componentChildren": [
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 6,
              "componentChildren": [
                {
                  "componentType": "Input",
                  "fieldId": "name",
                  "placeholder": "请输入客户姓名",
                  "additionalProperties": {
                    "label": "客户姓名"
                  }
                }
              ]
            },
            {
              "componentType": "Col",
              "span": 6,
              "componentChildren": [
                {
                  "componentType": "Input",
                  "fieldId": "mobile",
                  "placeholder": "请输入手机号码",
                  "additionalProperties": {
                    "label": "手机号码"
                  }
                }
              ]
            },
            {
              "componentType": "Col",
              "span": 6,
              "componentChildren": [
                {
                  "componentType": "Select",
                  "fieldId": "customerType",
                  "placeholder": "请选择客户类型",
                  "options": [
                    {"label": "个人", "value": "personal"},
                    {"label": "企业", "value": "enterprise"}
                  ],
                  "additionalProperties": {
                    "label": "客户类型"
                  }
                }
              ]
            },
            {
              "componentType": "Col",
              "span": 6,
              "buttonBox": true,
              "buttonAlign": "right",
              "componentChildren": [
                {
                  "componentType": "Button",
                  "title": "查询",
                  "type": "primary",
                  "events": [
                    {
                      "eventName": "onClick",
                      "actions": [
                        {
                          "actionName": "query",
                          "target": "customerTable"
                        }
                      ]
                    }
                  ]
                },
                {
                  "componentType": "Button",
                  "title": "重置",
                  "events": [
                    {
                      "eventName": "onClick",
                      "actions": [
                        {
                          "actionName": "reset",
                          "target": "customerSearchForm"
                        }
                      ]
                    }
                  ]
                },
                {
                  "componentType": "Button",
                  "title": "新增客户",
                  "type": "primary",
                  "events": [
                    {
                      "eventName": "onClick",
                      "actions": [
                        {
                          "actionName": "openWindow",
                          "windowId": "customerAddForm",
                          "mode": "modal"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "componentType": "Table",
      "entityDefId": "customerEntity",
      "columns": [
        {
          "title": "客户ID",
          "dataIndex": "id",
          "width": 100
        },
        {
          "title": "姓名",
          "dataIndex": "name",
          "width": 120
        },
        {
          "title": "手机号码",
          "dataIndex": "mobile",
          "width": 120
        },
        {
          "title": "客户类型",
          "dataIndex": "customerType",
          "width": 100,
          "options": [
            {"label": "个人", "value": "personal"},
            {"label": "企业", "value": "enterprise"}
          ]
        },
        {
          "title": "创建时间",
          "dataIndex": "createTime",
          "width": 150
        },
        {
          "title": "操作",
          "dataIndex": "operation",
          "width": 150,
          "operations": [
            {
              "title": "查看",
              "events": [
                {
                  "eventName": "onClick",
                  "actions": [
                    {
                      "actionName": "openWindow",
                      "windowId": "customerDetailForm",
                      "mode": "modal",
                      "parameters": [
                        {
                          "name": "ocId",
                          "value": {"ctxType": "row", "ctxValue": "id"}
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            {
              "title": "编辑",
              "events": [
                {
                  "eventName": "onClick",
                  "actions": [
                    {
                      "actionName": "openWindow",
                      "windowId": "customerEditForm",
                      "mode": "modal",
                      "parameters": [
                        {
                          "name": "ocId",
                          "value": {"ctxType": "row", "ctxValue": "id"}
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```
</example>

<example>
表单窗口需求转换示例：

需求描述：
```
创建一个客户信息编辑表单，包含以下字段：
1. 基本信息：姓名（必填）、性别、出生日期、手机号码（必填）、邮箱
2. 地址信息：省份、城市、详细地址
3. 其他信息：客户来源、备注
4. 操作按钮：保存、取消
```

转换为UI规格：
```json
{
  "title": "客户信息编辑",
  "type": "pc",
  "componentType": "Window",
  "events": [
    {
      "eventName": "onCreate",
      "actions": [
        {
          "actionName": "jsAction",
          "scripts": "var contexts = OC.getWindowContexts(); if (contexts.ocId) { var Form1 = ocWindow.getComponentByName('customerForm'); var entityName = Form1.getEntityName(); OC.getOne(entityName, contexts.ocId, Form1.getFieldSet(), function(response) { if (response.status === '0') { Form1.setFieldsValue(response.data); } }); }"
        }
      ]
    }
  ],
  "operation": [
    {
      "title": "取消",
      "type": "default",
      "events": [
        {
          "eventName": "onClick",
          "actions": [
            {
              "actionName": "back"
            }
          ]
        }
      ]
    },
    {
      "title": "保存",
      "type": "primary",
      "events": [
        {
          "eventName": "onClick",
          "actions": [
            {
              "actionName": "save",
              "body": {
                "dataSource": "customerForm"
              },
              "entityDefId": "customerEntity"
            }
          ]
        }
      ]
    }
  ],
  "componentChildren": [
    {
      "componentType": "Form",
      "componentName": "customerForm",
      "entityDefId": "customerEntity",
      "layout": "horizontal",
      "displayColNum": "2",
      "componentChildren": [
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 24,
              "componentChildren": [
                {
                  "componentType": "Divider",
                  "content": "基本信息",
                  "orientation": "left"
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "Input",
                  "fieldId": "name",
                  "required": true,
                  "placeholder": "请输入姓名",
                  "additionalProperties": {
                    "label": "姓名"
                  }
                }
              ]
            },
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "Radio",
                  "fieldId": "gender",
                  "options": [
                    {"label": "男", "value": "male"},
                    {"label": "女", "value": "female"}
                  ],
                  "additionalProperties": {
                    "label": "性别"
                  }
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "DatePicker",
                  "fieldId": "birthDate",
                  "placeholder": "请选择出生日期",
                  "additionalProperties": {
                    "label": "出生日期"
                  }
                }
              ]
            },
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "Input",
                  "fieldId": "mobile",
                  "required": true,
                  "placeholder": "请输入手机号码",
                  "additionalProperties": {
                    "label": "手机号码"
                  }
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "Input",
                  "fieldId": "email",
                  "placeholder": "请输入邮箱",
                  "additionalProperties": {
                    "label": "邮箱"
                  }
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 24,
              "componentChildren": [
                {
                  "componentType": "Divider",
                  "content": "地址信息",
                  "orientation": "left"
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "Select",
                  "fieldId": "province",
                  "placeholder": "请选择省份",
                  "additionalProperties": {
                    "label": "省份"
                  }
                }
              ]
            },
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "Select",
                  "fieldId": "city",
                  "placeholder": "请选择城市",
                  "additionalProperties": {
                    "label": "城市"
                  }
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 24,
              "componentChildren": [
                {
                  "componentType": "TextArea",
                  "fieldId": "address",
                  "placeholder": "请输入详细地址",
                  "additionalProperties": {
                    "label": "详细地址"
                  }
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 24,
              "componentChildren": [
                {
                  "componentType": "Divider",
                  "content": "其他信息",
                  "orientation": "left"
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 12,
              "componentChildren": [
                {
                  "componentType": "Select",
                  "fieldId": "source",
                  "placeholder": "请选择客户来源",
                  "additionalProperties": {
                    "label": "客户来源"
                  }
                }
              ]
            }
          ]
        },
        {
          "componentType": "Row",
          "componentChildren": [
            {
              "componentType": "Col",
              "span": 24,
              "componentChildren": [
                {
                  "componentType": "TextArea",
                  "fieldId": "remark",
                  "placeholder": "请输入备注",
                  "additionalProperties": {
                    "label": "备注"
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```
</example>

<example type="invalid">
错误的需求转换示例：

需求描述：
```
创建一个客户信息查询窗口，包含姓名和手机号查询，显示客户列表。
```

错误的转换：
```json
{
  "title": "客户信息查询",
  "type": "pc",
  "componentType": "Window",
  "componentChildren": [
    {
      "componentType": "Input",
      "fieldId": "name",
      "placeholder": "请输入姓名"
    },
    {
      "componentType": "Input",
      "fieldId": "mobile",
      "placeholder": "请输入手机号"
    },
    {
      "componentType": "Button",
      "title": "查询"
    },
    {
      "componentType": "Table",
      "entityDefId": "customerEntity"
    }
  ]
}
```

问题：
1. 没有使用Form组件包装查询条件
2. 没有使用Row和Col进行布局
3. 表单组件没有设置label和其他必要属性
4. 按钮没有定义事件和动作
5. 表格没有定义列配置
</example>

## 需求分析与转换步骤

1. 分析需求文档，识别UI窗口的类型和主要功能
2. 确定所需的数据实体和字段
3. 设计UI组件的层次结构和布局
4. 为每个组件定义属性和样式
5. 设计组件间的交互逻辑和事件处理
6. 配置数据绑定和业务规则
7. 生成符合Open-Care平台规范的UI JSON结构
8. 验证生成的UI规格是否满足需求

<critical>
  - 需求分析必须全面，确保捕获所有功能点
  - 组件结构必须遵循Open-Care平台的布局规范
  - 数据绑定必须正确配置，确保与后端实体字段匹配
  - 事件和动作必须完整定义，确保功能正常运行
  - 生成的UI规格必须符合Open-Care平台的JSON格式要求
</critical> 