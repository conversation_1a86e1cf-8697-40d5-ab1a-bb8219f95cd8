---
description: 
globs: 
alwaysApply: false
---
---
name: "java-coding-standards"
description: "Java编程规范和最佳实践"
pattern: "**/*.java"
---

# Java编程规范和最佳实践

to SOLID principles, DRY principles, KISS principles and YAGNI principles. You always follow OWASP best practices. You always break task down to smallest units and approach to solve any task in step by step manner.

You always write elegant code, and you always write code that is easy to understand and easy to maintain and test. 

**Test Coverage**: Suggest or include appropriate unit tests for new or modified code.
**Error Handling**: Implement robust error handling and logging where necessary.
**Avoid Magic Numbers**: Replace hardcoded values with named constants to improve code clarity and maintainability.
**Consider Edge Cases**: When implementing logic, always consider and handle potential edge cases.
**Verify Information**: Always verify information before presenting it. Do not make assumptions or speculate without clear evidence.

- 编写java代码时，请严格遵循如下规范
[201-code-style-guidelines.mdc](mdc:.cursor/rules/201-code-style-guidelines.mdc)
[202-import-path-guidelines.mdc](mdc:.cursor/rules/202-import-path-guidelines.mdc)
[203-crud-code-guidelines.mdc](mdc:.cursor/rules/203-crud-code-guidelines.mdc)
[204-process-import-guidelines.mdc](mdc:.cursor/rules/204-process-import-guidelines.mdc)
[205-sql-coding-guidelines.mdc](mdc:.cursor/rules/205-sql-coding-guidelines.mdc)
[206-other-guidelines.mdc](mdc:.cursor/rules/206-other-guidelines.mdc)
[211-performance-guidelines.mdc](mdc:.cursor/rules/211-performance-guidelines.mdc)
[212-unit-test-guidelines.mdc](mdc:.cursor/rules/212-unit-test-guidelines.mdc)
[214-code-review-guidelines.mdc](mdc:.cursor/rules/214-code-review-guidelines.mdc)


- Use PascalCase for class names (e.g., UserController, OrderService).
  - 若是Utility class，则类名以Util结尾，例如：StringUtil, CollectionUtil, DateUtil
- Use camelCase for method and variable names (e.g., findUserById, isOrderValid).
- Use ALL_CAPS for constants (e.g., MAX_RETRY_ATTEMPTS, DEFAULT_PAGE_SIZE).
- 函数或方法命名规范
  - 函数或方法名必须明确表达函数的意图，例如：findByCustomerId, findByCustomerName, findByCustomerIdAndCustomerName
  - 根据实体ID或其他条件查询某个实体对象，如果查询不到，则返回null的函数，其函数名以findBy开头，例如：findByCustomerId, findByCustomerName, findByCustomerIdAndCustomerName
  - 根据实体ID获取某个实体对象，如果实体不存在，则抛出异常，其函数名以getById开头，例如：getByIdCustomerId, getByIdCustomerName, getByIdCustomerIdAndCustomerNam
  - 在内存中创建某个DTO对象，该函数名以make开头，例如：makeCustomerDTO, makeCustomerDTOList, makeCustomerDTOListByCustomerName
  - 若是创建一个实体对象，函数名以create开头，例如：createCustomer, createCustomerList, createCustomerListByCustomerName
  - 根据条件查询多个实体对象，如果查询不到，则返回List为空的函数，其函数名以queryBy开头，例如：queryByCustomerId, queryByCustomerName, queryByCustomerIdAndCustomerName
  - 对输入对象的做验证，如果不符合条件则抛出异常，其函数名以validate开头，例如：validateCustomer, validateCustomerList, validateCustomerListByCustomerName
  - 对输入对象的做转换，其函数名以convert开头，例如：convertPersonToCustomer, convertCustomerList, convertCustomerListByCustomerName
- 变量命名规范
  - 变量名以小写字母开头，例如：customerId, customerName, customerIdAndCustomerName
  - 若变量是List，则变量名以list结尾，例如：customerList
  - 若变量是DTO，则变量名以dto结尾，例如：customerDTO, customerDTOList, customerDTOListByCustomerName
  - 若变量是实体对象的ID，则变量名以id结尾，例如：customerId, customerIdList
  - 若变量是Supplier，则变量名以supplier结尾，例如：customerSupplier
  - 若变量是Function等函数式接口，则变量名以functor结尾，例如：customerFunctor
- 在书写泛型类时，通常做以下的约定：
  - E表示Element，通常用在集合中；
  - ID用于表示对象的唯一标识符类型T表示Type(类型)，通常指代类；
  - K表示Key(键),通常用于Map中；
  - V表示Value(值),通常用于Map中，与K结对出现；
  - N表示Number,通常用于表示数值类型；
  - ？表示不确定的Java类型；
  - X用于表示异常；
  - U,S表示任意的类型；

# Java 后端接口定义规范（基于 Google 风格指南与最佳实践）

## 一、参数命名规范（核心原则）

### 1. 语义化命名
- 使用完整英文单词准确表达参数含义，避免模糊或歧义
- **示例**：
  ✅ `productId`（商品ID）、`categoryName`（类别名称）、`priceRange`（价格区间）
  ❌ `prodId`（缩写模糊）、`catName`（易误解为"猫咪名称"）、`price`（未明确范围）

### 2. 避免过度缩写
- 优先使用完整单词，仅使用业界广泛认可的缩写（如`id`代替`identifier`，`url`代替`uniformResourceLocator`）
- **示例**：
  ✅ `resourceIdentifier`（资源标识符）
  ❌ `resId`（非标准缩写）、`jgqj`（拼音缩写）

### 3. 一致性与驼峰命名法
- 统一使用**小驼峰命名法**（首字母小写，后续单词首字母大写），符合Java编码习惯
- **示例**：
  ✅ `orderStatus`（订单状态）、`paymentMethod`（支付方式）
  ❌ `order_status`（下划线命名）、`OrderStatus`（大驼峰用于类名）

### 4. 描述性优先
- 命名需聚焦参数用途，减少理解成本，避免过度简化
- **示例**：
  ✅ `userRegistrationDate`（用户注册日期）、`productExpirationDate`（产品过期日期）
  ❌ `regDate`（缺乏上下文）、`expDate`（可能指代多种场景）

### 5. 逻辑关联性
- 相关参数需体现业务逻辑关联，如时间范围、条件组合等
- **示例**：
  ✅ `startDate` & `endDate`（活动起止时间）、`minPrice` & `maxPrice`（价格范围）

## 二、接口命名与设计规范

### 1. RESTful风格原则
- 接口路径使用名词复数表示资源集合，HTTP方法表示操作类型：
  - `GET /api/products`（查询商品列表）
  - `POST /api/orders`（创建订单）
  - `PUT /api/products/{id}`（更新商品）
  - `DELETE /api/comments/{id}`（删除评论）

### 2. 方法命名规范
- Java接口方法使用**动词/动词短语 + 名词**的小驼峰命名，明确操作意图：
  - `List<Product> getProductsByCategory(String categoryName)`
  - `Order createOrder(OrderCreateRequest request)`
  - `void updateProductPrice(Long productId, BigDecimal price)`

### 3. 参数对象封装
- 复杂参数需封装为DTO（Data Transfer Object），类名使用大驼峰命名，属性遵循参数命名规范：
```java
public class ProductQueryDTO {
    private Long productId;
    private String categoryName;
    private PriceRange priceRange; // 自定义价格区间对象
    // getter/setter 省略
}
```
其中`PriceRange`类包含`minPrice`和`maxPrice`属性，体现逻辑关联性。

## 四、反例与避坑指南

| 问题类型 | 反例 | 修正方案 |
|----------------|-------------------------------|-------------------------------|
| 缩写模糊 | `usrId`（用户ID） | `userId` |
| 拼音命名 | `shangpinId`（商品ID） | `productId` |
| 语义含糊 | `data`（未明确数据类型） | `userData` 或 `productData` |
| 风格不一致 | `order_status`（下划线） | `orderStatus`（小驼峰） |
| 过度简化 | `regDate`（注册日期） | `userRegistrationDate` |