---
description: 
globs: 
alwaysApply: false
---
# API 接口定义规范

## 分析项目中的RestController，并按照API BluePrint规范生成API接口描述，同时给出示例

## 生成的文件放在/rest-api-doc下 , 格式为markdown

## Open-Care平台通用的返回格式OcResponse

### status
- **字段类型**: String
- **说明**: 0 表示成功，-1 表示失败

### data
- **字段类型**: Object
- **说明**: 返回的数据

### msg
- **字段类型**: String
- **说明**: 消息描述

## /save接口

### save接口请求的入参为SaveRequestDTO

### SaveRequestDTO
#### entityName
- **字段类型**: String
- **说明**: 保存的实体名称。包括但不限于：
  1. Java定义的Entity ClassName
  2. Java定义的DTO ClassName
- **必填**: 是

#### data
- **字段类型**: Map
- **说明**: 保存的实体内容：
  根据entityName对应的Entity或DTO的数据模型，生成的实体数据

```json
{
        "name": "admin",
        "username": "admin",
        "contacts": [
            {
                "type": "1",
                "value": "11111"
            }
        ],
        "address":{
           "country":"中国",
           "city":"北京"
        }
    }
```
- **必填**: 是

#### eagerProperties
- **字段类型**: String
- **说明**: 如果只需返回实体的所有基本类型字段值，则设置为： GlobalConstant.EAGER_ROOT

  如果需返回实体的关联字段（OneToOne、OneToMany、ManyToOne、ManyToMany）的值，则设置关联字段的字段名，如需返回多个关联字段，以逗号分隔。

  Ex：

  ```
  Class A {
    String name;
    B b;
  }
   
  Class B {
    String type;
    C c;
  }
   
  Class C {
    String code;
  }
  ```

  如果只需获取A对象的基本类型字段的值，则eagerProperties设为： / （此时返回A对象中所有基本类型字段的值，格式如下）

  ```
  {
    "name": "xxxx"
  }
  ```

  如果需获取b字段关联的对象信息，则eagerProperties设为：b （此时返回的A对象中，带有A对象中所有基本类型字段的值，及B对象中所有基本类型字段的值）

  ```
  {
    "name": "xxxx",
    "b": {
      "type": "zzzz"
    }
  }
  ```

  如果要获取到B对象中的c字段关联的对象信息，则设为： b.c（此时返回的A对象中，带有A对象中所有基本类型字段的值，及B对象中所有基本类型字段值，及C对象中所有基本类型字段的值

  ```
  {
    "name": "xxxx",
    "b": {
      "type": "zzzz",
      "c:": {
        "code": "aaaaa"
      }
    }
  }
  ```
  - **必填**: 否

## /query接口

### query接口请求的入参为QueryRequestDTO，出参OcResponse中的data为QueryDataResponse

### QueryRequestDTO的定义
#### entityName
- **字段类型**: String
- **说明**: 查询的实体名称。包括但不限于：
  1. Java定义的Entity ClassName
  2. Java定义的DTO ClassName
  3. 数据库视图名称
- **必填**: 是

#### filters
- **字段类型**: List\<FieldData\>
- **说明**: 查询的过滤条件。
  - fieldName: 字段名。如需按实体关联对象的字段进行过滤，则按如下方式设置字段名：A#B
    - Ex：按用户所属机构的类型进行过滤，则fieldName为：ownOrg#type。
  - operator：运算符，可选值【equal、greater、greaterOrEqual、less、lessOrEqual、notEqual、in、notIn、like、between、isNull、isNotNull、leftLike、rightLike】
  - fieldValue：字段值，说明：对于 in、not in运算符，fieldvalue的值为: ["a","b","c"] ,对于between运算符，fieldvalue的值为： ["2018-10-01","2018-12-01"]。

#### pagination
- **字段类型**: Page
- **说明**: 分页信息。
  - current 当前显示页码。
  - pageSize 每页显示多少行。

#### sorter
- **字段类型**: Map
- **说明**: 排序信息。
  - Key为字段值。
  - Value为排序方式，可选值：【ascend、descend】。
  - Ex：{ "created":"ascend"}

#### summary
- **字段类型**: Map
- **说明**: 合计信息。
  - Key为字段值。
  - Value为合计类型，可选值：【sum、min、max、avg、count】。
  - Ex：{ "amount":"sum"}

#### fieldSet
- **字段类型**: String
- **说明**: 指定查询返回的字段集合。
  - Ex： name,userName,sex

#### schemas
- **字段类型**: 
- **说明**: 实体及字段的访问权限信息。

#### eagerProperties
- **字段类型**: String
- **说明**: 如果只需返回实体的所有基本类型字段值，则设置为： GlobalConstant.EAGER_ROOT

  如果需返回实体的关联字段（OneToOne、OneToMany、ManyToOne、ManyToMany）的值，则设置关联字段的字段名，如需返回多个关联字段，以逗号分隔。

  Ex：

  ```
  Class A {
    String name;
    B b;
  }
   
  Class B {
    String type;
    C c;
  }
   
  Class C {
    String code;
  }
  ```

  如果只需获取A对象的基本类型字段的值，则eagerProperties设为： / （此时返回A对象中所有基本类型字段的值，格式如下）

  ```
  {
    "name": "xxxx"
  }
  ```

  如果需获取b字段关联的对象信息，则eagerProperties设为：b （此时返回的A对象中，带有A对象中所有基本类型字段的值，及B对象中所有基本类型字段的值）

  ```
  {
    "name": "xxxx",
    "b": {
      "type": "zzzz"
    }
  }
  ```

  如果要获取到B对象中的c字段关联的对象信息，则设为： b.c（此时返回的A对象中，带有A对象中所有基本类型字段的值，及B对象中所有基本类型字段值，及C对象中所有基本类型字段的值

  ```
  {
    "name": "xxxx",
    "b": {
      "type": "zzzz",
      "c:": {
        "code": "aaaaa"
      }
    }
  }
  ```

#### useGraphql
- **字段类型**: Boolean
- **说明**: 如果为true，平台将QueryRequestDTO中的相关信息转换为GraphQL查询语句，然后通过GraphQL方式进行查询。

#### authority
- **字段类型**: Boolean
- **说明**: 如果为true，会根据实体及字段的访问权限对查询返回的数据进行处理。

#### type
- **字段类型**: String
- **说明**: 查询类型。通过SchemaQuery.query时根据该字段的值调用不同的方法进行查询处理。

#### excludeFields
- **字段类型**: String
- **说明**: 仅在useGraphql=true时起作用。
  平台根据eagerProperties的值转换为GraphQL查询语句中的查询字段列表时，可将某些字段排除掉。
  Ex：name,sex

#### parallelCount
- **字段类型**: Integer
- **说明**: 针对Oracle数据库，设置查询并行参数。

#### queryHints
- **字段类型**: String
- **说明**: 针对Oracle数据库，设置 hints 语法。

### QueryDataResponse

#### data
- **字段类型**: Object
- **说明**: 查询返回的数据，一般为List<DTO>或List<Map>

#### page
- **字段类型**: Page
- **说明**: 分页信息。
  - total：记录总数。
  - pageSize: 每页显示记录数
  - current: 当前页面

#### authority
- **字段类型**: Object
- **说明**: 数据权限信息

#### summary
- **字段类型**: Object
- **说明**: 合计信息

## /get/{entityName}/{entityInstId}接口

### get接口请求的入参为GetRequestDTO，出参OcResponse中的data为EditDataResponse

### 请求URL中的参数说明
#### entityName
- **字段类型**: String
- **说明**: 查询的实体名称。包括但不限于：
  1. Java定义的Entity ClassName
  2. Java定义的DTO ClassName
- **必填**: 是

#### entityInstId
- **字段类型**: String
- **说明**: 查询的实体名称。包括但不限于：
  1. Java定义的Entity ClassName
  2. Java定义的DTO ClassName
  3. 数据库视图名称
- **必填**: 是

### EditDataResponse

#### data
- **字段类型**: Object
- **说明**: Get返回的数据，一般为DTO或Map

## /batchSave接口







