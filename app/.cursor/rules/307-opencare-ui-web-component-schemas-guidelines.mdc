---
description: 
globs: ui/*.json
alwaysApply: false
---
 # 组件Schema配置规则说明文档

---
description: 定义UI Web端各组件的schema配置信息
globs: *.json
alwaysApply: false
---

## Context
- 当需要为Open-Care平台UI组件定义Schema属性时应用此规则
- 组件生成Schema时，请使用对应组件的Schema模板，必须包含完整的Schema定义

## Window组件Schema
```json
{
    "type": "object",
    "title": "formData",
    "required": [
      "componentName",
      "title"
    ],
    "properties": {
      "tag": {
        "type": "string",
        "title": "标签"
      },
      "title": {
        "type": "string",
        "title": "默认窗口名称"
      },
      "visible": {
        "type": "boolean",
        "title": "可见"
      },
      "closeMsg": {
        "type": "string",
        "title": "multiTabs时关闭窗口提示语"
      },
      "marginTop": {
        "type": "string",
        "title": "上外边距（单位px）"
      },
      "operation": {
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "icon": {
              "type": "string",
              "title": "图标"
            },
            "type": {
              "enum": [
                "default",
                "primary",
                "danger",
                "dashed",
                "icon",
                "link"
              ],
              "type": "string",
              "title": "显示类型(类型设置为 icon 时，请一定设置下面的图标属性)",
              "default": "link"
            },
            "title": {
              "type": "string",
              "title": "标题"
            },
            "events": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "actions": {
                    "type": "array",
                    "items": {
                      "type": "object",
                      "required": [
                        "actionName"
                      ],
                      "properties": {
                        "actionName": {
                          "enum": [
                            "save",
                            "batchSave",
                            "openWindow",
                            "execDrools",
                            "startProcess",
                            "startProcessByKeyAndRunFirstTask",
                            "completeTask",
                            "setTaskVariables",
                            "query",
                            "chartQuery",
                            "delete",
                            "export",
                            "execScript",
                            "restApi",
                            "taskList",
                            "getOne",
                            "back",
                            "jsAction",
                            "reset",
                            "clean",
                            "claimTask",
                            "delegateTask",
                            "assigneeTask",
                            "resolveTask",
                            "openDrawer",
                            "currentUserTask",
                            "getOcSystemNoticeByTemplateKey",
                            "queryForGraphQL",
                            "getOneForGraphQL",
                            "saveForGraphQL",
                            "mobileOperationsAction"
                          ],
                          "type": "string",
                          "title": "动作"
                        }
                      },
                      "dependencies": {
                        "actionName": {
                          "oneOf": [
                            {
                              "properties": {
                                "mask": {
                                  "type": "boolean",
                                  "title": "蒙层(只对popover有效)",
                                  "default": false
                                },
                                "title": {
                                  "type": "string",
                                  "title": "标题"
                                },
                                "placement": {
                                  "enum": [
                                    "left",
                                    "right",
                                    "top",
                                    "bottom",
                                    "topLeft",
                                    "topRight",
                                    "bottomLeft",
                                    "bottomRight"
                                  ],
                                  "type": "string",
                                  "title": "只对popover有效",
                                  "default": "bottomRight"
                                },
                                "actionName": {
                                  "enum": [
                                    "mobileOperationsAction"
                                  ]
                                },
                                "description": {
                                  "type": "string",
                                  "title": "描述"
                                },
                                "displayType": {
                                  "enum": [
                                    "sheet",
                                    "alert",
                                    "operation",
                                    "popover"
                                  ],
                                  "type": "string",
                                  "title": "模式"
                                },
                                "actionOptions": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "actions": {
                                        "type": "array",
                                        "items": {
                                          "type": "object",
                                          "required": [
                                            "actionName"
                                          ],
                                          "properties": {
                                            "actionName": {
                                              "enum": [
                                                "save",
                                                "batchSave",
                                                "openWindow",
                                                "execDrools",
                                                "startProcess",
                                                "startProcessByKeyAndRunFirstTask",
                                                "completeTask",
                                                "setTaskVariables",
                                                "query",
                                                "chartQuery",
                                                "delete",
                                                "export",
                                                "execScript",
                                                "restApi",
                                                "taskList",
                                                "getOne",
                                                "back",
                                                "jsAction",
                                                "reset",
                                                "clean",
                                                "claimTask",
                                                "delegateTask",
                                                "assigneeTask",
                                                "resolveTask",
                                                "openDrawer",
                                                "currentUserTask",
                                                "getOcSystemNoticeByTemplateKey",
                                                "queryForGraphQL",
                                                "getOneForGraphQL",
                                                "saveForGraphQL"
                                              ],
                                              "type": "string",
                                              "title": "动作"
                                            }
                                          },
                                          "dependencies": {
                                            "actionName": {
                                              "oneOf": [
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        },
                                                        "additionalDataSource": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "主键"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              },
                                                              "operator": {
                                                                "enum": [
                                                                  "equal",
                                                                  "greater",
                                                                  "greaterOrEqual",
                                                                  "less",
                                                                  "lessOrEqual",
                                                                  "notEqual",
                                                                  "in",
                                                                  "notIn",
                                                                  "like",
                                                                  "between"
                                                                ],
                                                                "type": "string",
                                                                "title": "operator"
                                                              }
                                                            }
                                                          },
                                                          "title": "其他数据来源"
                                                        }
                                                      }
                                                    },
                                                    "target": {
                                                      "type": "string",
                                                      "title": "目标组件(target)"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "query",
                                                        "chartQuery",
                                                        "queryForGraphQL"
                                                      ]
                                                    },
                                                    "entityDefId": {
                                                      "type": "string",
                                                      "title": "实体对象(entityDefId)"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "dataValue": {
                                                          "type": "object",
                                                          "properties": {
                                                            "ctxType": {
                                                              "type": "string",
                                                              "title": "上下文类型"
                                                            },
                                                            "ctxValue": {
                                                              "type": "string",
                                                              "title": "上下文值"
                                                            }
                                                          }
                                                        },
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        }
                                                      }
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "delete"
                                                      ]
                                                    },
                                                    "confirmInfo": {
                                                      "type": "object",
                                                      "properties": {
                                                        "show": {
                                                          "type": "boolean",
                                                          "title": "删除时是否弹出确认框",
                                                          "default": true
                                                        },
                                                        "content": {
                                                          "type": "string",
                                                          "title": "删除时弹出确认框的内容"
                                                        }
                                                      }
                                                    },
                                                    "entityDefId": {
                                                      "type": "string",
                                                      "title": "实体对象(entityDefId)"
                                                    },
                                                    "refreshMode": {
                                                      "enum": [
                                                        "page",
                                                        "none"
                                                      ],
                                                      "type": "string",
                                                      "title": "执行该action后是否根据变化的entity刷新对应组件内容",
                                                      "default": "page"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "dataValue": {
                                                          "type": "object",
                                                          "properties": {
                                                            "ctxType": {
                                                              "type": "string",
                                                              "title": "上下文类型"
                                                            },
                                                            "ctxValue": {
                                                              "type": "string",
                                                              "title": "上下文值"
                                                            }
                                                          }
                                                        },
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        }
                                                      }
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "export"
                                                      ]
                                                    },
                                                    "entityDefId": {
                                                      "type": "string",
                                                      "title": "实体对象(entityDefId)"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        }
                                                      }
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "restApi"
                                                      ]
                                                    },
                                                    "parameters": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键"
                                                          },
                                                          "value": {
                                                            "type": "object",
                                                            "properties": {
                                                              "ctxType": {
                                                                "type": "string",
                                                                "title": "上下文类型"
                                                              },
                                                              "ctxValue": {
                                                                "type": "string",
                                                                "title": "上下文值"
                                                              }
                                                            }
                                                          }
                                                        }
                                                      },
                                                      "title": "请求参数"
                                                    },
                                                    "requestUrl": {
                                                      "enum": [
                                                        "sendSms",
                                                        "regist",
                                                        "assign",
                                                        "diable"
                                                      ],
                                                      "type": "string",
                                                      "title": "请求地址(url)"
                                                    },
                                                    "requestType": {
                                                      "enum": [
                                                        "upload",
                                                        "download"
                                                      ],
                                                      "type": "string",
                                                      "title": "文件传输类型(上传/下载)"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        }
                                                      }
                                                    },
                                                    "file": {
                                                      "type": "string",
                                                      "title": "脚本文件",
                                                      "format": "data-url"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "execScript"
                                                      ]
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "variables": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "主键"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          "title": "变量"
                                                        },
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        }
                                                      }
                                                    },
                                                    "target": {
                                                      "type": "string",
                                                      "title": "目标组件(target)"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "taskList"
                                                      ]
                                                    },
                                                    "parameters": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键"
                                                          },
                                                          "processDefinition": {
                                                            "type": "string",
                                                            "title": "流程定义(processDefinition)"
                                                          }
                                                        }
                                                      },
                                                      "title": "请求参数"
                                                    },
                                                    "entityDefId": {
                                                      "type": "string",
                                                      "title": "实体对象(entityDefId)"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "url": {
                                                      "type": "string",
                                                      "title": "网址"
                                                    },
                                                    "mode": {
                                                      "enum": [
                                                        "page",
                                                        "modal",
                                                        "drawer",
                                                        "url",
                                                        "popover"
                                                      ],
                                                      "type": "string",
                                                      "title": "打开形式"
                                                    },
                                                    "contexts": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键"
                                                          },
                                                          "value": {
                                                            "type": "object",
                                                            "properties": {
                                                              "ctxType": {
                                                                "type": "string",
                                                                "title": "上下文类型"
                                                              },
                                                              "ctxValue": {
                                                                "type": "string",
                                                                "title": "上下文值"
                                                              }
                                                            }
                                                          }
                                                        }
                                                      },
                                                      "title": "上下文"
                                                    },
                                                    "windowId": {
                                                      "type": "string",
                                                      "title": "绑定窗口(windowId)"
                                                    },
                                                    "placement": {
                                                      "enum": [
                                                        "rightBottom",
                                                        "top",
                                                        "left",
                                                        "right",
                                                        "bottom",
                                                        "topLeft",
                                                        "topRight",
                                                        "bottomLeft",
                                                        "bottomRight",
                                                        "leftTop",
                                                        "leftBottom",
                                                        "rightTop"
                                                      ],
                                                      "type": "string",
                                                      "title": "弹框位置(打开形式为popover时生效)",
                                                      "default": "rightBottom"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "openWindow"
                                                      ]
                                                    },
                                                    "modalWidth": {
                                                      "type": "string",
                                                      "title": "模态框宽度(打开形式为modal/drawer时生效)"
                                                    },
                                                    "modalHeight": {
                                                      "type": "string",
                                                      "title": "模态框高度(打开形式为modal时生效)"
                                                    },
                                                    "windowTitle": {
                                                      "type": "string",
                                                      "title": "自定义窗口名称(此项为空时，默认使用绑定窗口的名称)"
                                                    },
                                                    "maskClosable": {
                                                      "type": "boolean",
                                                      "title": "点击空白区域关闭弹框",
                                                      "default": false
                                                    },
                                                    "showCloseButton": {
                                                      "type": "boolean",
                                                      "title": "显示右上角“关闭”按钮(打开形式为modal/drawer时生效)",
                                                      "default": true
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "target": {
                                                      "type": "string",
                                                      "title": "目标组件(target)"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "getOne",
                                                        "getOneForGraphQL"
                                                      ]
                                                    },
                                                    "parameters": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键"
                                                          },
                                                          "value": {
                                                            "type": "object",
                                                            "properties": {
                                                              "ctxType": {
                                                                "type": "string",
                                                                "title": "上下文类型"
                                                              },
                                                              "ctxValue": {
                                                                "type": "string",
                                                                "title": "上下文值"
                                                              }
                                                            }
                                                          }
                                                        }
                                                      },
                                                      "title": "请求参数",
                                                      "maxItems": 1.0
                                                    },
                                                    "entityDefId": {
                                                      "type": "string",
                                                      "title": "实体对象(entityDefId)"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        },
                                                        "additionalDataSource": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "主键"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          "title": "其他数据来源"
                                                        }
                                                      }
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "save",
                                                        "saveForGraphQL",
                                                        "batchSave"
                                                      ]
                                                    },
                                                    "entityDefId": {
                                                      "type": "string",
                                                      "title": "实体对象(entityDefId)"
                                                    },
                                                    "refreshMode": {
                                                      "enum": [
                                                        "page",
                                                        "none"
                                                      ],
                                                      "type": "string",
                                                      "title": "执行该action后是否根据变化的entity刷新对应组件内容",
                                                      "default": "page"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "entities": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "实体对象(entityDefId)"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          "title": "实体"
                                                        },
                                                        "variables": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "主键"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          "title": "变量"
                                                        }
                                                      }
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "startProcess",
                                                        "startProcessByKeyAndRunFirstTask"
                                                      ]
                                                    },
                                                    "parameters": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键"
                                                          },
                                                          "processDefinition": {
                                                            "type": "string",
                                                            "title": "流程定义(processDefinition)"
                                                          }
                                                        }
                                                      },
                                                      "title": "请求参数"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "actionName": {
                                                      "enum": [
                                                        "claimTask",
                                                        "delegateTask",
                                                        "assigneeTask"
                                                      ]
                                                    },
                                                    "parameters": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键",
                                                            "default": "taskId"
                                                          },
                                                          "value": {
                                                            "type": "object",
                                                            "properties": {
                                                              "ctxType": {
                                                                "type": "string",
                                                                "title": "上下文类型"
                                                              },
                                                              "ctxValue": {
                                                                "type": "string",
                                                                "title": "上下文值"
                                                              }
                                                            }
                                                          }
                                                        }
                                                      },
                                                      "title": "请求参数"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "entities": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "实体对象(entityDefId)"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          "title": "实体"
                                                        },
                                                        "variables": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "主键"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          "title": "变量"
                                                        }
                                                      }
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "setTaskVariables",
                                                        "completeTask",
                                                        "resolveTask"
                                                      ]
                                                    },
                                                    "parameters": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键",
                                                            "default": "taskId"
                                                          },
                                                          "value": {
                                                            "type": "object",
                                                            "properties": {
                                                              "ctxType": {
                                                                "type": "string",
                                                                "title": "上下文类型"
                                                              },
                                                              "ctxValue": {
                                                                "type": "string",
                                                                "title": "上下文值"
                                                              }
                                                            }
                                                          }
                                                        }
                                                      },
                                                      "title": "请求参数"
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "actionName": {
                                                      "enum": [
                                                        "back"
                                                      ]
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "target": {
                                                      "type": "string",
                                                      "title": "Drawer组件"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "openDrawer"
                                                      ]
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "target": {
                                                      "type": "string",
                                                      "title": "目标组件"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "currentUserTask"
                                                      ]
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "additionalDataSource": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "主键"
                                                              },
                                                              "value": {
                                                                "type": "object",
                                                                "properties": {
                                                                  "ctxType": {
                                                                    "type": "string",
                                                                    "title": "上下文类型"
                                                                  },
                                                                  "ctxValue": {
                                                                    "type": "string",
                                                                    "title": "上下文值"
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          "title": "参数设置"
                                                        }
                                                      }
                                                    },
                                                    "target": {
                                                      "type": "string",
                                                      "title": "目标组件"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "getOcSystemNoticeByTemplateKey"
                                                      ]
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "body": {
                                                      "type": "object",
                                                      "title": "请求体",
                                                      "properties": {
                                                        "dataSource": {
                                                          "type": "string",
                                                          "title": "数据来源组件(dataSource)"
                                                        },
                                                        "otherDataSource": {
                                                          "type": "array",
                                                          "items": {
                                                            "type": "object",
                                                            "properties": {
                                                              "name": {
                                                                "type": "string",
                                                                "title": "主键"
                                                              },
                                                              "value": {
                                                                "type": "string"
                                                              }
                                                            }
                                                          },
                                                          "title": "其他数据来源"
                                                        }
                                                      }
                                                    },
                                                    "response": {
                                                      "type": "array",
                                                      "items": {
                                                        "type": "object",
                                                        "properties": {
                                                          "name": {
                                                            "type": "string",
                                                            "title": "主键"
                                                          },
                                                          "value": {
                                                            "type": "object",
                                                            "properties": {
                                                              "ctxType": {
                                                                "type": "string",
                                                                "title": "上下文类型"
                                                              },
                                                              "ctxValue": {
                                                                "type": "string",
                                                                "title": "上下文值"
                                                              }
                                                            }
                                                          }
                                                        }
                                                      },
                                                      "title": "响应"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "execDrools"
                                                      ]
                                                    },
                                                    "parameters": {
                                                      "type": "object",
                                                      "title": "请求参数",
                                                      "properties": {
                                                        "droolsId": {
                                                          "type": "string",
                                                          "title": "规则引擎(droolsId)"
                                                        }
                                                      }
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "scripts": {
                                                      "type": "string",
                                                      "title": "脚本代码"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "jsAction"
                                                      ]
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "target": {
                                                      "type": "string",
                                                      "title": "目标组件(target)"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "reset"
                                                      ]
                                                    }
                                                  }
                                                },
                                                {
                                                  "properties": {
                                                    "target": {
                                                      "type": "string",
                                                      "title": "目标组件(target)"
                                                    },
                                                    "actionName": {
                                                      "enum": [
                                                        "clean"
                                                      ]
                                                    }
                                                  }
                                                }
                                              ]
                                            }
                                          }
                                        },
                                        "title": "事件"
                                      },
                                      "canDisplay": {
                                        "type": "string"
                                      },
                                      "buttonTitle": {
                                        "type": "string",
                                        "title": "标题"
                                      },
                                      "destructiveAction": {
                                        "type": "boolean",
                                        "default": false
                                      }
                                    }
                                  }
                                },
                                "hasCancelButton": {
                                  "type": "boolean",
                                  "title": "有取消按钮",
                                  "default": true
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    },
                                    "additionalDataSource": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "主键"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          },
                                          "operator": {
                                            "enum": [
                                              "equal",
                                              "greater",
                                              "greaterOrEqual",
                                              "less",
                                              "lessOrEqual",
                                              "notEqual",
                                              "in",
                                              "notIn",
                                              "like",
                                              "between"
                                            ],
                                            "type": "string",
                                            "title": "operator"
                                          }
                                        }
                                      },
                                      "title": "其他数据来源"
                                    }
                                  }
                                },
                                "target": {
                                  "type": "string",
                                  "title": "目标组件(target)"
                                },
                                "actionName": {
                                  "enum": [
                                    "query",
                                    "chartQuery",
                                    "queryForGraphQL"
                                  ]
                                },
                                "entityDefId": {
                                  "type": "string",
                                  "title": "实体对象(entityDefId)"
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "dataValue": {
                                      "type": "object",
                                      "properties": {
                                        "ctxType": {
                                          "type": "string",
                                          "title": "上下文类型"
                                        },
                                        "ctxValue": {
                                          "type": "string",
                                          "title": "上下文值"
                                        }
                                      }
                                    },
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    }
                                  }
                                },
                                "actionName": {
                                  "enum": [
                                    "delete"
                                  ]
                                },
                                "confirmInfo": {
                                  "type": "object",
                                  "properties": {
                                    "show": {
                                      "type": "boolean",
                                      "title": "删除时是否弹出确认框",
                                      "default": true
                                    },
                                    "content": {
                                      "type": "string",
                                      "title": "删除时弹出确认框的内容"
                                    }
                                  }
                                },
                                "entityDefId": {
                                  "type": "string",
                                  "title": "实体对象(entityDefId)"
                                },
                                "refreshMode": {
                                  "enum": [
                                    "page",
                                    "none"
                                  ],
                                  "type": "string",
                                  "title": "执行该action后是否根据变化的entity刷新对应组件内容",
                                  "default": "page"
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "dataValue": {
                                      "type": "object",
                                      "properties": {
                                        "ctxType": {
                                          "type": "string",
                                          "title": "上下文类型"
                                        },
                                        "ctxValue": {
                                          "type": "string",
                                          "title": "上下文值"
                                        }
                                      }
                                    },
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    }
                                  }
                                },
                                "actionName": {
                                  "enum": [
                                    "export"
                                  ]
                                },
                                "entityDefId": {
                                  "type": "string",
                                  "title": "实体对象(entityDefId)"
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    }
                                  }
                                },
                                "actionName": {
                                  "enum": [
                                    "restApi"
                                  ]
                                },
                                "parameters": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键"
                                      },
                                      "value": {
                                        "type": "object",
                                        "properties": {
                                          "ctxType": {
                                            "type": "string",
                                            "title": "上下文类型"
                                          },
                                          "ctxValue": {
                                            "type": "string",
                                            "title": "上下文值"
                                          }
                                        }
                                      }
                                    }
                                  },
                                  "title": "请求参数"
                                },
                                "requestUrl": {
                                  "enum": [
                                    "sendSms",
                                    "regist",
                                    "assign",
                                    "diable"
                                  ],
                                  "type": "string",
                                  "title": "请求地址(url)"
                                },
                                "requestType": {
                                  "enum": [
                                    "upload",
                                    "download"
                                  ],
                                  "type": "string",
                                  "title": "文件传输类型(上传/下载)"
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    }
                                  }
                                },
                                "file": {
                                  "type": "string",
                                  "title": "脚本文件",
                                  "format": "data-url"
                                },
                                "actionName": {
                                  "enum": [
                                    "execScript"
                                  ]
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "variables": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "主键"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          }
                                        }
                                      },
                                      "title": "变量"
                                    },
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    }
                                  }
                                },
                                "target": {
                                  "type": "string",
                                  "title": "目标组件(target)"
                                },
                                "actionName": {
                                  "enum": [
                                    "taskList"
                                  ]
                                },
                                "parameters": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键"
                                      },
                                      "processDefinition": {
                                        "type": "string",
                                        "title": "流程定义(processDefinition)"
                                      }
                                    }
                                  },
                                  "title": "请求参数"
                                },
                                "entityDefId": {
                                  "type": "string",
                                  "title": "实体对象(entityDefId)"
                                }
                              }
                            },
                            {
                              "properties": {
                                "url": {
                                  "type": "string",
                                  "title": "网址"
                                },
                                "mode": {
                                  "enum": [
                                    "page",
                                    "modal",
                                    "drawer",
                                    "url",
                                    "popover"
                                  ],
                                  "type": "string",
                                  "title": "打开形式"
                                },
                                "contexts": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键"
                                      },
                                      "value": {
                                        "type": "object",
                                        "properties": {
                                          "ctxType": {
                                            "type": "string",
                                            "title": "上下文类型"
                                          },
                                          "ctxValue": {
                                            "type": "string",
                                            "title": "上下文值"
                                          }
                                        }
                                      }
                                    }
                                  },
                                  "title": "上下文"
                                },
                                "windowId": {
                                  "type": "string",
                                  "title": "绑定窗口(windowId)"
                                },
                                "placement": {
                                  "enum": [
                                    "rightBottom",
                                    "top",
                                    "left",
                                    "right",
                                    "bottom",
                                    "topLeft",
                                    "topRight",
                                    "bottomLeft",
                                    "bottomRight",
                                    "leftTop",
                                    "leftBottom",
                                    "rightTop"
                                  ],
                                  "type": "string",
                                  "title": "弹框位置(打开形式为popover时生效)",
                                  "default": "rightBottom"
                                },
                                "actionName": {
                                  "enum": [
                                    "openWindow"
                                  ]
                                },
                                "modalWidth": {
                                  "type": "string",
                                  "title": "模态框宽度(打开形式为modal/drawer时生效)"
                                },
                                "modalHeight": {
                                  "type": "string",
                                  "title": "模态框高度(打开形式为modal时生效)"
                                },
                                "windowTitle": {
                                  "type": "string",
                                  "title": "自定义窗口名称(此项为空时，默认使用绑定窗口的名称)"
                                },
                                "maskClosable": {
                                  "type": "boolean",
                                  "title": "点击空白区域关闭弹框",
                                  "default": false
                                },
                                "showCloseButton": {
                                  "type": "boolean",
                                  "title": "显示右上角“关闭”按钮(打开形式为modal/drawer时生效)",
                                  "default": true
                                }
                              }
                            },
                            {
                              "properties": {
                                "target": {
                                  "type": "string",
                                  "title": "目标组件(target)"
                                },
                                "actionName": {
                                  "enum": [
                                    "getOne",
                                    "getOneForGraphQL"
                                  ]
                                },
                                "parameters": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键"
                                      },
                                      "value": {
                                        "type": "object",
                                        "properties": {
                                          "ctxType": {
                                            "type": "string",
                                            "title": "上下文类型"
                                          },
                                          "ctxValue": {
                                            "type": "string",
                                            "title": "上下文值"
                                          }
                                        }
                                      }
                                    }
                                  },
                                  "title": "请求参数",
                                  "maxItems": 1.0
                                },
                                "entityDefId": {
                                  "type": "string",
                                  "title": "实体对象(entityDefId)"
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    },
                                    "additionalDataSource": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "主键"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          }
                                        }
                                      },
                                      "title": "其他数据来源"
                                    }
                                  }
                                },
                                "actionName": {
                                  "enum": [
                                    "save",
                                    "saveForGraphQL",
                                    "batchSave"
                                  ]
                                },
                                "entityDefId": {
                                  "type": "string",
                                  "title": "实体对象(entityDefId)"
                                },
                                "refreshMode": {
                                  "enum": [
                                    "page",
                                    "none"
                                  ],
                                  "type": "string",
                                  "title": "执行该action后是否根据变化的entity刷新对应组件内容",
                                  "default": "page"
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "entities": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "实体对象(entityDefId)"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          }
                                        }
                                      },
                                      "title": "实体"
                                    },
                                    "variables": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "主键"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          }
                                        }
                                      },
                                      "title": "变量"
                                    }
                                  }
                                },
                                "actionName": {
                                  "enum": [
                                    "startProcess",
                                    "startProcessByKeyAndRunFirstTask"
                                  ]
                                },
                                "parameters": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键"
                                      },
                                      "processDefinition": {
                                        "type": "string",
                                        "title": "流程定义(processDefinition)"
                                      }
                                    }
                                  },
                                  "title": "请求参数"
                                }
                              }
                            },
                            {
                              "properties": {
                                "actionName": {
                                  "enum": [
                                    "claimTask",
                                    "delegateTask",
                                    "assigneeTask"
                                  ]
                                },
                                "parameters": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键",
                                        "default": "taskId"
                                      },
                                      "value": {
                                        "type": "object",
                                        "properties": {
                                          "ctxType": {
                                            "type": "string",
                                            "title": "上下文类型"
                                          },
                                          "ctxValue": {
                                            "type": "string",
                                            "title": "上下文值"
                                          }
                                        }
                                      }
                                    }
                                  },
                                  "title": "请求参数"
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "entities": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "实体对象(entityDefId)"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          }
                                        }
                                      },
                                      "title": "实体"
                                    },
                                    "variables": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "主键"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          }
                                        }
                                      },
                                      "title": "变量"
                                    }
                                  }
                                },
                                "actionName": {
                                  "enum": [
                                    "setTaskVariables",
                                    "completeTask",
                                    "resolveTask"
                                  ]
                                },
                                "parameters": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键",
                                        "default": "taskId"
                                      },
                                      "value": {
                                        "type": "object",
                                        "properties": {
                                          "ctxType": {
                                            "type": "string",
                                            "title": "上下文类型"
                                          },
                                          "ctxValue": {
                                            "type": "string",
                                            "title": "上下文值"
                                          }
                                        }
                                      }
                                    }
                                  },
                                  "title": "请求参数"
                                }
                              }
                            },
                            {
                              "properties": {
                                "actionName": {
                                  "enum": [
                                    "back"
                                  ]
                                }
                              }
                            },
                            {
                              "properties": {
                                "target": {
                                  "type": "string",
                                  "title": "Drawer组件"
                                },
                                "actionName": {
                                  "enum": [
                                    "openDrawer"
                                  ]
                                }
                              }
                            },
                            {
                              "properties": {
                                "target": {
                                  "type": "string",
                                  "title": "目标组件"
                                },
                                "actionName": {
                                  "enum": [
                                    "currentUserTask"
                                  ]
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "additionalDataSource": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "主键"
                                          },
                                          "value": {
                                            "type": "object",
                                            "properties": {
                                              "ctxType": {
                                                "type": "string",
                                                "title": "上下文类型"
                                              },
                                              "ctxValue": {
                                                "type": "string",
                                                "title": "上下文值"
                                              }
                                            }
                                          }
                                        }
                                      },
                                      "title": "参数设置"
                                    }
                                  }
                                },
                                "target": {
                                  "type": "string",
                                  "title": "目标组件"
                                },
                                "actionName": {
                                  "enum": [
                                    "getOcSystemNoticeByTemplateKey"
                                  ]
                                }
                              }
                            },
                            {
                              "properties": {
                                "body": {
                                  "type": "object",
                                  "title": "请求体",
                                  "properties": {
                                    "dataSource": {
                                      "type": "string",
                                      "title": "数据来源组件(dataSource)"
                                    },
                                    "otherDataSource": {
                                      "type": "array",
                                      "items": {
                                        "type": "object",
                                        "properties": {
                                          "name": {
                                            "type": "string",
                                            "title": "主键"
                                          },
                                          "value": {
                                            "type": "string"
                                          }
                                        }
                                      },
                                      "title": "其他数据来源"
                                    }
                                  }
                                },
                                "response": {
                                  "type": "array",
                                  "items": {
                                    "type": "object",
                                    "properties": {
                                      "name": {
                                        "type": "string",
                                        "title": "主键"
                                      },
                                      "value": {
                                        "type": "object",
                                        "properties": {
                                          "ctxType": {
                                            "type": "string",
                                            "title": "上下文类型"
                                          },
                                          "ctxValue": {
                                            "type": "string",
                                            "title": "上下文值"
                                          }
                                        }
                                      }
                                    }
                                  },
                                  "title": "响应"
                                },
                                "actionName": {
                                  "enum": [
                                    "execDrools"
                                  ]
                                },
                                "parameters": {
                                  "type": "object",
                                  "title": "请求参数",
                                  "properties": {
                                    "droolsId": {
                                      "type": "string",
                                      "title": "规则引擎(droolsId)"
                                    }
                                  }
                                }
                              }
                            },
                            {
                              "properties": {
                                "scripts": {
                                  "type": "string",
                                  "title": "脚本代码"
                                },
                                "actionName": {
                                  "enum": [
                                    "jsAction"
                                  ]
                                }
                              }
                            },
                            {
                              "properties": {
                                "target": {
                                  "type": "string",
                                  "title": "目标组件(target)"
                                },
                                "actionName": {
                                  "enum": [
                                    "reset"
                                  ]
                                }
                              }
                            },
                            {
                              "properties": {
                                "target": {
                                  "type": "string",
                                  "title": "目标组件(target)"
                                },
                                "actionName": {
                                  "enum": [
                                    "clean"
                                  ]
                                }
                              }
                            }
                          ]
                        }
                      }
                    },
                    "title": "事件"
                  },
                  "eventName": {
                    "enum": [
                      "onCreate",
                      "onClick"
                    ],
                    "type": "string",
                    "title": "事件名称"
                  }
                }
              },
              "title": "事件序列"
            },
            "scripts": {
              "type": "string",
              "title": "显示规则(返回值：{visible:true/false})",
              "default": "return {visible: true};"
            },
            "visible": {
              "type": "boolean",
              "title": "可见"
            },
            "componentId": {
              "type": "string",
              "title": "组件标识"
            },
            "componentName": {
              "type": "string",
              "title": "组件名称"
            },
            "componentType": {
              "type": "string",
              "title": "componentType",
              "default": "Button",
              "visible": false,
              "required": true
            }
          }
        },
        "title": "操作列"
      },
      "background": {
        "type": "string",
        "title": "背景色（支持渐变）"
      },
      "marginLeft": {
        "type": "string",
        "title": "左外外距（单位px）"
      },
      "paddingTop": {
        "type": "string",
        "title": "上内边距（单位px）"
      },
      "titleExtra": {
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "tenant": {
              "type": "string",
              "title": "客户标识(tentant)"
            },
            "content": {
              "type": "string",
              "title": "窗口名称"
            }
          }
        },
        "title": "窗口名称配置"
      },
      "buttonAlign": {
        "enum": [
          "left",
          "center",
          "right"
        ],
        "type": "string",
        "title": "操作按钮对齐",
        "default": "left",
        "enumNames": [
          "左对齐",
          "居中",
          "右对齐"
        ]
      },
      "marginRight": {
        "type": "string",
        "title": "右外边距（单位px）"
      },
      "paddingLeft": {
        "type": "string",
        "title": "左内边距（单位px）"
      },
      "showDivider": {
        "type": "boolean",
        "title": "显示操作按钮分割线",
        "default": true
      },
      "marginBottom": {
        "type": "string",
        "title": "下外边距（单位px）"
      },
      "paddingRight": {
        "type": "string",
        "title": "右内边距（单位px）"
      },
      "componentName": {
        "type": "string",
        "title": "组件名称"
      },
      "paddingBottom": {
        "type": "string",
        "title": "下内边距（单位px）"
      },
      "windowContext": {
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "key": {
              "type": "string",
              "title": "主键"
            },
            "value": {
              "type": "string",
              "title": "键值"
            }
          }
        },
        "title": "窗口上下文"
      },
      "operationPosition": {
        "enum": [
          "top",
          "bottom"
        ],
        "type": "string",
        "title": "操作按钮显示位置",
        "default": "bottom",
        "enumNames": [
          "顶部",
          "底部"
        ]
      },
      "showMaxAndMinIcon": {
        "type": "boolean",
        "title": "显示最小化最大化按钮",
        "default": false
      },
      "uiSchemaDataCache": {
        "type": "boolean",
        "title": "开启UI界面缓存(后端)",
        "default": true
      },
      "componentDataCache": {
        "type": "boolean",
        "title": "开启组件数据缓存(前端)",
        "default": true
      },
      "enableBackgroundLoading": {
        "type": "boolean",
        "title": "开启预加载(静默加载)",
        "default": false
      }
    }
```

## Row组件Schema
```json
{
  "type": "object",
  "title": "formData",
  "required": ["componentName"],
  "properties": {
    "type": {
      "enum": ["flex"],
      "type": "string",
      "title": "布局模式"
    },
    "align": {
      "enum": ["top", "middle", "bottom"],
      "type": "string",
      "title": "flex 布局下的垂直对齐方式"
    },
    "gutter": {
      "type": "number",
      "title": "栅格间隔"
    },
    "justify": {
      "enum": ["start", "end", "center", "space-around", "space-between"],
      "type": "string",
      "title": "flex 布局下的水平排列方式"
    },
    "visible": {
      "type": "boolean",
      "title": "可见"
    },
    "componentName": {
      "type": "string",
      "title": "组件名称"
    }
  }
}
```

## Col组件Schema
```json
{
  "type": "object",
  "title": "formData",
  "required": ["componentName"],
  "properties": {
    "span": {
      "type": "number",
      "title": "栅格占位格数"
    },
    "offset": {
      "type": "number",
      "title": "栅格左侧偏移格数"
    },
    "pull": {
      "type": "number",
      "title": "栅格向左移动格数"
    },
    "push": {
      "type": "number",
      "title": "栅格向右移动格数"
    },
    "visible": {
      "type": "boolean",
      "title": "可见"
    },
    "componentName": {
      "type": "string",
      "title": "组件名称"
    }
  }
}
```

## Table组件Schema
```json
{
  "type": "object",
  "title": "formData",
  "required": ["componentName", "entityDefId"],
  "properties": {
    "entityDefId": {
      "type": "string",
      "title": "实体定义ID"
    },
    "columns": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "title": {
            "type": "string",
            "title": "列标题"
          },
          "dataIndex": {
            "type": "string",
            "title": "数据字段"
          },
          "width": {
            "type": "number",
            "title": "列宽"
          },
          "visible": {
            "type": "boolean",
            "title": "可见"
          },
          "showFilter": {
            "type": "boolean",
            "title": "显示筛选"
          },
          "bodyTextAlign": {
            "enum": ["left", "center", "right"],
            "type": "string",
            "title": "内容对齐方式"
          },
          "headTextAlign": {
            "enum": ["left", "center", "right"],
            "type": "string",
            "title": "表头对齐方式"
          }
        }
      },
      "title": "列配置"
    },
    "pagination": {
      "type": "object",
      "properties": {
        "pageSize": {
          "type": "number",
          "title": "每页条数"
        },
        "showSizeChanger": {
          "type": "boolean",
          "title": "显示页码选择器"
        },
        "showQuickJumper": {
          "type": "boolean",
          "title": "显示快速跳转"
        },
        "position": {
          "enum": ["top", "bottom", "both"],
          "type": "string",
          "title": "分页位置"
        },
        "pageSizeOptions": {
          "type": "array",
          "items": {
            "type": "string"
          },
          "title": "页码选项"
        }
      }
    },
    "size": {
      "enum": ["default", "middle", "small"],
      "type": "string",
      "title": "表格大小"
    },
    "bordered": {
      "type": "boolean",
      "title": "显示边框"
    },
    "showHeader": {
      "type": "boolean",
      "title": "显示表头"
    },
    "enableSeqno": {
      "type": "boolean",
      "title": "启用序号"
    },
    "isShowToolBar": {
      "type": "boolean",
      "title": "显示工具栏"
    },
    "isShowToolBarSearch": {
      "type": "boolean",
      "title": "显示工具栏搜索"
    }
  }
}
```