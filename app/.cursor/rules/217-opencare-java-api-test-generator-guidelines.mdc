---
description: 
globs: 
alwaysApply: false
---

# API测试用例生成规则

## 上下文
- 适用于基于API文档生成RESTful API测试用例
- 确保API符合预期行为和规范
- 验证各种操作和边界条件
- 自动化API测试过程

## 要求
- 测试类必须继承BaseRestAssuredTest基类
- 测试类名应遵循{EntityName}ApiTest命名规范
- 为每个API端点创建相应的测试方法
- 测试方法名应清晰描述测试意图，以test开头
- 使用@DisplayName注解描述测试目的
- 实现以下基本测试场景:
  * 保存实体（正常情况）
  * 批量保存实体（如适用）
  * 查询实体列表（带分页和过滤）
  * 获取实体详情
  * 删除实体（如适用）
  * 必填字段校验
- 在@BeforeEach中准备测试数据
- 在@AfterEach中清理测试资源
- 使用RestAssured发送HTTP请求并验证响应
- 为所有测试数据创建专用的生成方法
- 验证HTTP状态码、响应状态和核心字段
- 关联实体测试需设置eagerProperties属性

## 测试方法模板

### 初始化与准备
```java
private String ocId;
private Map<String, Object> testData;
private static final String ENTITY_NAME = "com.open_care.dto.{package}.{EntityName}";
private static final String API_PATH = "/api";

@BeforeEach
public void setup() {
    prepareTestData();
}

private void prepareTestData() {
    testData = createTestData();
}

private Map<String, Object> createTestData() {
    Map<String, Object> data = new HashMap<>();
    // 生成唯一标识
    String uniqueId = UUID.randomUUID().toString().substring(0, 8);
    // 设置必填字段
    data.put("field1", "value1");
    data.put("field2", "value2" + uniqueId);
    // 设置选填字段
    data.put("field3", "value3");
    return data;
}
```

### 保存测试
```java
@Test
@DisplayName("测试保存{实体名称}")
public void testSave{EntityName}() {
    // 构建保存请求
    SaveRequestDTO saveRequest = new SaveRequestDTO();
    saveRequest.setEntityName(ENTITY_NAME);
    saveRequest.setData(testData);

    // 关联实体设置
    saveRequest.setEagerProperties("relationEntity");

    // 发送请求并验证结果
    Response response = given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + "/save")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data", notNullValue())
            .extract()
            .response();

    // 保存创建的ID，用于后续测试
    ocId = response.jsonPath().getString("data.ocId");
    assertNotNull(ocId, "创建的{实体名称}ID不应为空");
}
```

### 查询测试
```java
@Test
@DisplayName("测试查询{实体名称}列表")
public void testQuery{EntityName}List() {
    // 先创建一个测试数据
    testSave{EntityName}();

    // 构建查询请求
    QueryRequestDTO queryRequest = new QueryRequestDTO();
    queryRequest.setEntityName(ENTITY_NAME);

    // 设置过滤条件
    List<FieldData> filters = new ArrayList<>();
    FieldData filter = new FieldData();
    filter.setFieldName("field1");
    filter.setOperator("equal");
    filter.setFieldValue("value1");
    filters.add(filter);
    queryRequest.setFilters(filters);

    // 设置分页信息
    queryRequest.setPagination(QueryUtils.page(10, 1));

    // 设置排序
    Map<String, String> sorter = new HashMap<>();
    sorter.put("ocId", "descend");
    queryRequest.setSorter(sorter);

    // 关联属性
    queryRequest.setEagerProperties("relationEntity");

    // 发送请求并验证结果
    given()
        .spec(requestSpec)
        .body(queryRequest)
        .when()
        .post(API_PATH + "/query")
        .then()
        .statusCode(HttpStatus.OK.value())
        .body("status", equalTo("0"))
        .body("data.data", notNullValue())
        .body("data.pagination", notNullValue())
        .body("data.pagination.current", equalTo(1))
        .body("data.pagination.pageSize", equalTo(10));
}
```

### 获取详情测试
```java
@Test
@DisplayName("测试获取{实体名称}详情")
public void testGet{EntityName}() {
    // 先创建一个测试数据
    testSave{EntityName}();

    // 发送请求并验证结果
    given()
            .spec(requestSpec)
            .body(new HashMap<>())
            .when()
            .post(API_PATH + "/get/" + ENTITY_NAME + "/" + ocId)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data.data", notNullValue())
            .body("data.data.ocId", equalTo(ocId));
}
```

### 必填字段验证测试
```java
@Test
@DisplayName("测试保存{实体名称}-必填字段校验")
public void testSave{EntityName}Validation() {
    // 创建缺少必填字段的数据
    Map<String, Object> invalidData = new HashMap<>();
    invalidData.put("field3", "value3"); // 只提供非必填字段

    // 构建保存请求
    SaveRequestDTO saveRequest = new SaveRequestDTO();
    saveRequest.setEntityName(ENTITY_NAME);
    saveRequest.setData(invalidData);

    // 发送请求并验证错误响应
    given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + "/save")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("-1"))
            .body("msg", notNullValue());
}
```

### 批量保存测试(如适用)
```java
@Test
@DisplayName("测试批量保存{实体名称}")
public void testBatchSave{EntityName}() {
    // 创建批量测试数据
    List<Map<String, Object>> batchData = createBatchTestData();

    // 构建批量保存请求
    List<SaveRequestDTO> saveRequests = new ArrayList<>();
    for (Map<String, Object> data : batchData) {
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(data);
        saveRequests.add(saveRequest);
    }

    // 发送请求并验证结果
    given()
            .spec(requestSpec)
            .body(saveRequests)
            .when()
            .post(API_PATH + "/batch/save")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data", notNullValue());
}

private List<Map<String, Object>> createBatchTestData() {
    List<Map<String, Object>> batchData = new ArrayList<>();
    // 创建多条测试数据
    for (int i = 0; i < 2; i++) {
        batchData.add(createTestData());
    }
    return batchData;
}
```

### 删除测试(如适用)
```java
@Test
@DisplayName("测试删除{实体名称}")
public void testDelete{EntityName}() {
    // 先创建一个测试数据
    testSave{EntityName}();

    // 构建删除请求
    Map<String, Object> deleteRequest = new HashMap<>();
    deleteRequest.put("entityName", ENTITY_NAME);
    List<String> ids = new ArrayList<>();
    ids.add(ocId);
    deleteRequest.put("ids", ids);

    // 发送请求并验证结果
    given()
            .spec(requestSpec)
            .body(deleteRequest)
            .when()
            .post(API_PATH + "/delete")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"));

    // 验证删除是否成功
    given()
            .spec(requestSpec)
            .body(new HashMap<>())
            .when()
            .post(API_PATH + "/get/" + ENTITY_NAME + "/" + ocId)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("-1"));
}
```

### 唯一性校验
#### 适用范围：所有具有唯一性约束的字段（如唯一索引、唯一标识、业务唯一字段等）。
#### 测试要求：
 - 必须为每个唯一性字段编写重复保存的测试用例。
 - 场景包括：先保存一条数据，再用相同唯一字段保存另一条数据，断言接口返回唯一性冲突的错误（如 status = -1，msg 包含唯一性字段提示）。
```java
@Test
@DisplayName("测试保存实体-唯一性校验")
public void testSaveEntityWithDuplicateUniqueField() {
      // 第一次保存
      Map<String, Object> data1 = createTestData();
      String uniqueValue = (String) data1.get("uniqueField");
      saveEntity(data1);

      // 第二次保存，使用相同唯一字段
      Map<String, Object> data2 = createTestData();
      data2.put("uniqueField", uniqueValue);
      given()
          .spec(requestSpec)
          .body(data2)
          .when()
          .post(API_PATH + "/save")
          .then()
          .statusCode(HttpStatus.OK.value())
          .body("status", equalTo("-1"))
          .body("msg", containsString("唯一性"));
}
```

### 字段可清空校验
#### 适用范围：所有允许置空（可选填）的字段。
#### 测试要求：
 - 必须测试字段先有值，再清空（设为""或null）后保存，断言数据库中该字段已为空。
 - 场景包括：首次保存时字段有值，二次保存时字段置空，查询详情断言该字段为空。
```java
@Test
@DisplayName("测试字段可清空")
public void testClearField() {
      // 第一次保存，字段有值
      Map<String, Object> data = createTestData();
      data.put("fieldName", "有值");
      String id = saveEntityAndGetId(data);

      // 验证已保存
      assertEquals("有值", getEntityDetail(id).get("fieldName"));

      // 第二次保存，字段清空
      data.put("id", id);
      data.put("fieldName", "");
      saveEntity(data);

      // 验证已清空
      assertTrue(getEntityDetail(id).get("fieldName") == null || "".equals(getEntityDetail(id).get("fieldName")));
}
```

## 示例

<example>
/**
 * 客户信息API测试类
 * 基于Customer-API.md文档进行测试
 */
public class CustomerApiTest extends BaseRestAssuredTest {

    private String ocId;
    private Map<String, Object> testCustomerData;
    private static final String ENTITY_NAME = "com.open_care.dto.customer.CustomerDTO";
    private static final String API_PATH = "/api";

    @BeforeEach
    public void setup() {
        prepareTestData();
    }

    private void prepareTestData() {
        testCustomerData = createCustomerTestData();
    }

    private Map<String, Object> createCustomerTestData() {
        Map<String, Object> customerData = new HashMap<>();

        // 生成唯一标识
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);

        // 设置必填字段
        customerData.put("name", "测试客户" + uniqueId);
        customerData.put("type", "PERSONAL");
        customerData.put("status", "ACTIVE");

        // 设置选填字段
        customerData.put("phone", "13800138000");
        customerData.put("email", "test" + uniqueId + "@example.com");
        customerData.put("address", "北京市朝阳区");

        return customerData;
    }

    @Test
    @DisplayName("测试保存客户信息")
    public void testSaveCustomer() {
        // 构建保存请求
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(testCustomerData);

        // 发送请求并验证结果
        Response response = given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + "/save")
                .then()
                .statusCode(HttpStatus.OK.value())
                .body("status", equalTo("0"))
                .body("data", notNullValue())
                .body("data.name", equalTo(testCustomerData.get("name")))
                .extract()
                .response();

        // 保存创建的ID，用于后续测试
        ocId = response.jsonPath().getString("data.ocId");

        assertNotNull(ocId, "创建的客户ID不应为空");
    }

    @Test
    @DisplayName("测试查询客户列表")
    public void testQueryCustomerList() {
        // 先创建一个测试数据
        testSaveCustomer();

        // 构建查询请求
        QueryRequestDTO queryRequest = new QueryRequestDTO();
        queryRequest.setEntityName(ENTITY_NAME);

        // 设置过滤条件
        List<FieldData> filters = new ArrayList<>();
        FieldData typeFilter = new FieldData();
        typeFilter.setFieldName("type");
        typeFilter.setOperator("equal");
        typeFilter.setFieldValue("PERSONAL");
        filters.add(typeFilter);

        queryRequest.setFilters(filters);

        // 设置分页信息
        queryRequest.setPagination(QueryUtils.page(10, 1));

        // 设置排序
        Map<String, String> sorter = new HashMap<>();
        sorter.put("ocId", "descend");
        queryRequest.setSorter(sorter);

        // 发送请求并验证结果
        given()
            .spec(requestSpec)
            .body(queryRequest)
            .when()
            .post(API_PATH + "/query")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data.data", notNullValue())
            .body("data.pagination", notNullValue());
    }

    @AfterEach
    public void tearDown() {
        // 清理测试资源
        System.out.println("测试完成，测试客户ID: " + ocId);
    }
}
</example>

<example type="invalid">
public class CustomerApiTest {

    @Test
    public void testSave() {
        // 直接使用HTTP客户端而不是RestAssured
        HttpClient client = HttpClient.newHttpClient();
        // 不使用统一的请求规范
        // 无明确的测试数据准备
        // 没有响应验证
    }

    @Test
    public void testQuery() {
        // 没有构建标准的查询请求
        // 没有设置过滤、分页、排序
        // 没有验证响应
    }
}
</example>

<example type="invalid">
public class CustomerApiTest extends BaseRestAssuredTest {

    // 缺少测试数据准备
    // 没有继承BaseRestAssuredTest
    // 没有定义实体名称和API路径常量

    @Test
    public void testSaveCustomer() {
        // 直接构造JSON字符串而不是使用SaveRequestDTO
        String jsonBody = "{\"name\":\"测试客户\"}";

        given()
            .body(jsonBody)
            .post("/api/save");
        // 没有验证响应
        // 没有保存创建的ID用于后续测试
    }
}
</example>