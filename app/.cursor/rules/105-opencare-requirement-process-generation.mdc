---
description: VALIDATE when REVIEWING_REQUIREMENTS to ENSURE_COMPLETENESS
globs: *.bpmn
alwaysApply: false
---
---
description: VALIDATE when REVIEWING_REQUIREMENTS to ENSURE_COMPLETENESS
globs: *.bpmn
alwaysApply: true
---


# BPMN流程图生成规则

## 上下文
- 适用于通过自然语言描述生成BPMN流程图
- 基于Camunda BPMN规范

## 头文件
- 格式要求：
 ```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="sample-diagram" targetNamespace="http://bpmn.io/schema/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
  ```

## 流程元素规范

### 1. 开始事件(StartEvent)
- 必须指定事件名称
- 每个流程图只能有一个开始事件
- 格式要求：
  ```xml
  <bpmn2:startEvent id="StartEvent_1" name="[事件名称]">
    <bpmn2:outgoing>[下一个节点ID]</bpmn2:outgoing>
  </bpmn2:startEvent>
  ```

### 2. 用户任务(UserTask)
- 必须包含以下属性：
  - id: 唯一标识符，格式为"Task_[任务标识]"
  - name: 任务名称
  - camunda:modelerTemplate: "com.open_care.bpmn.extension.user_tasks"
  - camunda:enableTaskEnterChecking: "false"
  - camunda:enableTaskNotClaimChecking: "false"
  - camunda:enableTaskNotCompleteChecking: "false"
  - camunda:enableTaskDelegateChecking: "false"
  - camunda:enableTaskAssigneeChecking: "false"
  - taskExecutors: 执行人查询语句
- 格式要求：
  ```xml
  <bpmn2:userTask id="Task_[任务标识]" name="[任务名称]" 
    camunda:modelerTemplate="com.open_care.bpmn.extension.user_tasks"
    camunda:enableTaskEnterChecking="false"
    camunda:enableTaskNotClaimChecking="false"
    camunda:enableTaskNotCompleteChecking="false"
    camunda:enableTaskDelegateChecking="false"
    camunda:enableTaskAssigneeChecking="false">
    <bpmn2:extensionElements>
      <camunda:inputOutput>
        <camunda:inputParameter name="userTaskWindow" />
        <camunda:inputParameter name="taskEnterNotifyUsers" />
        <camunda:inputParameter name="taskEnterMessageTemplate" />
        <camunda:inputParameter name="taskNotClaimTimeoutDuration" />
        <camunda:inputParameter name="taskNotClaimNotifyUsers" />
        <camunda:inputParameter name="taskNotClaimMessageTemplate" />
        <camunda:inputParameter name="taskNotCompleteTimeoutDuration" />
        <camunda:inputParameter name="taskNotCompleteNotifyUsers" />
        <camunda:inputParameter name="taskNotCompleteMessageTemplate" />
        <camunda:inputParameter name="taskDelegateUsers" />
        <camunda:inputParameter name="taskDelegateMessageTemplate" />
        <camunda:inputParameter name="taskAssigneeUsers" />
        <camunda:inputParameter name="taskAssigneeMessageTemplate" />
        <camunda:inputParameter name="taskExecutors">[执行人查询语句]</camunda:inputParameter>
      </camunda:inputOutput>
    </bpmn2:extensionElements>
    <bpmn2:incoming>[来源节点ID]</bpmn2:incoming>
    <bpmn2:outgoing>[目标节点ID]</bpmn2:outgoing>
  </bpmn2:userTask>
  ```


### 3. 排他网关(ExclusiveGateway)
- 用于条件分支流程
- 必须包含条件表达式
- 格式要求：
  ```xml
  <bpmn2:exclusiveGateway id="Gateway_[网关标识]" name="[网关名称]">
    <bpmn2:incoming>[来源节点ID]</bpmn2:incoming>
    <bpmn2:outgoing>[条件分支1ID]</bpmn2:outgoing>
    <bpmn2:outgoing>[条件分支2ID]</bpmn2:outgoing>
  </bpmn2:exclusiveGateway>
  ```

### 4. 包容网关(InclusiveGateway)
- 用于条件并行分支流程
- 基于条件表达式评估，可以激活一个或多个分支
- 合并时会等待所有已激活的分支完成
- 格式要求：
  ```xml
  <bpmn2:inclusiveGateway id="Gateway_[网关标识]" name="[网关名称]">
    <bpmn2:incoming>[来源节点ID]</bpmn2:incoming>
    <bpmn2:outgoing>[分支1ID]</bpmn2:outgoing>
    <bpmn2:outgoing>[分支2ID]</bpmn2:outgoing>
  </bpmn2:inclusiveGateway>
  ```
- 分支条件格式：
  ```xml
  <bpmn2:sequenceFlow id="Flow_[标识]" sourceRef="[源网关ID]" targetRef="[目标节点ID]">
    <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${条件表达式}</bpmn2:conditionExpression>
  </bpmn2:sequenceFlow>
  ```

### 5 并行网关(ParallelGateway)
- 用于严格的并行分支流程
- 所有分支必须同时执行
- 合并时必须等待所有分支完成
- 格式要求：
  ```xml
  <bpmn2:parallelGateway id="Gateway_[网关标识]" name="[网关名称]">
    <bpmn2:incoming>[来源节点ID]</bpmn2:incoming>
    <bpmn2:outgoing>[并行分支1ID]</bpmn2:outgoing>
    <bpmn2:outgoing>[并行分支2ID]</bpmn2:outgoing>
  </bpmn2:parallelGateway>
  ```

### 6. 结束事件(EndEvent)
- 每个流程分支必须有结束事件
- 格式要求：
  ```xml
  <bpmn2:endEvent id="EndEvent_[标识]">
    <bpmn2:incoming>[来源节点ID]</bpmn2:incoming>
  </bpmn2:endEvent>
  ```

### 7. 顺序流(SequenceFlow)
- 连接各个节点
- 条件分支必须包含条件表达式
- 格式要求：
  ```xml
  <bpmn2:sequenceFlow id="Flow_[标识]" sourceRef="[源节点ID]" targetRef="[目标节点ID]">
    <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${条件表达式}</bpmn2:conditionExpression>
  </bpmn2:sequenceFlow>
  ```

### 8. 连线标签规范：
- 网关分支连线必须添加标签
- 审核类分支标准标签：
   * 通过/同意/批准
   * 驳回/拒绝/不同意
- 标签应简洁明了，不超过4个字  

### 9. 常用条件变量：
- approved: 审核结果 (true/false)
- status: 状态码
- result: 处理结果

### 10. 图形布局规范：
- 主流程应该从左到右水平布局
- 网关分支向下布局
- 回流线路应该避免交叉
- 节点间距建议值：
  * 水平间距：100-150像素
  * 垂直间距：80-100像素
- 标签位置：
  * 节点名称：居中
  * 连线名称：连线上方居中

## 注意事项
1. 节点ID必须全局唯一
2. 确保所有分支都有结束节点
3. 网关必须配对使用
4. 条件表达式必须符合Camunda表达式规范
5. 执行人查询语句必须符合系统规范

## 图形布局规范（bpmndi）

### 1. BPMNDiagram 基本结构
```xml
<bpmndi:BPMNDiagram id="BPMNDiagram_1">
  <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
    <!-- 节点和连线的图形信息 -->
  </bpmndi:BPMNPlane>
</bpmndi:BPMNDiagram>
```

### 2. 节点图形规范（BPMNShape）
- 开始事件：
  ```xml
  <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
    <dc:Bounds x="152" y="232" width="36" height="36" />
    <bpmndi:BPMNLabel>
      <dc:Bounds x="148" y="275" width="44" height="14" />
    </bpmndi:BPMNLabel>
  </bpmndi:BPMNShape>
  ```

- 任务节点：
  ```xml
  <bpmndi:BPMNShape id="_BPMNShape_Task_1" bpmnElement="Task_1">
    <dc:Bounds x="240" y="210" width="100" height="80" />
  </bpmndi:BPMNShape>
  ```

- 网关节点：
  ```xml
  <bpmndi:BPMNShape id="_BPMNShape_Gateway_1" bpmnElement="Gateway_1" isMarkerVisible="true">
    <dc:Bounds x="395" y="225" width="50" height="50" />
    <bpmndi:BPMNLabel>
      <dc:Bounds x="385" y="195" width="70" height="14" />
    </bpmndi:BPMNLabel>
  </bpmndi:BPMNShape>
  ```

### 3. 连线图形规范（BPMNEdge）
```xml
<bpmndi:BPMNEdge id="BPMNEdge_Flow_1" bpmnElement="Flow_1">
  <di:waypoint x="188" y="250" />
  <di:waypoint x="240" y="250" />
  <bpmndi:BPMNLabel>
    <dc:Bounds x="424" y="303" width="22" height="14" />
  </bpmndi:BPMNLabel>
</bpmndi:BPMNEdge>
```

### 4. 尺寸规范
- 事件节点：36x36 像素
- 任务节点：100x80 像素
- 网关节点：50x50 像素
- 标签高度：14 像素

### 5. 间距规范
- 水平主流程节点间距：100-150 像素
- 垂直分支间距：80-100 像素
- 标签与节点间距：10-20 像素

### 6. 布局原则
1. 主流程布局：
   - 从左到右水平布局
   - 起始点坐标建议：(150, 230)
   - 每个主流程节点x坐标递增约100-150像素

2. 分支流程布局：
   - 分支向下延伸
   - 网关下方分支y坐标增加约100像素
   - 回流线避免交叉

3. 标签位置：
   - 节点标签：居中对齐
   - 网关标签：网关上方居中
   - 连线标签：连线上方居中

### 7. 图形渲染提示
- isMarkerVisible：网关节点必须设置为"true"
- 标签要避免重叠
- 连线拐点（waypoint）要避免锐角
- 平行连线保持适当间距

### 8. 最佳实践
1. 坐标系统：
   - 原点(0,0)在左上角
   - x轴向右为正
   - y轴向下为正

2. 布局优化：
   - 保持流程图整体对称
   - 控制图形大小在一屏内显示
   - 重要节点放置在视觉焦点位置

3. 可读性建议：
   - 避免连线交叉
   - 最小化连线拐点数量
   - 保持视觉流向一致性 
