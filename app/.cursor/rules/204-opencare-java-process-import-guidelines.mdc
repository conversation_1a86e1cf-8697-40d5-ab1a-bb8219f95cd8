---
description:
globs:
alwaysApply: false
---
---
name: "process-import-guidelines"
description: "流程相关类的导包路径规范及代码编写规范"

pattern: "**/*.java"
---

# 流程相关规范指南

## 1. 导包路径规范

- AbstractProcessTaskHook: com.open_care.service.process.task_hook.AbstractProcessTaskHook
- AbstractProcessInstanceHook: com.open_care.service.process.processInstance_hook.AbstractProcessInstanceHook
- AbstractProcessAttachment: com.open_care.service.process.attachment.AbstractProcessAttachment
- AbstractProcessEditTaskHook: com.open_care.service.process.task_hook.AbstractProcessEditTaskHook
- DefaultCompleteEditTaskDataDTO: com.open_care.dto.process.newProcess.completeTask.DefaultCompleteEditTaskDataDTO
- DefaultCompleteAuditTaskDataDTO: com.open_care.dto.process.newProcess.completeTask.DefaultCompleteAuditTaskDataDTO
- ProcessAuditTaskHook: com.open_care.service.process.task_hook.ProcessAuditTaskHook
- ProcessInfo: com.open_care.config.process.ProcessInfo
- OCProcessInfo: com.open_care.process.OCProcessInfo
- ProcessConfig: com.open_care.config.process.ProcessConfig
- CompleteTaskDataDTO: com.open_care.dto.process.newProcess.completeTask.CompleteTaskDataDTO
- ProcessInfoCrudService: com.open_care.service.process.processInstance_hook.ProcessInfoCrudServic





# 流程代码编写规则

流程代码编写规则如下：
- 所有流程定义必须基于bpmn规范
- 流程需注册到`ProcessDefTypeEnum`枚举中（路径：`open-care-app-entity/src/main/java/com/open_care/enums/process/ProcessDefTypeEnum.java`）
  - 参数顺序：数据库值（字符串类型数字，枚举内从上到下递增，三位数字且严格保证不与文件中的其他值重复）、流程名称、关联业务对象中文名称
- 任务节点需注册到`ProcessTaskTypeEnum`枚举中（路径：`open-care-app-entity/src/main/java/com/open_care/enums/process/ProcessTaskTypeEnum.java`）
  - 参数顺序：数据库值（字符串类型数字，枚举内从上到下递增，四位数字且严格保证不与文件中的其他值重复）、任务名称
- 所有流程必须注册到`application-process-info.yml`配置文件（路径：`open-care-app-server/src/main/resources/application-process-info.yml`）
   -processDef：流程定义，填写流程的ProcessDefTypeEnum枚举即可
   -key：填写bpmn文件中流程的id或者任务的id值
   -description：填写bpmn文件中流程的描述或者任务的描述
   -task-list: 下面包含所有任务信息
   -task-def：填写任务的ProcessTaskTypeEnum枚举即可

## 1. 流程钩子类规则

- 放在`open-care-app-server/src/main/java/com/open_care/service/process/processInstance_hook`路径
- 加上`@Service`，`@Getter`注解
- 注入`ProcessInfoCrudService`
- 继承`AbstractProcessInstanceHook`抽象类
- 重写`postStart`、`preDelete`、`postProcessEnded`、`preStartSave`方法
  * postStart：该方法中可更新关联业务实体的流程状态字段。
  * preDelete：该方法中可校验是否能进行删除操作。
  * postProcessEnded：该方法中可进行同步业务实体最终状态。
  * preStartSave：该方法先将需要开启流程的实体数据先存入数据库，然后返回该实体的Id。

```java
// 类模板示例
@Service
@Getter
public class XxxProcessHook extends AbstractProcessInstanceHook {
    @Autowired
    private ProcessInfoCrudService processInfoCrudService;

    @Override
    public void postStart(OCProcessInfo processInfo) { super.postStart(processInfo); }

    @Override
    public void preDelete(OCProcessInfo processInfo) { super.preDelete(processInfo); }

    @Override
    public void postProcessEnded(OCProcessInfo processInfo) { super.postProcessEnded(processInfo); }

    @Override
    public String preStartSave(Map<String, Object> data) { return super.preStartSave(data);}
}
```

## 2. 流程监听器规则

- 放在`open-care-app-server/src/main/java/com/open_care/service/process/attachment`路径
- 加上`@Service`，`@Getter`注解
- 继承`AbstractProcessAttachment`抽象类，实现`InitializingBean`接口
- 定义`PROCESS_DEF_TYPE_ENUM`常量、`relationEntityClass`、`processInstanceHook`等
- 重写`getProcessTaskHook`、`afterPropertiesSet`、`buildProcessTitle`方法
```java
// 类模板示例
@Service
@Getter
public class XxxProcessAttachment extends AbstractProcessAttachment implements InitializingBean {
    // 必须定义的常量（XXX替换为具体流程枚举）
    public static final ProcessDefTypeEnum PROCESS_DEF_TYPE_ENUM = ProcessDefTypeEnum.XXX;

    // 关联的业务实体类（XxxEntity替换为实际业务实体）
    private final Class<?> relationEntityClass = XxxEntity.class;

    // 注入的流程钩子实例（XxxProcessHook替换为对应的流程钩子类）
    private final XxxProcessHook processInstanceHook;

    // 流程配置实例
    private final ProcessConfig processConfig;

    // 任务钩子列表（泛型根据实际情况调整）
    private final List<ProcessTaskHook<?>> processTaskHooks;

    // 流程信息对象
    private ProcessInfo processInformation;

    // 构造函数（参数要与实际注入的bean匹配）
    @Autowired
    public XxxProcessAttachment(ProcessConfig processConfig,
                               XxxProcessHook processInstanceHook,
                               List<ProcessTaskHook<?>> processTaskHooks) {
        super(processConfig.getProcessInfo(PROCESS_DEF_TYPE_ENUM));
        this.processConfig = processConfig;
        this.processInstanceHook = processInstanceHook;
        this.processTaskHooks = processTaskHooks;
    }

    // 实现InitializingBean接口的方法
    @Override
    public void afterPropertiesSet() throws Exception {
        processInformation = processConfig.getProcessInfo(getProcessDefType());
    }

    // 获取任务钩子的实现
    @Override
    public <D extends CompleteTaskDataDTO> ProcessTaskHook<D> getProcessTaskHook(
        ProcessTaskTypeEnum processTaskType) {
        return (ProcessTaskHook<D>) processTaskHooks.stream()
            .filter(taskHook -> taskHook.matchTaskType(processTaskType))
            .findFirst()
            .orElse(null);
    }

    // 构建流程标题的默认实现（可根据需要重写）
    @Override
    public String buildProcessTitle(String entityInstanceId) {
        // 示例：return "XXX流程-" + entityInstanceId;
        return super.buildProcessTitle(entityInstanceId);
    }
}
```

## 3. 任务钩子类规则

- 放在`open-care-app-server/src/main/java/com/open_care/service/process/task_hook`路径下的包中
- 注入相应CrudService

### 审核类任务

- 加上`@Component`注解
- 定义静态内部DTO类继承`DefaultCompleteAuditTaskDataDTO`
- 继承`AbstractProcessTaskHook`抽象类
- 实现`ProcessAuditTaskHook`接口
- 重写`handleTaskType`、`convertDataToBO`、`postComplete`、`preComplete`方法
```java
// 完整审核类任务钩子示例
@Component
public class TemplateAuditTaskHook extends AbstractProcessTaskHook<TemplateAuditTaskHook.TemplateCompleteAuditTaskDataDTO>
        implements ProcessAuditTaskHook<TemplateAuditTaskHook.TemplateCompleteAuditTaskDataDTO> {

    @Override
    public ProcessTaskTypeEnum handleTaskType() {
        return ProcessTaskTypeEnum.YOUR_AUDIT_TASK_TYPE;
    }

    @Override
    public TemplateCompleteAuditTaskDataDTO convertDataToBO(Map<String, Object> data) {
        return GsonUtils.mapToObject(data, TemplateCompleteAuditTaskDataDTO.class);
    }

    @Override
    public void postComplete(OCProcessInfo processInfo, TemplateCompleteAuditTaskDataDTO data) {
        // 审核后处理逻辑（如：更新状态、发送通知等）
        // 更新状态时请使用枚举OCAuditStatusEnum
        xxxService.updateServiceRequestStatus(processInfo.getEntityInstId(), OCAuditStatusEnum.RESUBMIT_AUDITING);
        if (!data.getAuditResult()) {
            // 审核拒绝处理逻辑
        }
    }

    @Override
    public void preComplete(OCProcessInfo processInfo, TemplateCompleteAuditTaskDataDTO data) {
        // 审核前校验逻辑（如：权限校验、业务规则校验等）
    }

    @Data
    public static class XxxCompleteAuditTaskDataDTO extends DefaultCompleteAuditTaskDataDTO {
        // 可添加提交任务特有的字段（如有）
        @OcColumn(title = "XXX")
        private String xxxxx;
    }
}
```

### 提交类任务

- 加上`@Component`注解
- 定义静态内部DTO类继承`DefaultCompleteEditTaskDataDTO`
- 继承`AbstractProcessEditTaskHook`抽象类
- 重写`handleTaskType`、`doConvertDataToBO`、`preCompleteSave`、`postComplete`方法
```java
// 完整提交类任务钩子示例
@Component
public class TemplateEditTaskHook extends AbstractProcessEditTaskHook<YourEntityDTO, TemplateEditTaskHook.TemplateCompleteEditTaskDataDTO> {

    @Override
    public ProcessTaskTypeEnum handleTaskType() {
        return ProcessTaskTypeEnum.YOUR_EDIT_TASK_TYPE;
    }

    @Override
    public TemplateCompleteEditTaskDataDTO doConvertDataToBO(Map<String, Object> data) {
        return GsonUtils.mapToObject(data, TemplateCompleteEditTaskDataDTO.class);
    }

    @Override
    public void preCompleteSave(OCProcessInfo processInfo, YourEntityDTO data, List<String> editEntityFieldsPath) {
        // 提交前保存逻辑（如：数据校验、业务处理等）
        // 示例校验逻辑：
		Preconditions.checkArgument(data.getSomeField() != null, "必要字段不能为空");
    }

    @Override
    public void postComplete(OCProcessInfo processInfo, TemplateCompleteEditTaskDataDTO data) {
        // 提交后处理逻辑（如：启动后续流程、更新状态等）
        // 更新状态时请使用枚举OCAuditStatusEnum
        xxxService.updateServiceRequestStatus(processInfo.getEntityInstId(), OCAuditStatusEnum.RESUBMIT_AUDITING);
    }

    @Data
    public static class XxxCompleteEditTaskDataDTO extends DefaultCompleteEditTaskDataDTO<YourEntityDTO> {
        // 可添加提交任务特有的字段（如有）
        @OcColumn(title = "XXX")
        private String xxxxx;
    }
}
```



### 后置操作

**FlyWay脚本要求**：

```sql
    // 模板示例，请严格遵循以下格式
    INSERT INTO open_care_application.process_flow_graph_config(oc_id, active, attributes, created,
    created_by, created_by_name, dynamic_field_data, updated, updated_by, updated_by_name, initial_event_name,
    lane_height, lane_title_height, lane_width, node_height, node_width, offsety, process_definition_key, process_lane_flow_graph_configs)
    VALUES (uuid, true, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, xxxxx, 800, 30, 300, 180, 220, 30, xxxx, xxxx);
```
**需要修改的字段**：
  -oc_id字段不能与已有记录重复
  -initial_event_name字段为初始任务名称一般为提交某某申请
  -process_definition_key字段为流程图中定义的流程的唯一Id
  -process_lane_flow_graph_configs字段为json格式，该字段配置了泳道图的泳道名称及哪些任务应该在哪个泳道



## 4. 流程代码更新规则

### 1. 枚举类更新

- 流程定义更新
  - 若新增了流程定义，需在`ProcessDefTypeEnum`枚举中添加新的条目。
  - 修改已有的流程定义时，确保对应的`ProcessDefTypeEnum`中的描述和关联业务对象名称保持最新。
- 任务节点更新
  - 新增或删除任务节点时，应在`ProcessTaskTypeEnum`枚举中相应地添加或移除条目。
  - 修改任务节点名称或属性时，确保`ProcessTaskTypeEnum`中的对应条目也得到更新。

### 2. 配置文件更新

- 在`application-process-info.yml`配置文件中，根据BPMN文件的更改，更新`processDef`、`key`、`description`和`task-list`下的相关信息。
- 如果流程或任务发生了变化，确保在配置文件中正确反映了这些变化，包括新加入的任务或流程的注册信息。

### 3. 代码实现更新

- 流程钩子类
  - 当流程定义发生变动时，检查并更新相应的流程钩子类，例如`postStart`、`preDelete`等方法内可能需要根据新流程逻辑进行调整。
  - `postStart(OCProcessInfo processInfo)`：如果新流程定义在启动时需要执行额外的业务逻辑（如状态更新、日志记录等），则在此方法中添加相应的逻辑。
- 流程监听器
  - 根据流程定义或任务节点的变化，检查是否需要更新`PROCESS_DEF_TYPE_ENUM`常量、`relationEntityClass`以及`processInstanceHook`等相关成员变量。
  - 更新`PROCESS_DEF_TYPE_ENUM`常量：如果流程定义发生了变化（例如名称变更或新增了一个流程类型），需要相应地更新这个常量。
  - 更新`relationEntityClass`和`processInstanceHook`：如果业务实体或流程钩子类有所变动，则需同步更新这些成员变量。
- 任务钩子类
  - 对于审核类或提交类任务，如果任务类型或处理逻辑有变，应更新`handleTaskType`、`convertDataToBO`、`postComplete`等方法以适应新的业务需求。
  - `handleTaskType()`：如果新增或修改了任务类型，则需要在此方法中返回正确的`ProcessTaskTypeEnum`值。
  - `convertDataToBO()`：当任务数据结构发生变化时，需要更新此方法以正确转换传入的数据到业务对象。
  - `postComplete()` 和 `preComplete()`：基于新的业务需求，这两个方法可能需要更新其内部逻辑。

### 4. 数据库脚本更新

- 当涉及到泳道图的结构变化或流程定义的关键字段变更时，使用FlyWay脚本来更新数据库表`open_care_application.process_flow_graph_config`中的相关信息，确保`oc_id`、`initial_event_name`、`process_definition_key`及`process_lane_flow_graph_configs`等字段与最新的BPMN文件一致。

```
    // 模板示例，请严格遵循以下格式
    INSERT INTO open_care_application.process_flow_graph_config(oc_id, active, attributes, created,
    created_by, created_by_name, dynamic_field_data, updated, updated_by, updated_by_name, initial_event_name,
    lane_height, lane_title_height, lane_width, node_height, node_width, offsety, process_definition_key, process_lane_flow_graph_configs)
    VALUES (uuid, true, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, xxxxx, 800, 30, 300, 180, 220, 30, xxxx, xxxx);
```

