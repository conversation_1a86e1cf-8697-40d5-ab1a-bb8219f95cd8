---
description: APPLY when STYLING_UI_COMPONENTS to ENSURE_CONSISTENT_APPEARANCE
globs: ui/*.json
alwaysApply: false
---

# OpenCare UI 样式与主题规范

<version>1.1.0</version>

## Context
- 当需要为Open-Care平台UI组件定义样式和主题时应用此规则
- 规范化样式定义方法、常用样式属性、组件特定样式和颜色主题
- 确保UI界面风格一致且符合Open-Care平台的设计规范

## Requirements

### 1. 样式定义方法

样式必须通过组件的`styles`属性定义，采用JSON字符串格式：

```json
"styles": "{\"margin\":\"10px\",\"padding\":\"15px\",\"backgroundColor\":\"#f5f5f5\"}"
```

所有样式属性名必须使用驼峰命名法（camelCase），如`backgroundColor`而非`background-color`。

### 2. 常用样式属性

#### 尺寸与间距
- `width`: 宽度，可使用像素值（px）、百分比（%）或自动（auto）
- `height`: 高度，可使用像素值（px）、百分比（%）或自动（auto）
- `minWidth`: 最小宽度
- `maxWidth`: 最大宽度
- `minHeight`: 最小高度
- `maxHeight`: 最大高度
- `margin`: 外边距，可使用简写形式或分别指定四个方向
  - `marginTop`: 上外边距
  - `marginRight`: 右外边距
  - `marginBottom`: 下外边距
  - `marginLeft`: 左外边距
- `padding`: 内边距，可使用简写形式或分别指定四个方向
  - `paddingTop`: 上内边距
  - `paddingRight`: 右内边距
  - `paddingBottom`: 下内边距
  - `paddingLeft`: 左内边距

#### 字体与文本
- `fontSize`: 字体大小，通常使用像素值（px）
- `fontWeight`: 字体粗细，可使用数值（400、700等）或关键字（normal、bold等）
- `fontFamily`: 字体族
- `color`: 文本颜色，使用十六进制值（#RRGGBB）或颜色名称
- `textAlign`: 文本对齐方式，可选值：left、center、right、justify
- `lineHeight`: 行高
- `letterSpacing`: 字符间距
- `textDecoration`: 文本装饰，可选值：none、underline、line-through等
- `textOverflow`: 文本溢出处理，可选值：clip、ellipsis
- `whiteSpace`: 空白处理，可选值：normal、nowrap、pre等

#### 背景与边框
- `backgroundColor`: 背景颜色
- `backgroundImage`: 背景图片，使用url()函数
- `backgroundSize`: 背景尺寸，可选值：cover、contain等
- `backgroundPosition`: 背景位置
- `backgroundRepeat`: 背景重复方式，可选值：repeat、no-repeat等
- `border`: 边框，可使用简写形式或分别指定四个方向
  - `borderTop`: 上边框
  - `borderRight`: 右边框
  - `borderBottom`: 下边框
  - `borderLeft`: 左边框
- `borderRadius`: 边框圆角，可使用简写形式或分别指定四个角
  - `borderTopLeftRadius`: 左上角圆角
  - `borderTopRightRadius`: 右上角圆角
  - `borderBottomLeftRadius`: 左下角圆角
  - `borderBottomRightRadius`: 右下角圆角
- `boxShadow`: 盒阴影

#### 布局与定位
- `display`: 显示方式，可选值：flex、block、inline-block等
- `flexDirection`: Flex容器的主轴方向，可选值：row、column等
- `flexWrap`: Flex容器的换行方式，可选值：nowrap、wrap等
- `justifyContent`: Flex容器的主轴对齐方式，可选值：flex-start、center、flex-end等
- `alignItems`: Flex容器的交叉轴对齐方式，可选值：flex-start、center、flex-end等
- `alignSelf`: Flex项目的交叉轴对齐方式，可选值：auto、flex-start、center等
- `flexGrow`: Flex项目的放大比例
- `flexShrink`: Flex项目的缩小比例
- `flexBasis`: Flex项目的基准尺寸
- `position`: 定位方式，可选值：static、relative、absolute、fixed
- `top`: 上偏移量
- `right`: 右偏移量
- `bottom`: 下偏移量
- `left`: 左偏移量
- `zIndex`: 层叠顺序

### 3. 组件特定样式

#### Window组件
- 推荐样式：
  ```json
  "styles": "{\"padding\":\"20px\",\"backgroundColor\":\"#ffffff\",\"boxShadow\":\"0 2px 8px rgba(0,0,0,0.15)\",\"borderRadius\":\"4px\"}"
  ```
- 样式指南：
  - 使用适当的内边距（padding）确保内容不会贴近窗口边缘
  - 使用白色或浅色背景增强可读性
  - 使用轻微的阴影（boxShadow）增强深度感
  - 使用圆角（borderRadius）柔化视觉效果

#### Form组件
- 推荐样式：
  ```json
  "styles": "{\"margin\":\"10px 0\",\"padding\":\"15px\",\"backgroundColor\":\"#f5f5f5\",\"borderRadius\":\"4px\"}"
  ```
- 样式指南：
  - 使用适当的外边距（margin）和内边距（padding）增强可读性
  - 使用浅色背景与窗口背景区分
  - 使用圆角（borderRadius）保持一致的视觉风格

#### Input组件
- 推荐样式：
  ```json
  "styles": "{\"width\":\"100%\",\"height\":\"32px\",\"borderRadius\":\"4px\",\"border\":\"1px solid #d9d9d9\"}"
  ```
- 样式指南：
  - 使用100%宽度适应容器大小
  - 使用统一的高度（32px）保持一致性
  - 使用浅色边框和圆角增强视觉效果

#### Select组件
- 推荐样式：
  ```json
  "styles": "{\"width\":\"100%\",\"height\":\"32px\",\"borderRadius\":\"4px\",\"border\":\"1px solid #d9d9d9\"}"
  ```
- 样式指南：
  - 与Input组件保持一致的样式
  - 使用统一的高度和边框样式

#### Button组件
- 推荐样式：
  ```json
  "styles": "{\"marginRight\":\"8px\",\"height\":\"32px\",\"borderRadius\":\"4px\",\"padding\":\"0 15px\"}"
  ```
- 样式指南：
  - 使用适当的外边距（marginRight）分隔相邻按钮
  - 使用统一的高度（32px）保持一致性
  - 使用适当的内边距（padding）确保按钮文本有足够的空间

#### Table组件
- 推荐样式：
  ```json
  "styles": "{\"marginTop\":\"15px\",\"border\":\"1px solid #e8e8e8\",\"borderRadius\":\"4px\",\"overflow\":\"hidden\"}"
  ```
- 样式指南：
  - 使用适当的外边距（marginTop）与上方元素分隔
  - 使用浅色边框和圆角增强视觉效果
  - 使用overflow:hidden确保内容不会溢出

#### Divider组件
- 推荐样式：
  ```json
  "styles": "{\"margin\":\"16px 0\",\"borderTop\":\"1px solid #e8e8e8\"}"
  ```
- 样式指南：
  - 使用适当的外边距（margin）与相邻元素分隔
  - 使用浅色边框保持一致的视觉风格

### 4. 颜色主题

Open-Care平台使用以下颜色主题：

#### 主色调
- 主色：`#1890ff`（蓝色）
- 次要色：`#52c41a`（绿色）
- 警告色：`#faad14`（黄色）
- 危险色：`#ff4d4f`（红色）

#### 中性色调
- 标题文本：`#000000d9`（85%黑色）
- 正文文本：`#000000a6`（65%黑色）
- 次要文本：`#00000073`（45%黑色）
- 禁用文本：`#00000040`（25%黑色）
- 边框颜色：`#d9d9d9`（浅灰色）
- 分割线颜色：`#f0f0f0`（更浅的灰色）
- 背景颜色：`#f5f5f5`（最浅的灰色）
- 表格头部背景：`#fafafa`（几乎白色）

#### 按钮颜色
- 主要按钮：
  ```json
  "styles": "{\"backgroundColor\":\"#1890ff\",\"color\":\"#ffffff\",\"border\":\"1px solid #1890ff\"}"
  ```
- 次要按钮：
  ```json
  "styles": "{\"backgroundColor\":\"#ffffff\",\"color\":\"#000000a6\",\"border\":\"1px solid #d9d9d9\"}"
  ```
- 危险按钮：
  ```json
  "styles": "{\"backgroundColor\":\"#ff4d4f\",\"color\":\"#ffffff\",\"border\":\"1px solid #ff4d4f\"}"
  ```

#### 状态颜色
- 成功状态：`#52c41a`（绿色）
- 警告状态：`#faad14`（黄色）
- 错误状态：`#ff4d4f`（红色）
- 信息状态：`#1890ff`（蓝色）
- 禁用状态：`#00000040`（25%黑色）

### 5. 样式应用方法

样式可以通过以下方式应用：

#### 直接在组件中定义
通过组件的`styles`属性直接定义样式：
```json
"styles": "{\"margin\":\"10px\",\"padding\":\"15px\",\"backgroundColor\":\"#f5f5f5\"}"
```

#### 使用JavaScript动态设置
在事件处理中使用JavaScript动态设置样式：
```javascript
var component = ocWindow.getComponentByName('componentName');
component.setStyles({
  margin: '10px',
  padding: '15px',
  backgroundColor: '#f5f5f5'
});
```

#### 使用预定义的样式类
通过`className`属性应用预定义的样式类：
```json
"className": "oc-card oc-shadow"
```

### 6. 响应式设计

为确保UI在不同设备上的良好显示效果，应遵循以下响应式设计原则：

#### 流式布局
- 使用百分比宽度而非固定像素值
  ```json
  "styles": "{\"width\":\"100%\",\"maxWidth\":\"1200px\",\"margin\":\"0 auto\"}"
  ```
- 使用最大宽度（maxWidth）限制大屏幕上的内容宽度
- 使用自动边距（margin: 0 auto）居中内容

#### 弹性布局
- 使用Flex布局创建灵活的界面
  ```json
  "styles": "{\"display\":\"flex\",\"flexDirection\":\"row\",\"flexWrap\":\"wrap\",\"justifyContent\":\"space-between\"}"
  ```
- 使用flexWrap: wrap确保内容在小屏幕上换行
- 使用justifyContent和alignItems控制内容对齐方式

#### 移动优先
- 首先为移动设备设计界面，然后逐步增强
- 确保文本和控件大小适合触摸操作
- 简化移动设备上的界面，隐藏非必要元素

#### 媒体查询
- 使用媒体查询适配不同屏幕尺寸
  ```javascript
  if (window.innerWidth < 768) {
    component.setStyles({
      flexDirection: 'column',
      padding: '10px'
    });
  } else {
    component.setStyles({
      flexDirection: 'row',
      padding: '20px'
    });
  }
  ```
- 常用断点：
  - 手机：< 768px
  - 平板：768px - 992px
  - 桌面：992px - 1200px
  - 大屏：> 1200px

## Examples

<example>
Window组件样式示例：

```json
{
  "componentId": "customerWindow",
  "componentType": "Window",
  "componentName": "customerWindow",
  "title": "客户信息",
  "visible": true,
  "styles": "{\"padding\":\"20px\",\"backgroundColor\":\"#ffffff\",\"boxShadow\":\"0 2px 8px rgba(0,0,0,0.15)\",\"borderRadius\":\"4px\",\"maxWidth\":\"1200px\",\"margin\":\"0 auto\"}"
}
```
</example>

<example>
表单布局样式示例：

```json
{
  "componentId": "customerForm",
  "componentType": "Form",
  "componentName": "customerForm",
  "visible": true,
  "styles": "{\"margin\":\"10px 0\",\"padding\":\"15px\",\"backgroundColor\":\"#f5f5f5\",\"borderRadius\":\"4px\"}",
  "componentChildren": [
    {
      "componentId": "formRow",
      "componentType": "Row",
      "componentName": "formRow",
      "visible": true,
      "styles": "{\"display\":\"flex\",\"flexWrap\":\"wrap\",\"marginBottom\":\"15px\"}",
      "componentChildren": [
        {
          "componentId": "nameCol",
          "componentType": "Col",
          "componentName": "nameCol",
          "visible": true,
          "span": 12,
          "styles": "{\"padding\":\"0 8px\"}",
          "componentChildren": [
            {
              "componentId": "nameInput",
              "componentType": "Input",
              "componentName": "nameInput",
              "visible": true,
              "styles": "{\"width\":\"100%\",\"height\":\"32px\",\"borderRadius\":\"4px\",\"border\":\"1px solid #d9d9d9\"}"
            }
          ]
        },
        {
          "componentId": "ageCol",
          "componentType": "Col",
          "componentName": "ageCol",
          "visible": true,
          "span": 12,
          "styles": "{\"padding\":\"0 8px\"}",
          "componentChildren": [
            {
              "componentId": "ageInput",
              "componentType": "Input",
              "componentName": "ageInput",
              "visible": true,
              "styles": "{\"width\":\"100%\",\"height\":\"32px\",\"borderRadius\":\"4px\",\"border\":\"1px solid #d9d9d9\"}"
            }
          ]
        }
      ]
    }
  ]
}
```
</example>

<example>
按钮样式示例：

```json
{
  "componentId": "buttonGroup",
  "componentType": "Col",
  "componentName": "buttonGroup",
  "visible": true,
  "buttonBox": true,
  "buttonAlign": "right",
  "styles": "{\"marginTop\":\"20px\"}",
  "componentChildren": [
    {
      "componentId": "cancelButton",
      "componentType": "Button",
      "componentName": "cancelButton",
      "title": "取消",
      "type": "default",
      "visible": true,
      "styles": "{\"marginRight\":\"8px\",\"height\":\"32px\",\"borderRadius\":\"4px\",\"padding\":\"0 15px\",\"backgroundColor\":\"#ffffff\",\"color\":\"#000000a6\",\"border\":\"1px solid #d9d9d9\"}"
    },
    {
      "componentId": "saveButton",
      "componentType": "Button",
      "componentName": "saveButton",
      "title": "保存",
      "type": "primary",
      "visible": true,
      "styles": "{\"height\":\"32px\",\"borderRadius\":\"4px\",\"padding\":\"0 15px\",\"backgroundColor\":\"#1890ff\",\"color\":\"#ffffff\",\"border\":\"1px solid #1890ff\"}"
    }
  ]
}
```
</example>

<example>
响应式布局示例：

```json
{
  "componentId": "responsiveLayout",
  "componentType": "Row",
  "componentName": "responsiveLayout",
  "visible": true,
  "styles": "{\"display\":\"flex\",\"flexWrap\":\"wrap\",\"margin\":\"0 -8px\"}",
  "componentChildren": [
    {
      "componentId": "leftPanel",
      "componentType": "Col",
      "componentName": "leftPanel",
      "visible": true,
      "styles": "{\"flex\":\"1 1 300px\",\"minWidth\":\"300px\",\"padding\":\"0 8px\",\"marginBottom\":\"16px\"}",
      "componentChildren": []
    },
    {
      "componentId": "rightPanel",
      "componentType": "Col",
      "componentName": "rightPanel",
      "visible": true,
      "styles": "{\"flex\":\"2 1 600px\",\"minWidth\":\"300px\",\"padding\":\"0 8px\",\"marginBottom\":\"16px\"}",
      "componentChildren": []
    }
  ]
}
```
</example>

<example type="invalid">
错误的样式定义示例：

```json
// 错误：使用了非JSON格式
"styles": "margin: 10px; padding: 15px; background-color: #f5f5f5;"

// 错误：使用了连字符命名法而非驼峰命名法
"styles": "{\"margin\":\"10px\",\"padding\":\"15px\",\"background-color\":\"#f5f5f5\"}"

// 错误：颜色值格式不正确
"styles": "{\"color\":\"blue\",\"backgroundColor\":\"lightgray\"}"

// 错误：缺少单位
"styles": "{\"margin\":\"10\",\"padding\":\"15\"}"

// 错误：嵌套样式格式不正确
"styles": "{\"font\":{\"size\":\"14px\",\"weight\":\"bold\"}}"
```
</example>

## 最佳实践

### 1. 样式一致性
- 为相同类型的组件使用一致的样式
- 保持颜色、字体、间距等样式属性的一致性
- 创建样式模板，在不同组件中复用

### 2. 视觉层次
- 使用颜色、大小、间距等创建清晰的视觉层次
- 重要内容应更加突出，次要内容可以淡化
- 使用对比度增强可读性和可用性

### 3. 空白使用
- 合理使用空白（margin和padding）增强可读性
- 相关元素之间使用较小的间距，不相关元素之间使用较大的间距
- 避免界面过于拥挤或过于稀疏

### 4. 响应式设计
- 使用百分比和弹性布局
- 为不同设备优化布局
- 测试在各种屏幕尺寸上的显示效果

### 5. 性能优化
- 避免过于复杂的样式定义
- 合理使用阴影、渐变等高消耗属性
- 优化大型表格和列表的样式

### 6. 可访问性
- 确保文本与背景之间有足够的对比度
- 使用适当的字体大小，确保可读性
- 为交互元素提供足够大的点击区域

<critical>
  - 样式必须使用有效的JSON格式
  - 属性名必须使用驼峰命名法（camelCase）
  - 颜色值应使用十六进制格式（#RRGGBB）或rgba格式
  - 尺寸应指定单位（px、%、em等）
  - 样式应遵循Open-Care平台的颜色主题
  - 相同类型的组件应使用一致的样式
  - 响应式设计应考虑不同设备的显示效果
</critical>

```json
{
  "styles": "{\"margin\":\"10px\",\"padding\":\"15px\",\"backgroundColor\":\"#f5f5f5\"}"
}
```
