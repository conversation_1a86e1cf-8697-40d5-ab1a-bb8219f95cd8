---
description: GENERATE when CREATING_UI_COMPONENTS to PRODUCE_OPENCARE_UI_JSON
globs: ui/*.json
alwaysApply: false
---

# Open-Care UI生成规则

<version>1.0.0</version>

## Context
- 当需要为Open-Care平台创建UI组件时应用此规则
- 基于UIMetadata JSON格式定义UI布局、组件和交互逻辑
- 适用于生成符合Open-Care平台规范的UI界面

## references: 
  - 202-opencare-ui-requirements.mdc
  - 204-opencare-ui-interactions.mdc
  - 206-opencare-ui-web-component-formdata.mdc
  - 207-opencare-ui-web-component-schemas.mdc
  - 205-opencare-ui-styles.mdc

## Requirements

### 基本结构
- 每个UI JSON文件必须包含以下顶级属性：
  - `title`: UI窗口标题
  - `type`: 客户端类型，通常为"pc"
  - `seqno`: 序列号
  - `componentId`: 组件唯一标识符
  - `componentName`: 组件名称
  - `componentType`: 组件类型，顶级通常为"Window"
  - `visible`: 可见性
  - `styles`: 样式定义，通常为JSON字符串
  - `context`: 窗口上下文数组
  - `uiSchema`: UI结构定义
  - `permissionComponents`: 权限组件配置，默认值为{}
  - `mode`: 模式（working/published）
  - `uiSchemaDataCache`: 是否启用UI Schema数据缓存
  - `componentDataCache`: 是否启用组件数据缓存
  - `enableBackgroundLoading`: 是否启用后台加载
  - `tagNames`: 标签名称列表，默认值为null
  - `id`: 页面唯一标识，32位UUID
  - `tenantId`: 租户ID，默认值为0
  - `appDefId`: 应用定义ID，默认值为f09e7a9d-13a0-4536-a0ed-8aeaa836be64
  - `appInstId`: 应用实例ID,默认值为f09e7a9d-13a0-4536-a0ed-8aeaa836be64
  - `created`: 创建时间
  - `updated`: 更新时间

### uiSchema结构
- `uiSchema`必须包含以下子属性：
  - `UIData`: 包含UI组件树的完整定义
  - `metaData`: 包含组件元数据定义的数组
  - `client`: 客户端类型，通常为"pc"

### 生成页面元数据时，只需生成metaData中的JSON片段即可

### metaData结构
-每个组件的 metaData 配置包含以下基础字段：
  - icon: 组件图标（如："credit-card", "table", "menu-fold"等）
  - label: 组件标签（如："Layout", "Row", "Table"等）
  - title: 组件标题（如："布局", "行", "表格"等）
  - layout: 是否为布局组件（true/false）
  - category: 组件类别（如："容器组件", "数据展示"等）
  - schema: 组件schema定义（严格按照 @207-opencare-ui-web-component-schemas.mdc中的模板来生成）
    - type: schema类型（通常为"object"）
    - title: schema标题（通常为"formData"）
    - required: 必填字段列表（如：["componentName", "title"]）
    - properties: 属性定义（详细的属性配置）
  - formData: 组件表单数据（实际的配置值）（严格按照 @206-opencare-ui-web-component-formdata.mdc中的组件属性来生成）
  - uiSchema: UI配置数据（可以为空对象）
  - componentId: 组件唯一标识，32位UUID
  - componentName: 组件名称，全局唯一
  - children: 子组件配置列表（必需字段，用于表达组件层级关系）

### 组件层次结构
- 组件通过`children`属性形成层次结构
- 常见的布局组件层次：Layout > Row > Col > 具体组件
- 每个组件必须具有唯一的`componentId`和`componentName`

### metaData 元数据中组件层级规则说明
1. **Layout组件**：
   - 作为顶层容器，必须包含 children 字段
   - children 中可以包含任意类型的子组件

2. **Row组件**：
   - 必须包含 children 字段
   - children 中只能包含 Col 组件

3. **Col组件**：
   - 必须包含 children 字段
   - children 中可以包含除 Row 外的任意组件

4. **Card组件**：
   - 必须包含 children 字段
   - children 中可以包含任意组件

5. **Tabs组件**：
   - 必须包含 children 字段
   - children 中只能包含 TabPane 组件

6. **TabPane组件**：
   - 必须包含 children 字段
   - children 中可以包含任意组件

7. **Form组件**：
   - 必须包含 children 字段
   - children 中可以包含表单类组件和布局组件

### 注意事项
1. 每个容器类组件必须包含 children 字段，即使暂时没有子组件（此时为空数组）
2. 组件的层级关系必须严格遵循组件层级规则
3. children 中的组件配置必须包含完整的 metadata 信息

### 组件类型
- 容器组件：Layout, Row, Col, Form
- 表单组件：Input, Select, Checkbox, Radio, DatePicker, TimePicker
- 数据展示组件：Table, List
- 操作组件：Button
- 布局组件：Divider

### 事件与动作
- 通过`events`数组定义组件事件
- 每个事件包含：
  - `eventName`: 事件名称（如onClick, onCreate）
  - `actions`: 动作数组
- 动作类型包括：
  - `actionName`: 动作名称（如save, query, openWindow, jsAction等）
  - 特定动作的参数

### 样式定义
- 样式通过`styles`属性定义，为JSON字符串格式
- 组件特定样式通过各自的属性定义

### 数据绑定
- 表单组件通过`entityDefId`和`fieldId`绑定到数据实体
- 表格组件通过`columns`定义数据列

## 生成UI的步骤

1. 确定UI的整体结构和布局
2. 定义窗口组件及其基本属性
3. 构建组件层次结构（Layout > Row > Col > 具体组件）
4. 为每个组件分配唯一的componentId和componentName
5. 定义组件的属性、样式和事件
6. 配置数据绑定（entityDefId和fieldId）
7. 设置组件间的交互逻辑
8. 添加必要的元数据定义
9. 验证JSON结构的完整性和正确性

<critical>
  - 每个组件必须有唯一的componentId和componentName
  - 组件层次结构必须遵循Open-Care平台的规范
  - 数据绑定必须正确配置entityDefId和fieldId
  - 事件和动作必须按照平台支持的类型定义
  - 样式必须使用JSON字符串格式
  - 顶级组件必须时Layout，Layout中的children只能是Row组件
</critical> 

## 响应式布局规则

{
  "响应式布局": {
    "栅格系统": "24栅格",
    "常用列宽配置": [
      {"场景": "搜索表单字段", "span": 6},
      {"场景": "表单字段标签", "labelCol": {"span": 6}},
      {"场景": "表单字段内容", "wrapperCol": {"span": 18}}
    ],
    "移动端适配": {
      "小屏幕": {"字段列宽": 12},
      "超小屏幕": {"字段列宽": 24}
    }
  }
} 

## 组件命名规则细化

1. 容器组件：[类型][序号]，如Row1, Col1
2. 表单组件：[字段名][类型]，如nameInput, emailInput
3. 表格列：[字段名]Column，如nameColumn, emailColumn
4. 按钮组件：[功能]Button，如queryButton, addButton 