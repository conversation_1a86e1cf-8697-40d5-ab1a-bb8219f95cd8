---
description: 
globs: 
alwaysApply: false
---
---
name: "import-path-guidelines"
description: "常用类的导包路径规范"
pattern: "**/*.java"
---

# 常用类的导包路径规范

## CRUD相关
- BaseController类: com.open_care.controller.com.open_care.controller
- 实体基类：com.open_care.sys.OCBase
- JSON对象接口：com.open_care.core.IBaseJsonObject
- DTO基类接口：com.open_care.dto.IBaseDTO
- OcColumn注解：com.open_care.annotation.OcColumn
- JsonFormat注解：com.fasterxml.jackson.annotation.JsonFormat
- JsonAdapter注解：com.google.gson.annotations.JsonAdapter
- OCDateTypeAdapter类：com.open_care.adapter.OCDateTypeAdapter
- OCDateTimeTypeAdapter类：com.open_care.adapter.OCDateTimeTypeAdapter
- DeleteParamRequest：com.open_care.api.common.delete.DeleteParamRequest
- SaveRequestDTO：com.open_care.api.common.dto.crud.SaveRequestDTO
- GetRequestDTO：com.open_care.api.common.dto.crud.GetRequestDTO
- QueryRequestDTO：com.open_care.api.common.dto.crud.QueryRequestDTO

## 微服务相关
- AbstractAppMicroEntityCrudService: com.open_care.service.crud.AbstractAppMicroEntityCrudService
- AbstractAppLocalEntityCrudService: com.open_care.service.crud.AbstractAppLocalEntityCrudService
- OrderAppService: com.open_care.service.microServices.microServicesImpl.OrderAppService
- CustomerAppService: com.open_care.service.microServices.microServicesImpl.CustomerAppService
- ProductAppService: com.open_care.service.microServices.microServicesImpl.ProductAppService
- ApplicationService: com.open_care.service.microServices.microServicesImpl.ApplicationService

## 工具类
- ResponseUtil: com.open_care.util.ResponseUtil
- Assert: cn.hutool.core.lang.Assert
- ObjectUtil: cn.hutool.core.util.ObjectUtil
- BooleanUtil: cn.hutool.core.util.BooleanUtil
- CollUtil: cn.hutool.core.util.CollUtil
- StrUtil: cn.hutool.core.util.StrUtil 