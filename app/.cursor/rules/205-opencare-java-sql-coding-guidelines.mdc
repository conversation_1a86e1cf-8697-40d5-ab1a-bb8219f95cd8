---
description: 
globs: 
alwaysApply: false
---
---
name: "sql-coding-guidelines"
description: "SQL语句编写规范"
pattern: "**/*.sql"
---

# 视图SQL语句修改规则

```markdown
# 视图SQL语句修改规则

## 位置与命名规范
- SQL文件放在open-care-app-server/src/main/resources/db/migration/postgresql/repeatable路径
- 文件命名必须以R__create_view_为前缀
- 视图名称必须以v_开头
- 视图名称应清晰表达其包含的主要数据内容
- 视图名称使用下划线分隔单词，全部小写

## 设计原则
- 只包含业务需要的字段，避免SELECT *
- 字段必须有明确别名，与实体属性名一致，使用小驼峰
- 数据库关键字使用全大写
- 明确指定JOIN类型
- JOIN条件包含所有相关联字段

## 性能优化
- 避免子查询，使用WITH语句替换
- 避免ORDER BY，除非必要
- 避免基于其他视图的视图，最多一层嵌套
- 为频繁使用的过滤条件创建索引
- 使用UNION ALL代替UNION，除非需要去重

## 文档规范
- 添加注释说明视图用途
- 注释包含创建者、创建日期和最后修改日期
- 说明主要业务场景和使用限制
- 对复杂计算字段说明计算逻辑

## 维护规范
- 使用CREATE OR REPLACE VIEW语法
- 定期检查视图性能，优化低效视图
```

# Flyway版本化脚本编写规范

```markdown
# Flyway版本化脚本编写规范

## 存放路径与命名规范
- 放在open-care-app-server/src/main/resources/db/migration/postgresql/versioned目录
- 命名格式：V{年月日}{序号}__{描述}.sql
  - 例如：V20250304502__update_sys_setting.sql
  - 年月日格式：YYYYMMDD
  - 序号：3位数字
  - 描述：下划线连接的英文单词
- 版本号必须唯一

## 内容规范
- 注释：
    - 在脚本开头添加简短的中文注释说明脚本的目的
    - 例如：`--更新服务单结束能生成随访参数`
  - SQL语句格式：
    - 使用大写表示SQL关键字（如SELECT, UPDATE, INSERT, CREATE等）
    - 表名和列名使用小写
    - 使用缩进提高可读性
    - 复杂查询使用适当的换行和缩进
  - 表名规范：
    - 完整表名格式：`{schema}.{table_name}`
    - 例如：`open_care_application.ocsys_setting`
    - 常见schema包括：
      - open_care_application
      - open_care_customer
      - open_care_order
      - open_care_product
  - 操作类型：
    - 每个脚本通常专注于一种类型的操作，如：
      - 更新数据：update_xxx
      - 插入数据：insert_xxx
      - 创建索引：create_index
      - 修改列：alter_column_xxx
      - 修复历史数据：fixed_history_data
  - 冲突处理：
    - 插入数据时，使用`ON CONFLICT (oc_id) DO NOTHING`避免重复插入

## 安全性规范
- 脚本应当是幂等的
- 创建索引时使用IF NOT EXISTS
- 插入数据时使用ON CONFLICT子句

## 最佳实践
- 每个脚本专注于一个特定变更
- 避免混合多种不相关操作
- 避免破坏性变更
- 添加新列时考虑默认值

## 常见操作模板
  - 更新系统设置：
```
    UPDATE open_care_application.ocsys_setting
    SET param_value = '新值',
        updated = '当前时间戳',
        updated_by = '操作人ID',
        updated_by_name = '操作人名称',
        version = version + 1
    WHERE oc_id = '目标记录ID';
    ```
  - 插入数据：
    ```
    INSERT INTO schema.table_name (oc_id, field1, field2, ...)
    VALUES ('唯一ID', 'value1', 'value2', ...)
    ON CONFLICT (oc_id) DO NOTHING;
    ```
  - 创建索引：
    ```
    CREATE INDEX IF NOT EXISTS index_name ON schema.table_name USING 索引类型 (column_name);
    ```
  - 修改列类型：
    ```
    ALTER TABLE schema
```
