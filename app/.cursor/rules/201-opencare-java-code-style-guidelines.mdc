---
description: 
globs: 
alwaysApply: false
---
---
name: "code-style-guidelines"
description: "Java代码风格和编码规范"
pattern: "**/*.java"
---

# Java代码风格和编码规范

## 代码风格规范
- import导入时不能出现*，例如：import java.util.*;
- 判断对象是否是null，使用Objects.isNull(object)，不要使用object == null
- 使用Bean Validation来实现验证，例如：@Valid, custom validators
- 不能使用魔法值，使用常量来替代，例如：public static final String CUSTOMER_NAME = "customerName";
- 使用Java 17 的特性，例如：Lambda表达式，Stream API，Consumer，Function，Supplier，Predicate，Optional等
- 使用guard clause（守卫子句），例如：if (Objects.isNull(customer)) { return; }
- 使用集合类时必须明确指定泛型的类型，例如：List<Customer> customerList = new ArrayList<>();
- 一个函数的代码行（刨除注释、日志等代码）不能超过20行，超过后要考虑某些代码能抽取成可复用的函数
- 一个函数中，只能出现一层if-else，超过后要考虑某些代码能抽取成可复用的函数
- 一个函数中，只能出现一层for循环，超过后要考虑某些代码能抽取成可复用的函数
- 一个函数中，不能出现else，采用如下方式解决：
  - guard clause（守卫子句）来替代
  - 策略模式（strategy pattern） 
  - 工厂类和Consumer,Function,Supplier的结合
- 尽量使用Hutool库所提供的函数
- 抛出的异常的message要清晰明了且附带上下文参数便于快速明确识别异常信息，例如：throw new IllegalArgumentException("Customer 'xiaoming' is required");
- 字符串比较：使用hutool工具类中的StrUtil.equals方法
- 布尔比较：使用hutool工具类中的BooleanUtil.isTrue或者BooleanUtil.isFalse方法
- 其他对象比较：使用hutool工具类中的ObjectUtil.equals或者ObjectUtil.notEqual方法
- 字符串判空：使用hutool工具类中的StrUtil.isBlank或者StrUtil.isNotBlank方法
- 集合判空：使用hutool工具类中的CollUtil.isEmpty或者CollUtil.isNotEmpty方法。另外，对集合进行相关操作时，为避免空指针异常可使用CollUtil.emptyIfNull
- 其他对象判空：使用hutool工具类中的ObjectUtil.isNull或者ObjectUtil.isNotNull方法
- 参数校验：使用hutool工具类中的Assert.isTrue或者Assert.isFalse方法，例如Assert.isTrue(name == gender, "not equal");
