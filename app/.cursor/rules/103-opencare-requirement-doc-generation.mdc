---
description: 根据本规则生成符合规范的业务需求规格说明书
globs: *.docx
alwaysApply: false
---

# 需求规格说明书生成规则

## 规则用途
本规则用于指导AI根据业务需求素材文档，生成符合《需求规格说明书模板.docx》格式的具体业务需求文档。AI将根据企业业务核心模型规则集和需求规格说明书模板，自动生成结构化、标准化的业务需求文档。

## 文档结构规则

### 基本结构
- **封面**：包含文档标题、作者、日期等基本信息
- **更改历史**：记录文档的版本变更情况
- **目录**：自动生成的文档目录
- **摘要**：包含文档目的、预期读者、参考资料、术语/概念、TODO
- **系统功能综述**：包含业务能力组件、业务能力分布模型、应用架构图、总体业务流程图、总体用例图、系统功能列表、数据架构、用户范围、组织架构、岗位角色说明
- **系统功能需求详述**：按一级功能、二级功能组织，包含功能简介、功能路径、用例图及场景描述、业务流程图及说明、状态图及说明、岗位角色权限说明、系统功能说明（包含功能界面UI mock、界面元素说明、逻辑功能说明）、整体功能逻辑说明
- **非功能性需求**：包含性能、安全、可用性等非功能性需求
- **统一对接需求**：包含对接系统名称、功能点系统对接交互图及说明
- **附录**：包含参考资料、图目录、表目录

### 章节层级结构
- 一级标题：如"系统功能综述"、"系统功能需求详述"等
- 二级标题：如"业务能力组件"、"一级功能名称1"等
- 三级标题：如"二级功能名称1"等
- 四级标题：如"功能简介"、"功能路径"等

## AI生成指令

### 1. 文档结构生成指令
- 严格按照需求规格说明书模板的结构组织文档
- 保持章节层级结构的一致性和完整性
- 确保所有必要的章节和小节都被包含
- 生成合适的标题和编号

### 2. 内容要素生成指令
- 根据业务需求素材，提取关键信息填充到对应章节
- 对于缺失的信息，基于业务核心模型规则进行合理推导和补充
- 确保生成的内容符合业务逻辑和技术可行性
- 保持内容的一致性和完整性

### 3. 图表生成指令
- 根据业务流程描述，生成标准的业务流程图
- 根据功能描述，生成符合UML标准的用例图和状态图
- 根据系统架构描述，生成清晰的应用架构图
- 根据界面需求，设计符合用户体验的UI mock
- 确保所有图表有清晰的标题、编号和说明文字

### 4. 术语处理指令
- 识别业务需求素材中的专业术语和概念
- 在术语/概念表中提供准确的解释说明
- 在文档中保持术语使用的一致性
- 对于首次出现的术语，提供必要的解释

### 5. TODO项处理指令
- 识别业务需求素材中的不确定或待完善的内容
- 在TODO部分列出这些待完成的事项
- 为每个TODO项提供清晰的描述和建议的解决方向
- 在相关章节中标注TODO项的位置

## AI生成策略

### 1. 业务实体映射策略
- 将企业业务核心模型中的产品模型映射到系统功能需求
- 将客户模型映射到用户范围和岗位角色说明
- 将订单模型映射到业务流程和功能需求
- 将销售策略映射到系统功能和业务规则
- 将服务流程映射到系统流程和功能需求

### 2. 业务场景转换策略
- 将销售场景转换为具体的系统功能和流程
- 将服务场景转换为服务相关的功能和流程
- 将结算场景转换为支付和财务相关的功能和流程
- 确保场景描述的完整性和一致性

### 3. 需求分类组织策略
- 按业务域对需求进行分类和组织
- 按功能模块对需求进行分类和组织
- 按优先级对需求进行分类和组织
- 确保分类的合理性和完整性

### 4. 需求描述生成策略
- 生成清晰、准确、具体的功能描述
- 包含业务背景、功能定义、业务规则和操作流程
- 使用统一的术语和表述方式
- 避免模糊、不确定的表述

### 5. 验收标准生成策略
- 生成功能验收标准，包括功能完整性和正确性
- 生成性能指标，包括响应时间和并发能力
- 生成数据要求，包括数据准确性和安全性
- 生成异常处理要求，包括异常识别和处理方案

## 内容生成原则

### 1. 内容质量控制
- 确保内容的准确性和一致性
- 避免生成矛盾或冲突的内容
- 避免生成过于技术化的语言
- 确保内容的可读性和易理解性
- 确保内容的完整性和逻辑性

### 2. 专业性控制
- 确保使用正确的业务术语和技术术语
- 确保业务流程和规则的准确性
- 确保系统功能描述的专业性
- 确保非功能性需求的合理性
- 确保对接需求的准确性

### 3. 格式规范控制
- 确保生成的文档符合规范的格式要求
- 标题使用正确的层级格式
- 列表使用规范的项目符号或编号
- 表格有清晰的表头和内容
- 图表有清晰的标题、编号和说明

## 文档生成流程

### 1. 需求素材分析
- 分析业务需求素材的结构和内容
- 识别关键的业务实体、流程和规则
- 提取可用于生成文档的信息
- 识别信息缺口和不确定性

### 2. 文档框架构建
- 根据需求规格说明书模板构建文档框架
- 确定各章节的内容范围和重点
- 规划图表的位置和类型
- 准备术语表和TODO列表

### 3. 内容生成与填充
- 根据业务需求素材生成各章节的内容
- 根据业务核心模型规则补充缺失的内容
- 生成必要的图表和UI mock
- 填充术语表和TODO列表

### 4. 内容审核与优化
- 检查内容的完整性和一致性
- 检查术语使用的准确性和一致性
- 检查图表与文字描述的一致性
- 优化内容的表述和组织

### 5. 文档完善与输出
- 完善文档的格式和样式
- 生成目录、图目录和表目录
- 检查文档的整体质量
- 输出最终的需求规格说明书

## 注意事项
1. AI应严格遵循需求规格说明书模板的结构和格式
2. AI应基于实际业务需求素材生成内容，避免虚构或臆测
3. 对于业务需求素材中不明确的部分，AI应标记为TODO项
4. AI应确保生成的内容符合业务逻辑和技术可行性
5. AI应使用统一的术语和表述方式，保持文档的一致性
6. AI应关注不同功能之间的依赖关系和交互方式
7. AI应考虑系统的可扩展性和未来发展需求
8. AI生成的图表应清晰、准确、易于理解
9. AI应避免生成过于技术化的语言，确保非技术人员也能理解
10. AI应确保文档的可读性和易理解性

## 规则文件管理

### 规则文件存放位置
- 所有生成的AI规则文件（.mdc格式）必须保存到项目的.cursor/rules目录中
- 规则文件应使用有意义的编号前缀（如003-）和描述性名称
- 确保规则文件使用正确的扩展名（.mdc）
- 规则文件应遵循Cursor规则文件的格式要求

### 规则文件更新管理
- 当业务需求发生变化时，相应更新规则文件
- 保留规则文件的版本历史
- 在更新规则文件时，确保与其他规则文件的一致性
- 更新后的规则文件应立即放入.cursor/rules目录