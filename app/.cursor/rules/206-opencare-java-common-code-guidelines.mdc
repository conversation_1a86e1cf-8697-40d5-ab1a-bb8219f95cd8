---
description: 
globs: *.java
alwaysApply: true
---
# OpenCare 通用代码编写规范指南

## 目录
- [定时任务代码规范](mdc:#定时任务代码规范)
- [飞书运维消息推送规范](mdc:#飞书运维消息推送规范)
- [WebSocket消息推送规范](mdc:#websocket消息推送规范)
- [JsonLogic规则校验规范](mdc:#jsonlogic规则校验规范)
- [系统参数配置规范](mdc:#系统参数配置规范)

## 定时任务代码规范

### 位置要求
- 所有定时任务类必须位于 `open-care-app-server/src/main/java/com/open_care/service/powerjob` 包下
- 类命名需参考该路径下其他类的命名规则

### 代码要求
- 必须实现 `tech.powerjob.worker.core.processor.sdk.BasicProcessor` 接口
- 类上必须添加注解：
  - `@Service`
  - `@Log4j2`
- 必须实现并添加以下方法：
  ```java
  @Override
  @DataAuthorityIgnoreCheck
  public ProcessResult process(TaskContext context) throws Exception {
      return null;
  }
  ```

## 飞书运维消息推送规范

### 前置准备
1. 在 `com.open_care.enums.sys.SystemOperationNotificationModule` 中创建对应枚举类型

### 实现步骤
1. 注入依赖：
   ```java
   @Autowired
   private SystemOperationNotificationService systemOperationNotificationService;
   ```

2. 发送消息：
   ```java
   systemOperationNotificationService.sendSystemOperationMessageByAsync(
       SystemOperationNotificationType枚举,  // 参数1
       SystemOperationNotificationModule枚举, // 参数2
       "消息标题",                           // 参数3
       "消息内容"                           // 参数4
   );
   ```

3. 错误日志记录：
   - 使用 `log.error` 输出错误日志
   - 必须包含明确的上下文信息和操作实体ID

## WebSocket消息推送规范

### 配置步骤
1. 在 `com.open_care.enums.message.OCNotificationMessageType` 中定义新消息类型枚举

2. 编写FlyWay脚本配置消息模板：
   ```sql
   INSERT INTO "open_care_application"."ocnotification_type_config" 
   ("oc_id", "active", "attributes", "created", "created_by", "created_by_name", 
    "dynamic_field_data", "updated", "updated_by", "updated_by_name", "assoc_entity_name",
    "content_template", "title_template", "type", "window_name", "version")
   VALUES 
   (uuid, 't', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 
    'com.open_care.event.OCCustomerEvent', 'xxx', 'xxx', '500', 
    'FlowUpTaskEditor_To_DO_List_Editor', 0);
   ```

### 实现步骤
1. 注入服务：
   ```java
   @Autowired
   private UserNotificationMessageService userNotificationMessageService;
   ```

2. 发送消息：
   ```java
   userNotificationMessageService.sendNotificationMessageToUsers(
       userId,        // 接收用户ID
       messageType,   // 消息枚举类型
       entityId,      // 操作实体ID
       variableMap    // 模板变量Map
   );
   ```

## JsonLogic规则校验规范

### 实体配置
1. 在业务实体类中添加规则表达式字段：
   ```java
   private OCQueryBuilderRuleExpression ruleExpression;
   ```

### 实现步骤
1. 在 `com.open_care.queryBuilder` 包下定义规则匹配上下文DTO

2. 注入服务：
   ```java
   @Autowired
   private RuleMatchService ruleMatchService;
   ```

3. 进行规则匹配：
   ```java
   ruleMatchService.matchRule(contextDTO, ruleExpression.getJsonLogicValue());
   ```

### 数据库配置
1. 在 `R__ad_func_and_query_build_config.sql` 中配置：
   ```sql
   -- 配置查询构建器
   INSERT INTO open_care_config.ad_query_builder_config 
   (id, app_def_id, app_inst_id, client, context_class_name)
   VALUES 
   ('com.open_care.dto.xxx.ContextDTO', 'xxx', 'xxx', 'open-care',
    'com.open_care.dto.xxx.ContextDTO');

   -- 配置字段
   INSERT INTO open_care_config.ad_query_builder_field_config 
   (id, field_settings, label, value_type, field_id, query_builder_config_id)
   VALUES 
   ('xxx', '{"multiSelect": true}', 'xxx', 'FIELD', 'xxx', 
    'com.open_care.dto.xxx.ContextDTO');
   ```

## 系统参数配置规范

### 配置步骤
1. 在 `versioned` 目录下编写FlyWay脚本：
   ```sql
   INSERT INTO "open_care_application"."ocsys_setting" 
   ("oc_id", "active", "note", "param_name", "param_value", "web_use")
   VALUES 
   (uuid, 't', 'xxx', 'paramName', 'paramValue', 't')
   ON CONFLICT (oc_id) DO UPDATE
   SET active=EXCLUDED.active, 
       note=EXCLUDED.note,
       param_name=EXCLUDED.param_name,
       param_value=EXCLUDED.param_value,
       web_use=EXCLUDED.web_use;
   ```

### 使用步骤
1. 在 `SysSettingConst` 中定义常量

2. 注入服务：
   ```java
   @Autowired
   private SystemSettingService systemSettingService;
   ```

3. 获取配置：
   ```java
   systemSettingService.getSysSettingValueByParamNameAndSettingTypeCode(
       paramName, 
       settingTypeCode
   );
   ```

> 💡 **提示：** 本文档中的所有示例代码仅供参考，实际使用时请根据具体业务需求进行调整。

> ℹ️ **注意：** 所有的配置更改都应该通过FlyWay脚本进行，以确保环境一致性。