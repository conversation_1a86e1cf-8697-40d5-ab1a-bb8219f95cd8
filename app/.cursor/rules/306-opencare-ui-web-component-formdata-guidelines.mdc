---
description: 
globs: ui/*.json
alwaysApply: false
---
 # 组件Schema配置规则说明文档

---
description: 定义UI Web端各组件的formData配置信息
globs: *.json
alwaysApply: false
---

## Context
- 当需要为Open-Care平台UI组件定义formData属性时应用此规则

## 布局组件

### Window组件的formData
- componentType: "Window"
- title: 窗口标题
- displayBreadcrumb: 是否显示面包屑导航
- displayTitle: 是否显示标题
- windowContext: 窗口上下文数据
  - key: 上下文键
  - value: 上下文值
- showMaxAndMinIcon: 是否显示最大化最小化图标
- operationPosition: 操作按钮位置（top/bottom）
- componentDataCache: 是否启用组件数据缓存
- uiSchemaDataCache: 是否启用UI Schema数据缓存
- enableBackgroundLoading: 是否启用后台加载
- titleExtra: 标题栏额外内容配置
- tag: 窗口标签配置
- closeMsg: 关闭提示消息
- buttonAlign: 按钮对齐方式（left/center/right）
- showDivider: 是否显示操作按钮分割线
- operation: 操作按钮配置
  - name: 按钮名称
  - type: 按钮类型（default/primary/danger/dashed/icon/link）
  - visible: 是否可见
  - events: 按钮事件配置
    - eventName: 事件名称
    - actions: 事件动作列表
      - actionName: 动作名称
      - scripts: 执行脚本
- background: 背景样式
- paddingLeft: 左内边距
- paddingRight: 右内边距
- paddingTop: 上内边距
- paddingBottom: 下内边距
- marginTop: 上外边距
- marginRight: 右外边距
- marginBottom: 下外边距
- marginLeft: 左外边距
- events: 事件配置
  - eventName: 事件名称（onClick/onChange/onCreate/onInit/onDoubleClick）
  - actions: 事件动作
    - actionName: 动作名称
    - scripts: 执行脚本
- componentChildren: 子组件配置

### Row组件formData
- componentType: "Row"
- align: 垂直对齐方式（top/middle/bottom）
- justify: 水平对齐方式（start/end/center/space-around/space-between）
- gutter: 栅格间隔

### Col组件formData
- componentType: "Col"
- span: 栅格占位格数（1-24）
- offset: 栅格左侧偏移格数
- pull: 栅格向左移动格数
- push: 栅格向右移动格数
- anchorId: 组件锚点ID
- buttonBox: 是否显示为按钮盒子（true/false）
- viewType: 渲染模式（normal）
- buttonAlign: 按钮对齐方式（left/center/right）
- paddingTop: 上内边距
- paddingRight: 右内边距
- paddingBottom: 下内边距
- paddingLeft: 左内边距

### Card 卡片组件
- componentType: "Card"
- title: 卡片标题
- type: 卡片类型（inner/default）
- bordered: 是否有边框
- headStyle: 标题区域样式（JSON字符串）
- bodyStyle: 内容区域样式（JSON字符串）
- windowId: 关联窗口ID
- windowComponentName: 关联窗口组件名称
- showMiniOperator: 是否显示迷你操作器
- miniOperatorType: 迷你操作器类型（icon/text）
- miniOperatorContent: 迷你操作器内容
- miniOperatorTrigger: 迷你操作器触发方式（click/hover）
- miniOperatorPlacement: 迷你操作器位置
- miniOperatorWidth: 迷你操作器宽度
- operation: 操作按钮配置
  - name: 按钮名称
  - type: 按钮类型（primary/default等）
  - visible: 是否可见
  - events: 按钮事件配置
    - eventName: 事件名称
    - actions: 事件动作列表
      - actionName: 动作名称
      - scripts: 执行脚本
- paddingLeft: 左内边距
- paddingRight: 右内边距
- paddingTop: 上内边距
- paddingBottom: 下内边距
- marginTop: 上外边距
- marginRight: 右外边距
- marginBottom: 下外边距
- marginLeft: 左外边距
- componentChildren: 子组件配置

### Tab 标签页组件
- componentType: "Tabs"
- defaultActiveKey: 默认激活的标签页
- tabPosition: 标签位置（top/bottom/left/right）
- type: 标签类型（line/card/borderRadius/editable-card）
- showAddButton: 是否显示新增按钮（仅在type为editable-card时有效）
- showTabBadge: 是否显示标签徽标
- badgeType: 徽标类型（badge/text）
- tabBarStyle: 标签栏样式（JSON字符串）
- operation: 操作按钮配置
  - name: 按钮名称
  - type: 按钮类型（primary/default等）
  - visible: 是否可见
  - events: 按钮事件配置
    - eventName: 事件名称
    - actions: 事件动作列表
      - actionName: 动作名称
      - scripts: 执行脚本
- componentChildren: 标签页配置列表（Tabs.TabPane）
  - key: 标签页唯一标识
  - tab: 标签页标题
  - disabled: 是否禁用
  - lazyLoading: 是否启用懒加载
  - windowId: 关联窗口ID
  - windowComponentName: 关联窗口组件名称
  - componentChildren: 标签页内容组件

### Collapse 折叠面板组件
- componentType: "Collapse"
- bordered: 是否有边框
- defaultActiveKey: 默认展开的面板key数组
- accordion: 是否手风琴模式（同时只能展开一个）
- expandIcon: 自定义展开图标
- componentChildren: 折叠面板配置列表（Collapse.Panel）
  - key: 面板唯一标识
  - header: 面板标题
  - showArrow: 是否显示箭头
  - isShowRequiredMark: 是否显示必填标记
  - paddingTop: 上内边距
  - paddingRight: 右内边距
  - paddingBottom: 下内边距
  - paddingLeft: 左内边距
  - windowId: 关联窗口ID
  - windowComponentName: 关联窗口组件名称
  - operation: 操作按钮配置
    - name: 按钮名称
    - type: 按钮类型
    - visible: 是否可见
    - events: 按钮事件配置
      - eventName: 事件名称
      - actions: 事件动作列表
        - actionName: 动作名称
        - scripts: 执行脚本
  - componentChildren: 面板内容组件


## 数据展示组件

### Table组件Schema
- componentType: "Table"
- columns: 列配置（Column组件）
  - componentType: "Column"
  - fieldId: 字段ID
  - title: 列标题
  - dataIndex: 数据索引
  - width: 列宽度
  - visible: 是否可见
  - canEdit: 是否可编辑
  - sorter: 是否支持排序
  - showFilter: 是否显示筛选
  - bodyTextAlign: 内容对齐方式
  - headTextAlign: 表头对齐方式
  - keepEditing: 是否保持编辑状态
  - visibleLocked: 是否锁定可见状态
  - canEditScripts: 可编辑条件脚本
  - editingComponentType: 编辑时使用的组件类型
  - useMonospacedFont: 是否使用等宽字体
  - events: 列事件配置
    - eventName: 事件名称（onCellClick等）
    - actions: 事件动作
- rowSelection: 行选择配置
  - type: 选择类型（radio/checkbox）
- pagination: 分页配置
  - showSizeChanger: 是否显示页码选择器
  - pageSizeOptions: 页码选项
  - showQuickJumper: 是否允许快速跳转
  - position: 分页位置（top/bottom/both）
  - showTotalInfo: 是否显示总计信息
  - totalTemplate: 总计信息模板
  - float: 分页浮动方向
  - totalAsLastPageSizeOption: 是否将总数作为最后一个页码选项
- stripeRows: 是否显示斑马纹
- showFooter: 是否显示表尾
- operationColumnWidth: 操作列宽度
- showMiniOperator: 是否显示迷你操作器
- miniOperatorType: 迷你操作器类型（icon/text）
- enableResizeColumn: 是否允许调整列宽
- scroll: 滚动设置（x/y）
- size: 表格大小（default/middle/small）
- showFilter: 是否显示筛选
- styleRuleScript: 样式规则脚本
- defaultSort: 默认排序
- bordered: 是否显示边框
- showHeader: 是否显示表头
- showPagination: 是否显示分页
- canDragIn: 是否允许拖入
- canDragOut: 是否允许拖出
- autoUpdateStore: 是否自动更新存储
- operatorColumnFixed: 是否固定操作列
- checkWhenClick: 是否点击时选中
- entityDefId: 实体定义ID
- enableSeqno: 是否启用序号
- bodyTextAlign: 内容对齐方式
- headTextAlign: 表头对齐方式
- isShowToolBar: 是否显示工具栏
- operationTitle: 操作列标题
- showEmptyButton: 是否显示清空按钮
- toolbarDropdown: 工具栏下拉菜单配置
  - name: 按钮名称
  - events: 事件配置
  - visible: 是否可见
- enableDragColumn: 是否允许拖动列
- isSupportSetSize: 是否支持设置大小
- editBodyTextAlign: 编辑时内容对齐方式
- editHeadTextAlign: 编辑时表头对齐方式
- toolbarDefaultBtn: 工具栏默认按钮配置
- toolbarOperations: 工具栏操作按钮配置
  - name: 按钮名称
  - events: 事件配置
  - visible: 是否可见
- viewBodyTextAlign: 查看时内容对齐方式
- viewHeadTextAlign: 查看时表头对齐方式
- isShowToolBarSearch: 是否显示工具栏搜索
- isSupportLockColumns: 是否支持锁定列
- toolbarSmartViewNames: 工具栏智能视图名称
- isShowRowSelectionDropdown: 是否显示行选择下拉菜单
- isSupportSetColumnsVisible: 是否支持设置列可见性
- isShowValueWhenNotMatchedOptions: 未匹配选项时是否显示值
- stopDefaultQueryWhenHandleOnTableChange: 处理表格变更时是否停止默认查询

## 表单组件
### 表单类型组件通用配置项
所有表单类型组件都支持以下基础配置：

- tooltip: 组件提示信息
- editable: 是否可编辑
- required: 是否必填
- readonlyStyle: 只读样式（disable/basedOnForm/label）
- fieldId: 字段ID
- placeholder: 占位文本
- additionalProperties: 额外属性配置
  - label: 标签文本
  - labelCol: 标签栅格宽度
  - wrapperCol: 输入框栅格宽度
  - rules: 验证规则
    - type: 验证类型（required/email/url/phone/idCard/custom）
    - value: 验证值
    - message: 验证失败提示信息

### Form 表单组件
- componentType: "Form"
- layout: 布局模式（horizontal/vertical）
- editable: 是否可编辑
- showColon: 是否显示冒号
- labelTextAlign: 标签对齐方式（left/center/right）
- formItemLabelWidthAdaptation: 标签宽度自适应
- searchForm: 是否为搜索表单
- trimValues: 是否去除输入值的前后空格
- readonlyStyle: 只读样式（disable/basedOnForm/label）
- entityDefId: 实体定义ID
- eagerProperties: 预加载属性配置
- enablePromptOfUnSavedData: 是否启用未保存数据提示
- promptMessageOfUnSavedData: 未保存数据提示消息
- jsonPathName: JSON路径名称
- displayColNum: 每行显示的列数
- componentChildren: 子组件配置列表

### Input 输入框组件
- componentType: "Input"
- type: 输入框类型（text/password/number/textarea）
- allowClear: 是否允许清除
- autoComplete: 是否启用自动完成
- prefix: 输入框前缀
- suffix: 输入框后缀
- addonBefore: 输入框前置标签
- addonAfter: 输入框后置标签
- labelTextAlign: 标签文本对齐方式（basedOnForm）
- maxLength: 最大长度
- customValidateRule: 自定义验证规则
  - validateType: 验证类型（required/email/url/phone/idCard/custom）
  - validateMessage: 验证失败提示信息
  - validateScript: 自定义验证脚本（当validateType为custom时生效）
  - validateTrigger: 验证触发时机（onChange/onBlur）
- operatorForSearchForm: 搜索表单操作符（like/equal等）
- entityDefId: 实体定义ID
- fieldId: 字段ID

### InputNumber 数字输入框组件
- componentType: "InputNumber"
- min: 最小值
- max: 最大值
- precision: 数值精度（小数位数）
- step: 步长
- suffix: 后缀文本
- prefix: 前缀文本

### TextArea 文本域组件
- componentType: "Input.TextArea"
- maxLength: 最大长度
- autosize: 是否自适应高度（true/false）
- maxRows: 最大行数
- minRows: 最小行数

### Select 选择器组件
- componentType: "Select"
- options: 选项数组（包含label、value和可选的children）
- mode: 选择模式（default/multiple/tags）
- showArrow: 是否显示下拉箭头
- allowClear: 是否允许清除
- showSearch: 是否显示搜索框
- defaultValue: 默认值
- valueFieldName: 值字段名
- labelFieldName: 标签字段名
- filterOptionScripts: 选项过滤脚本
- refreshMode: 数据刷新模式（local/remote）
- clearType: 清除行为（clear/keep/filter）
- supportInput: 是否支持输入
- inputOptionPosition: 输入选项位置（first/last）

### TreeSelect 树选择组件
- componentType: "TreeSelect"
- treeData: 树形数据
- multiple: 是否多选
- treeCheckable: 是否显示勾选框
- showSearch: 是否显示搜索框
- formItemName: 表单项名称

### Cascader 级联选择组件
- componentType: "Cascader"
- options: 选项数据
- changeOnSelect: 是否允许选择任意级别
- expandTrigger: 展开触发方式（click/hover）
- formItemName: 表单项名称

### DatePicker/TimePicker 日期时间选择器
- componentType: "DatePicker"/"TimePicker"
- showTime: 是否显示时间选择
- format: 日期格式
- showToday: 是否显示今天按钮
- allowClear: 是否允许清除
- disabledDate: 禁用日期脚本
- disabledTime: 禁用时间脚本

### Radio 单选框组件
- componentType: "Radio"
- options: 选项数组
- buttonStyle: 按钮样式（outline/solid）
- formItemName: 表单项名称

### Checkbox 复选框组件
- componentType: "Checkbox"
- options: 选项数组
- formItemName: 表单项名称

### Switch 开关组件
- componentType: "Switch"
- checkedChildren: 选中时的内容
- unCheckedChildren: 未选中时的内容
- defaultChecked: 默认是否选中
- formItemName: 表单项名称

### Rate 评分组件
- componentType: "Rate"
- count: 总星数
- allowHalf: 是否允许半星
- defaultValue: 默认值
- formItemName: 表单项名称

### ColorPicker 颜色选择器组件
- componentType: "ColorPicker"
- defaultValue: 默认颜色值
- formItemName: 表单项名称

### RichEditor 富文本编辑器组件
- componentType: "RichEditor"
- height: 编辑器高度
- formItemName: 表单项名称

### Upload 上传组件
- componentType: "Upload"
- action: 上传URL
- multiple: 是否支持多选
- accept: 接受的文件类型
- listType: 上传列表样式（text/picture/picture-card）
- maxCount: 最大上传数量
- showUploadList: 是否显示上传列表
- beforeUpload: 上传前脚本
- customRequest: 自定义上传请求
- formItemName: 表单项名称

### TableForm 表单表格组件
- componentType: "TableForm"
- columns: 列配置
- formItemName: 表单项名称

### SubForm 子表单组件
- componentType: "SubForm"
- formItemName: 表单项名称
- refComponentName: 引用组件名称

## 其他组件formData

### 按钮
- componentType: "Button"
- type: 按钮类型（primary/default/dashed/danger/link）
- size: 按钮大小（large/default/small）
- loading: 是否加载中
- disabled: 是否禁用
- openDebounce: 是否开启防抖
- debounceWait: 防抖等待时间(ms)，默认值500
- openThrottle: 是否开启节流
- throttleWaitingTime: 节流等待时间(ms)，默认值500
- showType: 显示类型（operator等）
- scripts: 显示规则脚本
- disabledScripts: 禁用规则脚本
- showConfirmMsg: 是否显示确认消息
- confirmMsgTMP: 确认消息模板
- confirmMsgOkBtnType: 确认消息确定按钮类型

### 开关
- componentType: "Switch"
- checkedChildren: 选中时的内容
- unCheckedChildren: 未选中时的内容
- defaultChecked: 默认是否选中

### 进度条
- componentType: "Progress"
- percent: 百分比
- showInfo: 是否显示信息
- status: 状态（success/exception/active）

### 评分
- componentType: "Rate"
- count: 总星数
- allowHalf: 是否允许半星
- defaultValue: 默认值


